import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.db import transaction
from django.contrib.auth import get_user_model
from django.utils import timezone

from printify.api_client import PrintifyAPIClient
from orders.models import Order, OrderItem
from .models import OrderTracking

User = get_user_model()
logger = logging.getLogger(__name__)

def sync_orders_from_printify(days_back=30):
    """
    Sync orders from Printify to the OrderTracking model.
    
    Args:
        days_back (int): Number of days to look back for orders
        
    Returns:
        dict: A dictionary containing the results of the sync operation
    """
    try:
        # Initialize API client
        client = PrintifyAPIClient()
        shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)
        
        if not shop_id:
            logger.error("No Printify shop ID found in settings")
            return {
                'success': False,
                'message': 'No Printify shop ID found in settings',
                'orders_synced': 0,
                'orders_updated': 0,
                'errors': []
            }
        
        # Get the latest order date from the database
        latest_order = OrderTracking.objects.order_by('-created_at').first()
        
        # If we have orders in the database, only fetch orders newer than the latest one
        # Otherwise, fetch orders from the last 'days_back' days
        if latest_order:
            since_date = latest_order.created_at - timedelta(hours=1)  # Add a buffer to avoid missing orders
        else:
            since_date = timezone.now() - timedelta(days=days_back)
        
        # Format the date for Printify API
        since_date_str = since_date.strftime('%Y-%m-%d')
        
        logger.info(f"Fetching Printify orders since {since_date_str}")
        
        # Fetch orders from Printify
        params = {
            'limit': 100,  # Maximum allowed by Printify API
            'page': 1,
            'since': since_date_str
        }
        
        all_orders = []
        has_more = True
        
        # Paginate through all orders
        while has_more:
            try:
                orders_page = client.get_orders(shop_id, params)
                
                # Handle different response formats
                if isinstance(orders_page, dict) and 'data' in orders_page:
                    orders = orders_page.get('data', [])
                    # Check if there are more pages
                    has_more = orders_page.get('last_page', 1) > params['page']
                else:
                    orders = orders_page
                    has_more = len(orders) == params['limit']
                
                all_orders.extend(orders)
                params['page'] += 1
                
                # Safety check to prevent infinite loops
                if params['page'] > 10:
                    logger.warning("Reached maximum page limit (10), stopping pagination")
                    has_more = False
                    
            except Exception as e:
                logger.error(f"Error fetching orders page {params['page']}: {str(e)}")
                return {
                    'success': False,
                    'message': f"Error fetching orders: {str(e)}",
                    'orders_synced': 0,
                    'orders_updated': 0,
                    'errors': [str(e)]
                }
        
        logger.info(f"Found {len(all_orders)} orders from Printify")
        
        # Process the orders
        orders_synced = 0
        orders_updated = 0
        errors = []
        
        for printify_order in all_orders:
            try:
                with transaction.atomic():
                    # Extract order details
                    printify_order_id = printify_order.get('id')
                    status = printify_order.get('status', 'pending')
                    
                    # Get shipping information
                    shipping_info = printify_order.get('shipments', [])
                    tracking_number = None
                    carrier = None
                    carrier_link = None
                    estimated_delivery = None
                    
                    if shipping_info and len(shipping_info) > 0:
                        # Use the first shipment (most orders will have only one)
                        shipment = shipping_info[0]
                        tracking_number = shipment.get('tracking_number')
                        carrier = shipment.get('carrier')
                        carrier_link = shipment.get('tracking_url')
                        
                        # Extract estimated delivery date if available
                        if 'delivery_date' in shipment:
                            try:
                                estimated_delivery = datetime.strptime(
                                    shipment.get('delivery_date'), 
                                    '%Y-%m-%d'
                                ).date()
                            except (ValueError, TypeError):
                                logger.warning(f"Invalid delivery date format for order {printify_order_id}")
                    
                    # Find the corresponding order in our system
                    # First, try to find by external_id which should match our order ID
                    external_id = printify_order.get('external_id')
                    order = None
                    
                    if external_id:
                        try:
                            # The external_id might be in the format "{order_id}-{item_id}"
                            # Try to extract just the order_id part
                            if '-' in external_id:
                                order_id_part = external_id.split('-')[0]
                                order = Order.objects.filter(id=order_id_part).first()
                            else:
                                order = Order.objects.filter(id=external_id).first()
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid external_id format: {external_id}")
                    
                    # If we couldn't find the order by external_id, try to find by printify_order_id in OrderItems
                    if not order:
                        order_item = OrderItem.objects.filter(printify_order_id=printify_order_id).first()
                        if order_item:
                            order = order_item.order
                    
                    # If we still couldn't find the order, skip this one
                    if not order:
                        logger.warning(f"Could not find order for Printify order {printify_order_id}")
                        errors.append(f"Could not find order for Printify order {printify_order_id}")
                        continue
                    
                    # Check if we already have tracking for this order
                    existing_tracking = OrderTracking.objects.filter(
                        order_id=order.id,
                        printify_order_id=printify_order_id
                    ).first()
                    
                    if existing_tracking:
                        # Update existing tracking
                        existing_tracking.status = status
                        existing_tracking.tracking_number = tracking_number
                        existing_tracking.carrier = carrier
                        existing_tracking.carrier_link = carrier_link
                        existing_tracking.estimated_delivery = estimated_delivery
                        existing_tracking.save()
                        orders_updated += 1
                        logger.info(f"Updated tracking for order {order.id}, Printify order {printify_order_id}")
                    else:
                        # Create new tracking
                        OrderTracking.objects.create(
                            user=order.user,
                            order_id=order.id,
                            printify_order_id=printify_order_id,
                            status=status,
                            tracking_number=tracking_number,
                            carrier=carrier,
                            carrier_link=carrier_link,
                            estimated_delivery=estimated_delivery
                        )
                        orders_synced += 1
                        logger.info(f"Created tracking for order {order.id}, Printify order {printify_order_id}")
                    
                    # Also update the tracking number on the Order model if it's not set
                    if tracking_number and not order.tracking_number:
                        order.tracking_number = tracking_number
                        order.save(update_fields=['tracking_number'])
                        logger.info(f"Updated tracking number on order {order.id}")
                    
            except Exception as e:
                logger.error(f"Error processing Printify order {printify_order.get('id', 'unknown')}: {str(e)}")
                errors.append(f"Error processing Printify order {printify_order.get('id', 'unknown')}: {str(e)}")
        
        return {
            'success': True,
            'message': f"Successfully synced {orders_synced} new orders and updated {orders_updated} existing orders",
            'orders_synced': orders_synced,
            'orders_updated': orders_updated,
            'errors': errors
        }
        
    except Exception as e:
        logger.error(f"Error syncing orders from Printify: {str(e)}")
        return {
            'success': False,
            'message': f"Error syncing orders from Printify: {str(e)}",
            'orders_synced': 0,
            'orders_updated': 0,
            'errors': [str(e)]
        }
