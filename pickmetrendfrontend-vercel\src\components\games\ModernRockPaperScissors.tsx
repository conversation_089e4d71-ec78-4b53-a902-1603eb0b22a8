import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';
import { gameSessionService } from '../../services/gameSessionService';

type Choice = 'rock' | 'paper' | 'scissors';
type RoundResult = 'win' | 'loss' | 'draw';

interface GameState {
  playerChoice: Choice | null;
  aiChoice: Choice | null;
  playerScore: number;
  aiScore: number;
  round: number;
  maxRounds: number;
  gameStatus: 'waiting' | 'playing' | 'round-result' | 'finished';
  sessionId: string | null;
  roundResult: RoundResult | null;
  isResumedGame: boolean;
}

const CHOICES = [
  { name: 'rock', emoji: '🪨', label: 'Rock' },
  { name: 'paper', emoji: '📄', label: 'Paper' },
  { name: 'scissors', emoji: '✂️', label: 'Scissors' }
] as const;

const ModernRockPaperScissors: React.FC = () => {
  const { user } = useAuth();
  const { wallet, refreshWallet } = useWallet();
  
  const [gameState, setGameState] = useState<GameState>({
    playerChoice: null,
    aiChoice: null,
    playerScore: 0,
    aiScore: 0,
    round: 1,
    maxRounds: 3,
    gameStatus: 'waiting',
    sessionId: null,
    roundResult: null,
    isResumedGame: false
  });

  const [showResult, setShowResult] = useState(false);
  const [gameResult, setGameResult] = useState<'win' | 'loss' | 'draw' | 'forfeit' | null>(null);
  const [tokensEarned, setTokensEarned] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);

  // Handle page unload/exit warning
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (gameState.gameStatus === 'playing' && gameState.sessionId) {
        e.preventDefault();
        e.returnValue = 'Exiting now will forfeit your participation tokens!';
        return 'Exiting now will forfeit your participation tokens!';
      }
    };

    const handleUnload = () => {
      if (gameState.gameStatus === 'playing' && gameState.sessionId) {
        // Forfeit the game session on unload
        gameSessionService.forfeitGameSession(gameState.sessionId);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, [gameState.gameStatus, gameState.sessionId]);

  // Start new game
  const startGame = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await gameSessionService.startGameSession('rock_paper_scissors');

      if (result.success) {
        setGameState({
          playerChoice: null,
          aiChoice: null,
          playerScore: 0,
          aiScore: 0,
          round: 1,
          maxRounds: 3,
          gameStatus: 'playing',
          sessionId: result.session_id,
          roundResult: null,
          isResumedGame: result.is_resume
        });

        setShowResult(false);

        // Refresh wallet to show updated balance
        await refreshWallet();

        // Show message about token deduction or resume
        if (result.is_resume) {
          console.log('Resumed game:', result.message);
        } else {
          console.log('New game started:', result.message);
        }
      } else {
        alert(result.error || 'Failed to start game');
      }
    } catch (error) {
      console.error('Error starting game:', error);
      alert('Failed to start game');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle player choice
  const handleChoice = (choice: Choice) => {
    if (gameState.gameStatus !== 'playing') return;

    const aiChoice = getAIChoice();
    const result = determineWinner(choice, aiChoice);
    
    setShowAnimation(true);
    
    setGameState(prev => ({
      ...prev,
      playerChoice: choice,
      aiChoice: aiChoice,
      roundResult: result,
      gameStatus: 'round-result',
      playerScore: prev.playerScore + (result === 'win' ? 1 : 0),
      aiScore: prev.aiScore + (result === 'loss' ? 1 : 0)
    }));

    // Show result for 2 seconds, then next round or finish
    setTimeout(() => {
      setShowAnimation(false);
      
      if (gameState.round >= gameState.maxRounds) {
        finishGame();
      } else {
        setGameState(prev => ({
          ...prev,
          round: prev.round + 1,
          gameStatus: 'playing',
          playerChoice: null,
          aiChoice: null,
          roundResult: null
        }));
      }
    }, 2500);
  };

  // AI choice logic
  const getAIChoice = (): Choice => {
    const choices: Choice[] = ['rock', 'paper', 'scissors'];
    return choices[Math.floor(Math.random() * choices.length)];
  };

  // Determine round winner
  const determineWinner = (playerChoice: Choice, aiChoice: Choice): RoundResult => {
    if (playerChoice === aiChoice) return 'draw';
    
    const winConditions = {
      rock: 'scissors',
      paper: 'rock',
      scissors: 'paper'
    };
    
    return winConditions[playerChoice] === aiChoice ? 'win' : 'loss';
  };

  // Finish game
  const finishGame = async () => {
    if (!gameState.sessionId) return;

    let finalResult: 'win' | 'loss' | 'draw';

    if (gameState.playerScore > gameState.aiScore) {
      finalResult = 'win';
    } else if (gameState.playerScore < gameState.aiScore) {
      finalResult = 'loss';
    } else {
      finalResult = 'draw';
    }

    try {
      // Prepare game data
      const gameData = {
        rounds: gameState.round,
        final_score: {
          player: gameState.playerScore,
          ai: gameState.aiScore
        },
        max_rounds: gameState.maxRounds
      };

      const submitResult = await gameSessionService.completeGameSession(
        gameState.sessionId,
        finalResult,
        gameData
      );

      if (submitResult.success) {
        setTokensEarned(submitResult.tokens_earned);
        setGameResult(finalResult);
        await refreshWallet();

        // Handle draw case
        if (finalResult === 'draw') {
          gameSessionService.handleDrawContinuation(
            () => {
              // Continue playing - start new round with same session
              setGameState(prev => ({
                ...prev,
                playerChoice: null,
                aiChoice: null,
                playerScore: 0,
                aiScore: 0,
                round: 1,
                gameStatus: 'playing',
                roundResult: null
              }));
              setShowResult(false);
              return;
            },
            () => {
              // Forfeit the draw game
              gameSessionService.forfeitGameSession(gameState.sessionId!);
              setShowResult(true);
            }
          );
          return; // Don't show result immediately for draws
        }
      } else {
        console.error('Error completing game:', submitResult.error);
      }
    } catch (error) {
      console.error('Error completing game:', error);
    }

    setGameState(prev => ({ ...prev, gameStatus: 'finished' }));
    setShowResult(true);
  };

  // Forfeit game
  const forfeitGame = () => {
    if (!gameState.sessionId) return;

    gameSessionService.showExitWarning(
      async () => {
        // User confirmed forfeit
        try {
          const result = await gameSessionService.forfeitGameSession(gameState.sessionId!);
          if (result.success) {
            setTokensEarned(result.tokens_earned);
            setGameResult('forfeit');
            await refreshWallet();
            setShowResult(true);
          }
        } catch (error) {
          console.error('Error forfeiting game:', error);
        }
      },
      () => {
        // User cancelled forfeit - continue playing
        console.log('Forfeit cancelled');
      }
    );
  };

  if (showResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h2 className="text-3xl font-bold mb-6">✂️ Game Complete!</h2>
          
          <div className="space-y-4 mb-6">
            <div className="flex justify-between text-lg">
              <span>Your Score:</span>
              <span className="font-bold">{gameState.playerScore}</span>
            </div>
            <div className="flex justify-between text-lg">
              <span>AI Score:</span>
              <span className="font-bold">{gameState.aiScore}</span>
            </div>
          </div>

          <div className="mb-6">
            {gameResult === 'win' && (
              <div className="text-green-400">
                <div className="text-6xl mb-4">🎉</div>
                <div className="text-2xl font-bold">You Won!</div>
                <div className="text-lg">+{tokensEarned} tokens (Net: +{tokensEarned > 0 ? tokensEarned - 2 : tokensEarned} tokens)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens, Win bonus: +5 tokens</div>
              </div>
            )}
            {gameResult === 'loss' && (
              <div className="text-red-400">
                <div className="text-6xl mb-4">😔</div>
                <div className="text-2xl font-bold">You Lost</div>
                <div className="text-lg">{tokensEarned} tokens (Net: {tokensEarned - 2} tokens)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens, Loss penalty: -1 token</div>
              </div>
            )}
            {gameResult === 'draw' && (
              <div className="text-yellow-400">
                <div className="text-6xl mb-4">🤝</div>
                <div className="text-2xl font-bold">Draw!</div>
                <div className="text-lg">Must replay (No token change)</div>
                <div className="text-sm opacity-75 mt-2">Participation tokens held until win/loss</div>
              </div>
            )}
            {gameResult === 'forfeit' && (
              <div className="text-orange-400">
                <div className="text-6xl mb-4">🚪</div>
                <div className="text-2xl font-bold">Game Forfeited</div>
                <div className="text-lg">{tokensEarned} tokens (Participation fee lost)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens</div>
              </div>
            )}
          </div>

          <div className="mb-6">
            <div className="text-sm opacity-75">Current Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Play Again'}
          </button>
        </div>
      </div>
    );
  }

  if (gameState.gameStatus === 'waiting') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h1 className="text-4xl font-bold mb-4">✂️ Rock Paper Scissors</h1>
          <p className="text-lg mb-4 opacity-90">
            Classic battle game! Best of 3 rounds wins. Choose wisely!
          </p>

          <div className="mb-4">
            <div className="px-3 py-2 bg-yellow-50/20 border border-yellow-200/30 rounded-lg inline-block">
              <span className="text-sm font-medium text-yellow-200">🟡 Medium Difficulty</span>
            </div>
          </div>
          
          <div className="mb-6">
            <div className="text-sm opacity-75">Your Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <div className="mb-6 text-sm opacity-75">
            <div>Win: +5 tokens</div>
            <div>Draw: +2 tokens</div>
            <div>Lose: -1 token</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Start Game'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 p-4">
      <div className="max-w-2xl mx-auto">
        {/* Game Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 text-white">
          <div className="flex justify-between items-center mb-4">
            <div className="text-center">
              <div className="text-sm opacity-75">Your Score</div>
              <div className="text-2xl font-bold">{gameState.playerScore}</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold">Round {gameState.round}/{gameState.maxRounds}</div>
              <div className="text-sm opacity-75">Best of {gameState.maxRounds}</div>
            </div>
            <div className="text-center">
              <div className="text-sm opacity-75">AI Score</div>
              <div className="text-2xl font-bold">{gameState.aiScore}</div>
            </div>
          </div>
        </div>

        {/* Battle Display */}
        {gameState.gameStatus === 'round-result' && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-6 text-center text-white">
            <div className="flex justify-center items-center space-x-8 mb-6">
              <div className="text-center">
                <div className="text-6xl mb-2">{CHOICES.find(c => c.name === gameState.playerChoice)?.emoji}</div>
                <div className="text-lg font-bold">You</div>
              </div>
              <div className="text-4xl">VS</div>
              <div className="text-center">
                <div className="text-6xl mb-2">{CHOICES.find(c => c.name === gameState.aiChoice)?.emoji}</div>
                <div className="text-lg font-bold">AI</div>
              </div>
            </div>
            
            <div className="text-2xl font-bold">
              {gameState.roundResult === 'win' && <span className="text-green-400">You Win This Round! 🎉</span>}
              {gameState.roundResult === 'loss' && <span className="text-red-400">AI Wins This Round! 😔</span>}
              {gameState.roundResult === 'draw' && <span className="text-yellow-400">Round Draw! 🤝</span>}
            </div>
          </div>
        )}

        {/* Choice Buttons */}
        {gameState.gameStatus === 'playing' && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-white mb-6">Choose Your Move!</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {CHOICES.map((choice) => (
                <button
                  key={choice.name}
                  onClick={() => handleChoice(choice.name)}
                  className="bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-2xl p-6 text-white hover:bg-white/30 hover:border-white/50 transition-all duration-200 transform hover:scale-105"
                >
                  <div className="text-6xl mb-3">{choice.emoji}</div>
                  <div className="text-xl font-bold">{choice.label}</div>
                </button>
              ))}
            </div>
            
            <div className="mt-6 text-white opacity-75">
              <div className="text-sm">Rock beats Scissors • Paper beats Rock • Scissors beats Paper</div>
            </div>

            {/* Game Controls */}
            <div className="mt-6 flex justify-center space-x-4">
              <button
                onClick={forfeitGame}
                className="px-4 py-2 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-200"
              >
                ⚠️ Forfeit Game
              </button>
            </div>

            {/* Token Information */}
            <div className="mt-4 text-center text-sm opacity-75 text-white">
              <div>Current Balance: {wallet?.balance || 0} tokens</div>
              {gameState.isResumedGame && (
                <div className="text-yellow-300 mt-1">📍 Resumed Game - No additional tokens deducted</div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModernRockPaperScissors;
