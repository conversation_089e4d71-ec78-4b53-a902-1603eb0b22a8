# Generated by Django 5.0.2 on 2025-06-24 14:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gaming', '0003_spinwheelreward_spinwheelsettings_spinwheelhistory_and_more'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='gamesession',
            new_name='gaming_game_user_id_9bd784_idx',
            old_name='gaming_game_user_ga_b8e5a8_idx',
        ),
        migrations.RenameIndex(
            model_name='gamesession',
            new_name='gaming_game_status_fecf59_idx',
            old_name='gaming_game_status_4b8c9a_idx',
        ),
        migrations.RenameIndex(
            model_name='gamesession',
            new_name='gaming_game_created_9979ea_idx',
            old_name='gaming_game_created_2f4a1b_idx',
        ),
        migrations.AlterField(
            model_name='gamesession',
            name='game_type',
            field=models.Char<PERSON>ield(choices=[('tic_tac_toe', 'Tic <PERSON>c <PERSON>'), ('color_match', 'Color Match'), ('memory_card', 'Memory Card Match'), ('number_guessing', 'Number Guessing'), ('rock_paper_scissors', 'Rock Paper Scissors'), ('flappy_bird', 'Flappy Bird')], max_length=20),
        ),
    ]
