import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  photo_url: string | null;
  linkedin_url: string | null;
  order: number;
}

const TeamMembers: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
        const response = await axios.get(`${baseUrl}/team/api/members/`);

        // Check if the response has a results array (paginated response)
        if (response.data && response.data.results && Array.isArray(response.data.results)) {
          setTeamMembers(response.data.results);
        } else if (Array.isArray(response.data)) {
          // If the response is already an array
          setTeamMembers(response.data);
        } else {
          // If we can't determine the format, set an empty array
          console.error('Unexpected API response format:', response.data);
          setTeamMembers([]);
          setError('Unexpected API response format');
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching team members:', err);
        setError('Failed to load team members');
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  if (loading) {
    return (
      <div className="text-center py-4">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading team members...</p>
      </div>
    );
  }

  if (error) {
    // Fallback to hardcoded team members if API fails
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="text-center">
          <div className="w-32 h-32 rounded-full bg-gray-300 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium">Dr. Sujatha</h3>
          <p className="text-gray-600">Founder & CEO</p>
        </div>

        <div className="text-center">
          <div className="w-32 h-32 rounded-full bg-gray-300 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium">Phinihas Dandin</h3>
          <p className="text-gray-600">Chief Product Officer</p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {teamMembers.map((member) => (
        <div key={member.id} className="text-center">
          {member.photo_url ? (
            <img
              src={member.photo_url}
              alt={member.name}
              className="w-32 h-32 rounded-full object-cover mx-auto mb-4"
            />
          ) : (
            <div className="w-32 h-32 rounded-full bg-gray-300 mx-auto mb-4"></div>
          )}
          <h3 className="text-lg font-medium">{member.name}</h3>
          <p className="text-gray-600">{member.role}</p>
          {member.linkedin_url && (
            <a
              href={member.linkedin_url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block mt-2 text-blue-600 hover:text-blue-800"
            >
              LinkedIn Profile
            </a>
          )}
        </div>
      ))}
    </div>
  );
};

export default TeamMembers;
