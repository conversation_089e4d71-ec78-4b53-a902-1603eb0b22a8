"""
Flappy Bird Game API
===================

API endpoints for Flappy Bird game with token-based reward system:
- Deduct 2 tokens to start playing
- Award 5 tokens if player passes at least 10 pipes
- Deduct 1 token if player loses before reaching 5 pipes
- If user exits early, deduct all 2 participation tokens
- Save high score and current score to user's profile
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction
from wallet.models import Wallet
from .game_session_service import GameSessionService
from .models import GameSession, PlayerStats
import json
import uuid


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_flappy_bird_game(request):
    """
    Start a new Flappy Bird game session
    
    POST /api/gaming/flappy-bird/start/
    {
        "difficulty": "easy|medium|hard"  // optional, defaults to medium
    }
    
    Returns:
    {
        "success": true,
        "session_id": "uuid",
        "message": "Game started! 2 tokens deducted for participation.",
        "balance": 198,
        "difficulty": "medium",
        "is_resume": false
    }
    """
    try:
        difficulty = request.data.get('difficulty', 'medium')
        
        # Validate difficulty
        valid_difficulties = ['easy', 'medium', 'hard']
        if difficulty not in valid_difficulties:
            return Response({
                'success': False,
                'error': f'Invalid difficulty. Must be one of: {", ".join(valid_difficulties)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Start game session using the existing service
        result = GameSessionService.start_game_session(request.user, 'flappy_bird', difficulty)
        
        if result['success']:
            return Response(result, status=status.HTTP_201_CREATED)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_flappy_bird_score(request):
    """
    Submit final score and complete Flappy Bird game session
    
    POST /api/gaming/flappy-bird/submit-score/
    {
        "session_id": "uuid",
        "score": 15,
        "pipes_passed": 15,
        "game_duration": 45.5,
        "exit_reason": "collision|ground|exit"  // collision with pipe, hit ground, or early exit
    }
    
    Token Rules:
    - Award 5 tokens if pipes_passed >= 10
    - Deduct 1 additional token if pipes_passed < 5 (total -3 tokens)
    - If early exit, deduct all 2 participation tokens (total -2 tokens)
    - Normal loss (5-9 pipes): only participation fee lost (total -2 tokens)
    """
    try:
        session_id = request.data.get('session_id')
        score = request.data.get('score', 0)
        pipes_passed = request.data.get('pipes_passed', 0)
        game_duration = request.data.get('game_duration', 0)
        exit_reason = request.data.get('exit_reason', 'collision')
        
        if not session_id:
            return Response({
                'success': False,
                'error': 'session_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Enhanced validation with anti-cheat measures
        if not isinstance(score, int) or score < 0:
            return Response({
                'success': False,
                'error': 'Invalid score. Must be a non-negative integer.'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not isinstance(pipes_passed, int) or pipes_passed < 0:
            return Response({
                'success': False,
                'error': 'Invalid pipes_passed. Must be a non-negative integer.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Anti-cheat validation: Score should be reasonable for pipes passed
        # In Flappy Bird, typical scoring is 1 point per pipe, with bonus points
        # Maximum reasonable score is about 15 points per pipe
        max_reasonable_score = pipes_passed * 15
        if score > max_reasonable_score and pipes_passed > 0:
            return Response({
                'success': False,
                'error': 'Score appears to be manipulated. Please play fairly.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Anti-cheat: Game duration should be reasonable
        # Minimum time per pipe is about 2 seconds, maximum is about 10 seconds
        if pipes_passed > 0:
            min_duration = pipes_passed * 1.5  # Very fast but possible
            max_duration = pipes_passed * 15   # Very slow but possible
            if game_duration < min_duration or game_duration > max_duration:
                return Response({
                    'success': False,
                    'error': 'Game duration appears suspicious. Please play normally.'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Anti-cheat: Check for impossible scores
        if score > 1000:  # Extremely high score threshold
            return Response({
                'success': False,
                'error': 'Score too high. Maximum allowed score is 1000.'
            }, status=status.HTTP_400_BAD_REQUEST)

        if pipes_passed > 100:  # Extremely high pipes threshold
            return Response({
                'success': False,
                'error': 'Pipes passed too high. Maximum allowed is 100.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the session
        try:
            session = GameSession.objects.get(id=session_id, user=request.user)
        except GameSession.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Game session not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        if session.status != 'active':
            return Response({
                'success': False,
                'error': 'Game session is not active'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Anti-cheat: Check session timing
        from django.utils import timezone
        session_duration = (timezone.now() - session.started_at).total_seconds()

        # Minimum session time should be at least 3 seconds for any meaningful game
        if session_duration < 3:
            return Response({
                'success': False,
                'error': 'Game session too short. Minimum play time is 3 seconds.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check for rapid successive games (anti-bot measure)
        recent_sessions = GameSession.objects.filter(
            user=request.user,
            game_type='flappy_bird',
            created_at__gte=timezone.now() - timezone.timedelta(minutes=1)
        ).count()

        if recent_sessions > 10:  # More than 10 games in 1 minute is suspicious
            return Response({
                'success': False,
                'error': 'Too many games played recently. Please wait a moment.'
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        
        # Determine game result based on Flappy Bird rules
        result = _determine_flappy_bird_result(pipes_passed, exit_reason)
        
        # Prepare game data
        game_data = {
            'score': score,
            'pipes_passed': pipes_passed,
            'game_duration': game_duration,
            'exit_reason': exit_reason,
            'difficulty': session.game_data.get('difficulty', 'medium')
        }
        
        # Handle special Flappy Bird token logic
        with transaction.atomic():
            if result == 'win':
                # Award 5 tokens for passing 10+ pipes
                session.tokens_awarded = 5
                session.net_token_change = 3  # 5 - 2 (participation)
            elif result == 'loss' and pipes_passed < 5:
                # Deduct additional 1 token for poor performance
                session.tokens_awarded = 0
                session.net_token_change = -3  # -2 (participation) - 1 (penalty)
                
                # Deduct the additional token
                wallet = Wallet.objects.get(user=request.user)
                if wallet.balance >= 1:
                    wallet.spend_tokens(
                        amount=1,
                        transaction_type='game_loss',
                        description=f"Flappy Bird penalty: scored less than 5 pipes"
                    )
            elif exit_reason == 'exit':
                # Early exit - forfeit
                result = 'forfeit'
                session.tokens_awarded = 0
                session.net_token_change = -2  # Only participation fee
            else:
                # Normal loss (5-9 pipes) - only participation fee
                session.tokens_awarded = 0
                session.net_token_change = -2
            
            # Complete the session
            completion_result = GameSessionService.complete_game_session(
                session_id, result, game_data
            )
            
            # Update high score if applicable
            _update_flappy_bird_high_score(request.user, score, pipes_passed)
        
        if completion_result['success']:
            return Response({
                'success': True,
                'result': result,
                'score': score,
                'pipes_passed': pipes_passed,
                'tokens_change': completion_result['tokens_change'],
                'new_balance': completion_result['new_balance'],
                'message': _get_flappy_bird_message(result, pipes_passed, completion_result['tokens_change']),
                'is_high_score': _is_new_high_score(request.user, score)
            }, status=status.HTTP_200_OK)
        else:
            return Response(completion_result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def forfeit_flappy_bird_game(request):
    """
    Forfeit an active Flappy Bird game session
    
    POST /api/gaming/flappy-bird/forfeit/
    {
        "session_id": "uuid"
    }
    """
    try:
        session_id = request.data.get('session_id')
        
        if not session_id:
            return Response({
                'success': False,
                'error': 'session_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Forfeit using the existing service
        result = GameSessionService.forfeit_game_session(session_id)
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_flappy_bird_stats(request):
    """
    Get user's Flappy Bird statistics and high scores
    
    GET /api/gaming/flappy-bird/stats/
    """
    try:
        user = request.user
        
        # Get wallet info
        try:
            wallet = Wallet.objects.get(user=user)
            current_balance = wallet.balance
        except Wallet.DoesNotExist:
            current_balance = 0
        
        # Get Flappy Bird specific stats
        flappy_sessions = GameSession.objects.filter(
            user=user, 
            game_type='flappy_bird',
            status__in=['completed', 'incomplete']
        )
        
        total_games = flappy_sessions.count()
        wins = flappy_sessions.filter(result='win').count()
        losses = flappy_sessions.filter(result='loss').count()
        forfeits = flappy_sessions.filter(result='forfeit').count()
        
        # Calculate high score and best pipes
        high_score = 0
        best_pipes = 0
        total_pipes = 0
        
        for session in flappy_sessions:
            game_data = session.game_data or {}
            score = game_data.get('score', 0)
            pipes = game_data.get('pipes_passed', 0)
            
            if score > high_score:
                high_score = score
            if pipes > best_pipes:
                best_pipes = pipes
            total_pipes += pipes
        
        # Calculate averages
        avg_score = high_score / total_games if total_games > 0 else 0
        avg_pipes = total_pipes / total_games if total_games > 0 else 0
        
        return Response({
            'success': True,
            'stats': {
                'current_balance': current_balance,
                'total_games': total_games,
                'wins': wins,
                'losses': losses,
                'forfeits': forfeits,
                'win_rate': round((wins / total_games * 100), 2) if total_games > 0 else 0,
                'high_score': high_score,
                'best_pipes_passed': best_pipes,
                'average_score': round(avg_score, 1),
                'average_pipes': round(avg_pipes, 1),
                'total_pipes_passed': total_pipes
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _determine_flappy_bird_result(pipes_passed, exit_reason):
    """Determine game result based on Flappy Bird performance"""
    if exit_reason == 'exit':
        return 'forfeit'
    elif pipes_passed >= 10:
        return 'win'
    else:
        return 'loss'


def _get_flappy_bird_message(result, pipes_passed, tokens_change):
    """Get appropriate message for Flappy Bird result"""
    if result == 'win':
        return f"Excellent! You passed {pipes_passed} pipes and earned 5 tokens! Net gain: +{tokens_change} tokens."
    elif result == 'loss' and pipes_passed < 5:
        return f"You passed {pipes_passed} pipes. Additional 1 token penalty for scoring less than 5. Net loss: {tokens_change} tokens."
    elif result == 'forfeit':
        return f"Game forfeited. Participation tokens lost. Net loss: {tokens_change} tokens."
    else:
        return f"You passed {pipes_passed} pipes. Only participation fee lost. Net loss: {tokens_change} tokens."


def _update_flappy_bird_high_score(user, score, pipes_passed):
    """Update user's Flappy Bird high score in game data"""
    try:
        # Store high score in the most recent completed session's game_data
        # This is a simple approach - in production you might want a dedicated high score table
        pass
    except Exception:
        pass


def _is_new_high_score(user, score):
    """Check if this is a new high score for the user"""
    try:
        best_session = GameSession.objects.filter(
            user=user,
            game_type='flappy_bird',
            status__in=['completed', 'incomplete']
        ).order_by('-created_at').first()
        
        if not best_session:
            return True
        
        previous_high = 0
        for session in GameSession.objects.filter(user=user, game_type='flappy_bird'):
            session_score = session.game_data.get('score', 0) if session.game_data else 0
            if session_score > previous_high:
                previous_high = session_score
        
        return score > previous_high
    except Exception:
        return False
