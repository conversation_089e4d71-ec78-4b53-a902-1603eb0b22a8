from django.core.management.base import BaseCommand
from django.conf import settings
from printify.api_client import PrintifyAPIClient
from products.admin import ProductAdmin
from products.models import Product
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Test Printify product sync functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--limit',
            type=int,
            default=5,
            help='Limit number of products to sync (default: 5)'
        )

    def handle(self, *args, **options):
        limit = options['limit']
        
        self.stdout.write('=== Testing Printify Product Sync ===')
        
        try:
            # Test configuration
            self.stdout.write('\n1. Checking Configuration...')
            api_token = getattr(settings, 'PRINTIFY_API_TOKEN', None)
            shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)
            base_url = getattr(settings, 'PRINTIFY_API_BASE_URL', None)
            
            self.stdout.write(f'   API Token: {"✅ Set" if api_token else "❌ Missing"}')
            self.stdout.write(f'   Shop ID: {shop_id if shop_id else "❌ Missing"}')
            self.stdout.write(f'   Base URL: {base_url if base_url else "❌ Missing"}')
            
            if not api_token or not shop_id:
                self.stdout.write(self.style.ERROR('❌ Missing required configuration'))
                return
            
            # Test API client
            self.stdout.write('\n2. Testing API Client...')
            client = PrintifyAPIClient()
            self.stdout.write('   ✅ API Client initialized')
            
            # Test getting shops
            self.stdout.write('\n3. Testing Shop Access...')
            try:
                shops = client.get_shops()
                self.stdout.write(f'   ✅ Retrieved {len(shops) if isinstance(shops, list) else "unknown"} shops')
                
                # Find our shop
                target_shop = None
                if isinstance(shops, list):
                    for shop in shops:
                        if str(shop.get('id')) == str(shop_id):
                            target_shop = shop
                            break
                
                if target_shop:
                    self.stdout.write(f'   ✅ Found target shop: {target_shop.get("title", "Unknown")}')
                else:
                    self.stdout.write(f'   ⚠️ Target shop ID {shop_id} not found in available shops')
                    if isinstance(shops, list):
                        self.stdout.write('   Available shops:')
                        for shop in shops[:3]:
                            self.stdout.write(f'     - {shop.get("title", "Unknown")} (ID: {shop.get("id", "Unknown")})')
                            
            except Exception as e:
                self.stdout.write(f'   ❌ Failed to get shops: {str(e)}')
                return
            
            # Test getting products
            self.stdout.write('\n4. Testing Product Retrieval...')
            try:
                products = client.get_products(shop_id)
                
                if isinstance(products, dict) and 'data' in products:
                    products_list = products['data']
                elif isinstance(products, list):
                    products_list = products
                else:
                    self.stdout.write(f'   ⚠️ Unexpected response format: {type(products)}')
                    return
                
                self.stdout.write(f'   ✅ Retrieved {len(products_list)} products')
                
                if not products_list:
                    self.stdout.write('   ⚠️ No products found in shop')
                    return
                
                # Show first few products
                self.stdout.write('   First few products:')
                for i, product in enumerate(products_list[:3]):
                    title = product.get('title', 'Unknown')
                    product_id = product.get('id', 'Unknown')
                    self.stdout.write(f'     {i+1}. {title} (ID: {product_id})')
                    
            except Exception as e:
                self.stdout.write(f'   ❌ Failed to get products: {str(e)}')
                return
            
            # Test sync functionality
            self.stdout.write(f'\n5. Testing Sync (limit: {limit})...')
            try:
                # Create a ProductAdmin instance to use its sync method
                admin_instance = ProductAdmin(Product, None)
                
                # Run the sync
                synced_count = admin_instance.sync_products_from_printify()
                
                self.stdout.write(f'   ✅ Sync completed successfully!')
                self.stdout.write(f'   📊 Synced {synced_count} products')
                
                # Show current product count
                total_products = Product.objects.count()
                printify_products = Product.objects.filter(printify_id__isnull=False).count()
                
                self.stdout.write(f'\n6. Database Status:')
                self.stdout.write(f'   Total products: {total_products}')
                self.stdout.write(f'   Printify products: {printify_products}')
                
                # Show recent Printify products
                recent_printify = Product.objects.filter(printify_id__isnull=False).order_by('-created_at')[:5]
                if recent_printify:
                    self.stdout.write(f'\n   Recent Printify products:')
                    for product in recent_printify:
                        self.stdout.write(f'     - {product.name} (Printify ID: {product.printify_id})')
                
            except Exception as e:
                self.stdout.write(f'   ❌ Sync failed: {str(e)}')
                import traceback
                self.stdout.write(f'   Traceback: {traceback.format_exc()}')
                return
            
            self.stdout.write(f'\n✅ All tests completed successfully!')
            
        except Exception as e:
            self.stdout.write(f'\n❌ Test failed: {str(e)}')
            import traceback
            self.stdout.write(f'Traceback: {traceback.format_exc()}')
