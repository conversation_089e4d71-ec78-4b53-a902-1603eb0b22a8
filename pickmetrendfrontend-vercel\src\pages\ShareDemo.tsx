import React from 'react';
import ShareProduct from '../components/ui/ShareProduct';

const ShareDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Share Product Feature Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Demonstration of the ShareProduct component with different configurations and use cases.
          </p>
        </div>

        {/* Demo Sections */}
        <div className="space-y-12">
          
          {/* Basic Share Button */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Basic Share Button
            </h2>
            <p className="text-gray-600 mb-6">
              Standard share button with product information
            </p>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50">
              <div className="flex items-center space-x-4">
                <img 
                  src="https://via.placeholder.com/100x100?text=Product" 
                  alt="Demo Product" 
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Premium Cotton T-Shirt
                  </h3>
                  <p className="text-xl font-bold text-primary-600 mt-1">
                    ₹1,299
                  </p>
                </div>
                <ShareProduct
                  productName="Premium Cotton T-Shirt"
                  productUrl="/product/premium-cotton-tshirt"
                  productPrice="₹1,299"
                  productImage="https://via.placeholder.com/400x400?text=Product"
                />
              </div>
            </div>
          </div>

          {/* Product Card Style */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Product Card Integration
            </h2>
            <p className="text-gray-600 mb-6">
              Share button integrated into a product card layout
            </p>
            
            <div className="max-w-sm mx-auto bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
              <img 
                src="https://via.placeholder.com/300x200?text=Product+Image" 
                alt="Demo Product" 
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Wireless Bluetooth Headphones
                </h3>
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-lg font-bold text-primary-600">₹2,499</span>
                  <span className="text-sm text-gray-500 line-through">₹3,499</span>
                </div>
                <div className="flex items-center justify-between mb-4">
                  <span className="text-xs text-green-600">In Stock</span>
                  <ShareProduct
                    productName="Wireless Bluetooth Headphones"
                    productUrl="/product/wireless-bluetooth-headphones"
                    productPrice="₹2,499"
                    productImage="https://via.placeholder.com/400x400?text=Headphones"
                    className="scale-75 origin-right"
                  />
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <button className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md text-sm font-medium">
                    View Details
                  </button>
                  <button className="bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md text-sm font-medium">
                    Add to Cart
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Product Detail Page Style */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Product Detail Page Integration
            </h2>
            <p className="text-gray-600 mb-6">
              Share button as it appears on product detail pages
            </p>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50">
              <div className="max-w-md">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Smart Fitness Watch
                </h1>
                
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  <span className="ml-2 text-sm text-gray-500">24 reviews</span>
                </div>

                <div className="flex items-center mb-6">
                  <p className="text-2xl font-bold text-gray-900">₹8,999</p>
                  <p className="ml-3 text-lg text-gray-500 line-through">₹12,999</p>
                  <div className="ml-3 bg-red-100 text-red-800 text-sm font-medium px-2 py-0.5 rounded">
                    Save 31%
                  </div>
                </div>

                <ShareProduct
                  productName="Smart Fitness Watch"
                  productUrl="/product/smart-fitness-watch"
                  productPrice="₹8,999"
                  productImage="https://via.placeholder.com/400x400?text=Smart+Watch"
                  className="inline-block"
                />
              </div>
            </div>
          </div>

          {/* Features List */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Share Feature Capabilities
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Sharing Platforms
                </h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                      </svg>
                    </div>
                    WhatsApp sharing with custom message
                  </li>
                  <li className="flex items-center">
                    <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </div>
                    Facebook sharing with product URL
                  </li>
                  <li className="flex items-center">
                    <div className="w-6 h-6 bg-sky-500 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                    </div>
                    Twitter sharing with hashtags
                  </li>
                  <li className="flex items-center">
                    <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </div>
                    Copy link to clipboard
                  </li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Technical Features
                </h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Responsive design for all screen sizes</li>
                  <li>• Click outside to close dropdown</li>
                  <li>• Keyboard navigation support</li>
                  <li>• Fallback for older browsers</li>
                  <li>• Custom share text generation</li>
                  <li>• Proper URL encoding</li>
                  <li>• Visual feedback for copy action</li>
                  <li>• Customizable styling</li>
                </ul>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default ShareDemo;
