{"name": "dropshipping-frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "axios": "^1.8.4", "react": "^19.1.0", "react-confetti": "^6.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "predeploy": "npm run build", "deploy": "gh-pages -d build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "gh-pages": "^6.3.0", "html-webpack-plugin": "^5.6.3", "postcss": "^8.4.31", "serve": "^14.2.4", "tailwindcss": "^3.3.0"}}