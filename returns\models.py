from django.db import models
from django.contrib.auth.models import User
from orders.models import Order
import uuid


class ReturnRequest(models.Model):
    """
    Model for handling return requests from customers
    """
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
    )

    REASON_CHOICES = (
        ('defective', 'Defective Product'),
        ('wrong_item', 'Wrong Item Received'),
        ('not_as_described', 'Not as Described'),
        ('damaged_shipping', 'Damaged During Shipping'),
        ('size_issue', 'Size Issue'),
        ('quality_issue', 'Quality Issue'),
        ('changed_mind', 'Changed Mind'),
        ('other', 'Other'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='return_requests')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='return_requests')

    # Return details
    reason = models.CharField(max_length=50, choices=REASON_CHOICES)
    reason_detail = models.TextField(help_text="Detailed explanation of the return reason")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Admin response
    admin_notes = models.TextField(blank=True, null=True, help_text="Internal notes for admin use")
    admin_response = models.TextField(blank=True, null=True, help_text="Response message to customer")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(blank=True, null=True, help_text="When the return was processed")

    # Additional fields
    refund_amount = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    tracking_number = models.CharField(max_length=100, blank=True, null=True, help_text="Return shipping tracking number")

    class Meta:
        ordering = ['-created_at']
        # Prevent multiple return requests for the same order
        unique_together = ['order', 'user']

    def __str__(self):
        return f"Return Request {self.id} for Order {self.order.id}"

    @property
    def can_be_returned(self):
        """Check if the order can be returned (must be delivered)"""
        return self.order.status == 'delivered'

    @property
    def is_pending(self):
        """Check if return request is pending"""
        return self.status == 'pending'

    @property
    def is_approved(self):
        """Check if return request is approved"""
        return self.status == 'approved'

    @property
    def is_rejected(self):
        """Check if return request is rejected"""
        return self.status == 'rejected'

    def approve(self, admin_response=None, refund_amount=None):
        """Approve the return request"""
        self.status = 'approved'
        if admin_response:
            self.admin_response = admin_response
        if refund_amount:
            self.refund_amount = refund_amount
        self.save()

    def reject(self, admin_response=None):
        """Reject the return request"""
        self.status = 'rejected'
        if admin_response:
            self.admin_response = admin_response
        self.save()

    def complete(self, tracking_number=None):
        """Mark return as completed"""
        self.status = 'completed'
        if tracking_number:
            self.tracking_number = tracking_number
        from django.utils import timezone
        self.processed_at = timezone.now()
        self.save()
