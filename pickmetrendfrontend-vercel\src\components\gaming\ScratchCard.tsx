import React, { useState, useRef, useEffect } from 'react';
import { spinWheelService, ScratchCardReveal } from '../../services/spinWheelService';

interface ScratchCardProps {
  spinId: string;
  scratchCardId: string;
  onComplete: (reward: any) => void;
  onClose: () => void;
}

const ScratchCard: React.FC<ScratchCardProps> = ({ 
  spinId, 
  scratchCardId, 
  onComplete, 
  onClose 
}) => {
  const [isScratching, setIsScratching] = useState(false);
  const [scratchedAreas, setScratchedAreas] = useState<Set<number>>(new Set());
  const [isRevealed, setIsRevealed] = useState(false);
  const [reward, setReward] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const isMouseDown = useRef(false);

  useEffect(() => {
    initializeCanvas();
  }, []);

  const initializeCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = 400;
    canvas.height = 300;

    // Create scratch-off surface
    ctx.fillStyle = '#C0C0C0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add scratch-off pattern
    ctx.fillStyle = '#A0A0A0';
    for (let i = 0; i < canvas.width; i += 20) {
      for (let j = 0; j < canvas.height; j += 20) {
        if ((i + j) % 40 === 0) {
          ctx.fillRect(i, j, 10, 10);
        }
      }
    }

    // Add text overlay
    ctx.fillStyle = '#808080';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Scratch to reveal!', canvas.width / 2, canvas.height / 2);
  };

  const startScratching = (e: React.MouseEvent<HTMLCanvasElement>) => {
    isMouseDown.current = true;
    setIsScratching(true);
    scratch(e);
  };

  const scratch = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isMouseDown.current) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Create scratch effect
    ctx.globalCompositeOperation = 'destination-out';
    ctx.beginPath();
    ctx.arc(x, y, 20, 0, 2 * Math.PI);
    ctx.fill();

    // Check if enough area is scratched
    checkScratchProgress();
  };

  const stopScratching = () => {
    isMouseDown.current = false;
  };

  const checkScratchProgress = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Get image data to check transparency
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    
    let transparentPixels = 0;
    for (let i = 3; i < pixels.length; i += 4) {
      if (pixels[i] < 128) { // Alpha channel
        transparentPixels++;
      }
    }

    const scratchedPercentage = transparentPixels / (canvas.width * canvas.height);
    
    // If 30% or more is scratched, reveal the card
    if (scratchedPercentage > 0.3 && !isRevealed) {
      revealCard();
    }
  };

  const revealCard = async () => {
    if (isRevealed || loading) return;

    try {
      setLoading(true);
      setIsRevealed(true);
      
      const result = await spinWheelService.revealScratchCard(spinId);
      
      if (result.success) {
        setReward(result.reward_result);
        
        // Clear the canvas to show the reward
        const canvas = canvasRef.current;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
          }
        }
        
        // Auto-close after showing reward
        setTimeout(() => {
          onComplete(result.reward_result);
        }, 3000);
      } else {
        setError(result.error || 'Failed to reveal scratch card');
      }
    } catch (err) {
      setError('Failed to reveal scratch card');
    } finally {
      setLoading(false);
    }
  };

  const handleSkipScratching = () => {
    revealCard();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4">
        {/* Header */}
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-2">
            🎫 Scratch Card
          </h3>
          <p className="text-gray-600">
            Scratch to reveal your hidden prize!
          </p>
        </div>

        {/* Scratch Card */}
        <div className="relative mb-6">
          <div className="bg-gradient-to-br from-yellow-100 to-orange-100 rounded-lg p-6 border-2 border-dashed border-yellow-300">
            {!isRevealed ? (
              <canvas
                ref={canvasRef}
                className="w-full h-48 cursor-pointer rounded-lg"
                onMouseDown={startScratching}
                onMouseMove={scratch}
                onMouseUp={stopScratching}
                onMouseLeave={stopScratching}
                style={{ touchAction: 'none' }}
              />
            ) : (
              <div className="w-full h-48 flex items-center justify-center">
                {loading ? (
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
                    <p className="text-gray-600">Revealing your prize...</p>
                  </div>
                ) : reward ? (
                  <div className="text-center">
                    <div className="text-6xl mb-4">
                      {reward.reward_type === 'tokens' ? '🪙' : '🎁'}
                    </div>
                    <div className="text-2xl font-bold text-green-600 mb-2">
                      Congratulations!
                    </div>
                    <div className="text-lg text-gray-800">
                      {reward.message}
                    </div>
                    {reward.reward_type === 'tokens' && (
                      <div className="text-3xl font-bold text-yellow-600 mt-2">
                        +{reward.amount} Tokens
                      </div>
                    )}
                  </div>
                ) : error ? (
                  <div className="text-center text-red-600">
                    <div className="text-4xl mb-2">❌</div>
                    <div>{error}</div>
                  </div>
                ) : null}
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        {!isRevealed && !loading && (
          <div className="text-center mb-6">
            <p className="text-sm text-gray-500 mb-4">
              Click and drag to scratch off the surface
            </p>
            <button
              onClick={handleSkipScratching}
              className="text-blue-500 hover:text-blue-700 text-sm underline"
            >
              Skip scratching and reveal now
            </button>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4">
          {isRevealed && reward && (
            <button
              onClick={() => onComplete(reward)}
              className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-semibold"
            >
              Collect Reward
            </button>
          )}
          
          <button
            onClick={onClose}
            className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            {isRevealed ? 'Close' : 'Cancel'}
          </button>
        </div>

        {/* Progress Indicator */}
        {isScratching && !isRevealed && (
          <div className="mt-4 text-center">
            <div className="text-sm text-gray-600">
              Keep scratching to reveal your prize!
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                style={{ width: '30%' }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ScratchCard;
