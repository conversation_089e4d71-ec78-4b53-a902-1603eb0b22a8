# 🪙 Token Purchase System - PickMeTrend

## Overview

Complete token purchase system with Razorpay integration that allows users to buy tokens using real money when their balance is low.

## 🎯 Features

### ✅ **Token Packs**
- **Starter Pack**: 100 tokens for ₹10
- **Popular Pack**: 500 tokens for ₹45 (marked as popular)
- **Best Value Pack**: 1000 tokens for ₹80 (best savings)

### ✅ **Smart Purchase Flow**
1. **Low Balance Detection**: Automatic alerts when tokens ≤ 10
2. **Pack Selection**: Choose from predefined token packs
3. **Secure Payment**: Razorpay integration with signature verification
4. **Instant Credit**: Tokens credited immediately after successful payment
5. **Transaction History**: Complete audit trail of all purchases

### ✅ **User Experience**
- **Modal Interface**: Clean, responsive purchase modal
- **Purchase Buttons**: Available throughout the app
- **Low Balance Alerts**: Contextual alerts when tokens are low
- **Success Feedback**: Clear confirmation of successful purchases

## 🏗️ Architecture

### **Backend Components**

#### **Models** (`wallet/models.py`)
```python
# Token Packs - Predefined packages
class TokenPack:
    - name: Pack name (e.g., "Starter Pack")
    - tokens: Number of tokens
    - price_inr: Price in Indian Rupees
    - is_active: Whether pack is available
    - sort_order: Display order

# Token Purchases - Purchase tracking
class TokenPurchase:
    - user: User who made the purchase
    - token_pack: Selected token pack
    - tokens_purchased: Number of tokens bought
    - amount_paid: Amount paid in INR
    - payment_status: pending/completed/failed/etc.
    - razorpay_order_id: Razorpay order ID
    - razorpay_payment_id: Razorpay payment ID
    - razorpay_signature: Payment signature for verification
```

#### **API Endpoints** (`wallet/token_purchase_views.py`)
```
GET  /api/wallet/token-packs/           - Get available token packs
POST /api/wallet/create-token-order/    - Create Razorpay order
POST /api/wallet/verify-token-payment/  - Verify payment & credit tokens
GET  /api/wallet/purchase-history/      - Get user's purchase history
```

#### **Admin Interface** (`wallet/admin.py`)
- **TokenPackAdmin**: Manage token packs (create, edit, activate/deactivate)
- **TokenPurchaseAdmin**: View purchase history (read-only for security)

### **Frontend Components**

#### **Core Components**
```typescript
// Main purchase modal
TokenPurchaseModal.tsx
- Pack selection interface
- Payment processing
- Razorpay integration

// Purchase button (reusable)
TokenPurchaseButton.tsx
- Configurable variants (primary/secondary/outline)
- Different sizes (sm/md/lg)
- Opens purchase modal

// Low balance alert
LowBalanceAlert.tsx
- Automatic detection of low balance
- Contextual purchase prompts
- Dismissible alerts
```

#### **Service Layer**
```typescript
// API service
tokenPurchaseService.ts
- getTokenPacks()
- createOrder()
- verifyPayment()
- getPurchaseHistory()
```

## 🚀 Setup Instructions

### **1. Backend Setup**

#### **Run Migrations**
```bash
python manage.py makemigrations wallet
python manage.py migrate
```

#### **Create Default Token Packs**
```bash
python manage.py create_token_packs
```

#### **Configure Razorpay**
Ensure these settings are in your environment:
```python
# settings.py or environment variables
RAZORPAY_KEY_ID = "rzp_test_xxx"  # or rzp_live_xxx for production
RAZORPAY_KEY_SECRET = "your_secret_key"
```

### **2. Frontend Setup**

#### **Environment Variables**
```bash
# .env or .env.production
REACT_APP_RAZORPAY_KEY_ID=rzp_test_xxx  # or rzp_live_xxx
```

#### **Install Dependencies**
The system uses existing dependencies:
- Razorpay Checkout (loaded dynamically)
- Existing API service
- React hooks and components

## 💡 Usage Examples

### **1. Add Purchase Button to Any Component**
```tsx
import TokenPurchaseButton from '../components/wallet/TokenPurchaseButton';

function MyComponent() {
  const { wallet, refreshWallet } = useWallet();
  
  return (
    <TokenPurchaseButton
      currentBalance={wallet?.balance || 0}
      onTokensAdded={(tokens) => {
        refreshWallet();
        console.log(`Added ${tokens} tokens!`);
      }}
      variant="primary"
      size="md"
    >
      Buy Tokens
    </TokenPurchaseButton>
  );
}
```

### **2. Add Low Balance Alert**
```tsx
import LowBalanceAlert from '../components/wallet/LowBalanceAlert';

function GamePage() {
  const { wallet, refreshWallet } = useWallet();
  
  return (
    <div>
      <LowBalanceAlert
        balance={wallet?.balance || 0}
        onTokensAdded={refreshWallet}
        threshold={5}  // Show when ≤ 5 tokens
      />
      {/* Your game content */}
    </div>
  );
}
```

### **3. Enhanced Wallet Balance Display**
```tsx
import WalletBalance from '../components/wallet/WalletBalance';

function Header() {
  return (
    <WalletBalance
      showDetails={true}
      showBuyButton={true}
      lowBalanceThreshold={10}
    />
  );
}
```

## 🔒 Security Features

### **Payment Verification**
- **Signature Verification**: All payments verified using Razorpay signature
- **Order Validation**: Orders validated before processing
- **Atomic Transactions**: Database operations are atomic
- **Audit Trail**: Complete transaction history maintained

### **Admin Controls**
- **Read-Only Purchases**: Admins cannot manually create/delete purchases
- **Pack Management**: Admins can activate/deactivate token packs
- **Transaction Monitoring**: Full visibility into all purchases

## 📊 Token Economy Integration

### **Transaction Types**
The system adds a new transaction type:
```python
('token_purchase', 'Token Purchase')
```

### **Wallet Integration**
- Purchases automatically credit user's wallet
- Transaction history includes purchase records
- Balance updates are immediate and atomic

### **Game Integration**
- Low balance alerts appear in games
- Purchase buttons available in game interfaces
- Seamless flow from "insufficient tokens" to purchase

## 🎨 UI/UX Features

### **Responsive Design**
- Mobile-friendly purchase modal
- Adaptive button sizes and layouts
- Touch-friendly interface

### **Visual Feedback**
- Loading states during payment processing
- Success/error messages
- Progress indicators
- Clear pricing display

### **Accessibility**
- Keyboard navigation support
- Screen reader friendly
- High contrast colors
- Clear focus indicators

## 🧪 Testing

### **Test Payment Flow**
1. Use Razorpay test credentials
2. Test cards: 4111 1111 1111 1111
3. Any future expiry date
4. Any 3-digit CVV

### **Test Scenarios**
- ✅ Successful payment flow
- ✅ Payment cancellation
- ✅ Payment failure handling
- ✅ Network error handling
- ✅ Low balance detection
- ✅ Token crediting
- ✅ Transaction recording

## 📈 Analytics & Monitoring

### **Key Metrics**
- Purchase conversion rates
- Popular token pack preferences
- Payment success/failure rates
- User token spending patterns

### **Admin Dashboard**
- Purchase volume tracking
- Revenue analytics
- User engagement metrics
- Payment method preferences

## 🔄 Future Enhancements

### **Planned Features**
- [ ] Bulk purchase discounts
- [ ] Seasonal promotional packs
- [ ] Gift token functionality
- [ ] Subscription-based token plans
- [ ] Loyalty rewards for frequent buyers

### **Integration Opportunities**
- [ ] Email notifications for purchases
- [ ] SMS confirmations
- [ ] Social sharing of achievements
- [ ] Referral bonuses for token purchases

---

## 🎉 **System Status: READY FOR DEPLOYMENT**

The token purchase system is fully implemented and ready for use. Users can now:
- ✅ Buy tokens when balance is low
- ✅ Choose from multiple token packs
- ✅ Pay securely with Razorpay
- ✅ Get instant token credits
- ✅ View purchase history
- ✅ Enjoy seamless gaming experience

**Next Step**: Deploy and test with real users! 🚀
