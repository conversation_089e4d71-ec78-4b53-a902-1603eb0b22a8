"""
Ludo Game API
=============

Custom 5-minute timer-based Ludo game with token integration.
Supports 1v1 mode (User vs Bot or User vs User).
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from wallet.models import Wallet
from .game_session_service import GameSessionService
from .ai_bot import get_ai_bot
import uuid
import json
import random
from datetime import datetime, timedelta
from django.utils import timezone


class LudoGame:
    """
    Custom Ludo Game Logic for 5-minute timer-based gameplay
    """
    
    def __init__(self, num_tokens=4):
        self.num_tokens = num_tokens  # 2 or 4 tokens per player
        self.board_size = 52  # Standard Ludo board positions
        self.home_positions = {
            'player1': list(range(1, num_tokens + 1)),  # Starting positions
            'player2': list(range(27, 27 + num_tokens))  # Bot/Player2 starting positions
        }
        self.safe_positions = [1, 9, 14, 22, 27, 35, 40, 48]  # Safe spots on board
        
    def get_initial_state(self):
        """Initialize a new Ludo game state"""
        return {
            'board': self.create_board(),
            'current_player': 'player1',
            'dice_value': 0,
            'game_timer': 300,  # 5 minutes in seconds
            'start_time': timezone.now().isoformat(),
            'player1_moves': 0,
            'player2_moves': 0,
            'player1_tokens': self.get_initial_tokens('player1'),
            'player2_tokens': self.get_initial_tokens('player2'),
            'status': 'active',
            'winner': None,
            'last_move': None
        }
    
    def create_board(self):
        """Create the Ludo board representation"""
        board = {}
        for i in range(self.board_size):
            board[str(i)] = None
        return board
    
    def get_initial_tokens(self, player):
        """Get initial token positions for a player"""
        tokens = {}
        start_pos = 0 if player == 'player1' else 26
        
        for i in range(self.num_tokens):
            tokens[f'token_{i}'] = {
                'position': -1,  # -1 means in home
                'is_home': True,
                'is_finished': False
            }
        return tokens
    
    def roll_dice(self):
        """Roll a dice (1-6)"""
        return random.randint(1, 6)
    
    def get_valid_moves(self, game_state, player):
        """Get all valid moves for a player"""
        dice_value = game_state['dice_value']
        player_tokens = game_state[f'{player}_tokens']
        valid_moves = []
        
        for token_id, token_data in player_tokens.items():
            if token_data['is_finished']:
                continue
                
            if token_data['is_home']:
                # Can only move out of home with 6
                if dice_value == 6:
                    start_pos = 0 if player == 'player1' else 26
                    valid_moves.append({
                        'token': token_id,
                        'from': -1,
                        'to': start_pos,
                        'type': 'exit_home'
                    })
            else:
                # Normal move
                current_pos = token_data['position']
                new_pos = (current_pos + dice_value) % self.board_size
                
                # Check if move is valid (not blocked by own token)
                if not self.is_position_blocked(game_state, new_pos, player):
                    valid_moves.append({
                        'token': token_id,
                        'from': current_pos,
                        'to': new_pos,
                        'type': 'normal'
                    })
        
        return valid_moves
    
    def is_position_blocked(self, game_state, position, player):
        """Check if a position is blocked by player's own token"""
        player_tokens = game_state[f'{player}_tokens']
        
        for token_data in player_tokens.values():
            if not token_data['is_home'] and not token_data['is_finished']:
                if token_data['position'] == position:
                    return True
        return False
    
    def make_move(self, game_state, player, move):
        """Execute a move and update game state"""
        token_id = move['token']
        new_position = move['to']
        
        # Update token position
        game_state[f'{player}_tokens'][token_id]['position'] = new_position
        game_state[f'{player}_tokens'][token_id]['is_home'] = False
        
        # Increment move count
        game_state[f'{player}_moves'] += 1
        
        # Check for captures
        opponent = 'player2' if player == 'player1' else 'player1'
        self.check_captures(game_state, new_position, opponent)
        
        # Update last move
        game_state['last_move'] = {
            'player': player,
            'token': token_id,
            'from': move['from'],
            'to': new_position,
            'timestamp': timezone.now().isoformat()
        }
        
        # Switch turns (unless rolled 6)
        if game_state['dice_value'] != 6:
            game_state['current_player'] = opponent
        
        return game_state
    
    def check_captures(self, game_state, position, opponent):
        """Check if any opponent tokens are captured"""
        if position in self.safe_positions:
            return  # Can't capture on safe positions
        
        opponent_tokens = game_state[f'{opponent}_tokens']
        for token_id, token_data in opponent_tokens.items():
            if (not token_data['is_home'] and 
                not token_data['is_finished'] and 
                token_data['position'] == position):
                # Send opponent token back home
                token_data['position'] = -1
                token_data['is_home'] = True
    
    def check_game_end(self, game_state):
        """Check if game should end (5 minutes up)"""
        start_time = datetime.fromisoformat(game_state['start_time'].replace('Z', '+00:00'))
        current_time = timezone.now()
        elapsed = (current_time - start_time).total_seconds()
        
        if elapsed >= 300:  # 5 minutes
            # Determine winner based on move count
            p1_moves = game_state['player1_moves']
            p2_moves = game_state['player2_moves']
            
            if p1_moves > p2_moves:
                game_state['winner'] = 'player1'
                game_state['status'] = 'completed'
            elif p2_moves > p1_moves:
                game_state['winner'] = 'player2'
                game_state['status'] = 'completed'
            else:
                game_state['winner'] = 'draw'
                game_state['status'] = 'draw'
            
            return True
        
        return False


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_ludo_game(request):
    """
    Start a new Ludo game and deduct 2 tokens for participation
    """
    try:
        user = request.user
        difficulty = request.data.get('difficulty', 'medium')
        num_tokens = request.data.get('num_tokens', 4)  # 2 or 4 tokens
        game_mode = request.data.get('mode', 'vs_bot')  # vs_bot or vs_user
        
        # Validate num_tokens
        if num_tokens not in [2, 4]:
            return Response({
                'error': 'Invalid number of tokens. Must be 2 or 4.',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Start game session using the comprehensive service
        result = GameSessionService.start_game_session(user, 'ludo', difficulty)
        
        if result['success']:
            # Initialize Ludo game
            ludo_game = LudoGame(num_tokens)
            initial_state = ludo_game.get_initial_state()
            
            # Add game-specific data
            initial_state.update({
                'difficulty': difficulty,
                'num_tokens': num_tokens,
                'game_mode': game_mode,
                'session_id': result['session_id']
            })
            
            return Response({
                'success': True,
                'game_id': result['session_id'],
                'game_state': initial_state,
                'difficulty': difficulty,
                'num_tokens': num_tokens,
                'game_mode': game_mode,
                'can_play': True,
                'balance': result['balance'],
                'message': result['message'],
                'is_resume': result.get('is_resume', False)
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'success': False,
                'error': result['error'],
                'can_play': False,
                'balance': result.get('balance', 0)
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def roll_dice_ludo(request):
    """
    Roll dice for Ludo game
    """
    try:
        game_id = request.data.get('game_id')
        
        if not game_id:
            return Response({
                'error': 'Missing game_id',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get game session
        session_result = GameSessionService.get_session_status(game_id)
        if not session_result['success']:
            return Response({
                'error': 'Game session not found',
                'success': False
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Roll dice
        ludo_game = LudoGame()
        dice_value = ludo_game.roll_dice()
        
        return Response({
            'success': True,
            'dice_value': dice_value,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def make_move_ludo(request):
    """
    Make a move in Ludo game
    """
    try:
        game_id = request.data.get('game_id')
        move_data = request.data.get('move')
        game_state = request.data.get('game_state')

        if not all([game_id, move_data, game_state]):
            return Response({
                'error': 'Missing required data: game_id, move, game_state',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Initialize Ludo game
        ludo_game = LudoGame(game_state.get('num_tokens', 4))

        # Make the move
        updated_state = ludo_game.make_move(game_state, 'player1', move_data)

        # Check if game should end
        game_ended = ludo_game.check_game_end(updated_state)

        return Response({
            'success': True,
            'game_state': updated_state,
            'game_ended': game_ended,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_valid_moves_ludo(request):
    """
    Get valid moves for current player
    """
    try:
        game_state = request.data.get('game_state')
        player = request.data.get('player', 'player1')

        if not game_state:
            return Response({
                'error': 'Missing game_state',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Initialize Ludo game
        ludo_game = LudoGame(game_state.get('num_tokens', 4))

        # Get valid moves
        valid_moves = ludo_game.get_valid_moves(game_state, player)

        return Response({
            'success': True,
            'valid_moves': valid_moves,
            'player': player
        })

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def bot_move_ludo(request):
    """
    Get bot move for Ludo game
    """
    try:
        game_state = request.data.get('game_state')
        difficulty = request.data.get('difficulty', 'medium')

        if not game_state:
            return Response({
                'error': 'Missing game_state',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Initialize Ludo game and AI bot
        ludo_game = LudoGame(game_state.get('num_tokens', 4))
        ai_bot = get_ai_bot(difficulty)

        # Get valid moves for bot
        valid_moves = ludo_game.get_valid_moves(game_state, 'player2')

        if not valid_moves:
            return Response({
                'success': True,
                'bot_move': None,
                'message': 'No valid moves available for bot'
            })

        # AI bot selects move based on difficulty
        if difficulty == 'easy':
            # Random move selection
            selected_move = random.choice(valid_moves)
        elif difficulty == 'medium':
            # Prefer moves that advance tokens or capture
            selected_move = ai_bot._select_ludo_move(valid_moves, game_state, 'medium')
        else:  # hard
            # Strategic move selection
            selected_move = ai_bot._select_ludo_move(valid_moves, game_state, 'hard')

        # Execute bot move
        updated_state = ludo_game.make_move(game_state, 'player2', selected_move)

        return Response({
            'success': True,
            'bot_move': selected_move,
            'game_state': updated_state,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_ludo_game(request):
    """
    Complete a Ludo game and handle token transactions
    """
    try:
        user = request.user
        game_id = request.data.get('game_id')
        game_result = request.data.get('result')  # 'win', 'loss', 'draw'
        game_state = request.data.get('game_state')

        if not all([game_id, game_result, game_state]):
            return Response({
                'error': 'Missing required data: game_id, result, game_state',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        if game_result not in ['win', 'loss', 'draw']:
            return Response({
                'error': 'Invalid game result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Prepare game data for session completion
        game_data = {
            'difficulty': game_state.get('difficulty', 'medium'),
            'num_tokens': game_state.get('num_tokens', 4),
            'game_mode': game_state.get('game_mode', 'vs_bot'),
            'player1_moves': game_state.get('player1_moves', 0),
            'player2_moves': game_state.get('player2_moves', 0),
            'game_duration': 300,  # 5 minutes
            'final_state': game_state
        }

        # Complete game session
        result = GameSessionService.complete_game_session(game_id, game_result, game_data)

        if result['success']:
            return Response({
                'success': True,
                'tokens_earned': result.get('tokens_change', 0),
                'new_balance': result.get('new_balance', 0),
                'message': result.get('message', ''),
                'game_result': game_result,
                'player1_moves': game_state.get('player1_moves', 0),
                'player2_moves': game_state.get('player2_moves', 0)
            })
        else:
            return Response({
                'error': result.get('error', 'Failed to complete game'),
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def forfeit_ludo_game(request):
    """
    Forfeit a Ludo game
    """
    try:
        game_id = request.data.get('game_id')

        if not game_id:
            return Response({
                'error': 'Missing game_id',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Forfeit game session
        result = GameSessionService.forfeit_game_session(game_id)

        return Response(result)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
