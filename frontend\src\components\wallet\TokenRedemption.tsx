import React, { useState, useEffect } from 'react';
import { useWallet } from '../../hooks/useWallet';
import { formatINR } from '../../utils/currencyFormatter';

interface TokenRedemptionProps {
  orderAmount: number;
  onTokensSelected: (tokens: number, inrValue: number) => void;
  className?: string;
}

const TokenRedemption: React.FC<TokenRedemptionProps> = ({
  orderAmount,
  onTokensSelected,
  className = ''
}) => {
  const { wallet, calculateTokenRedemption } = useWallet();
  const [redemptionData, setRedemptionData] = useState<any>(null);
  const [selectedTokens, setSelectedTokens] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (orderAmount > 0) {
      fetchRedemptionData();
    }
  }, [orderAmount]);

  const fetchRedemptionData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await calculateTokenRedemption(orderAmount);
      if (data) {
        setRedemptionData(data);
        // Reset selected tokens when redemption data changes
        setSelectedTokens(0);
        onTokensSelected(0, 0);
      }
    } catch (err) {
      setError('Failed to calculate token redemption');
    } finally {
      setLoading(false);
    }
  };

  const handleTokenChange = (tokens: number) => {
    if (!redemptionData) return;
    
    const maxTokens = Math.min(redemptionData.redeemable_tokens, redemptionData.available_tokens);
    const validTokens = Math.max(0, Math.min(tokens, maxTokens));
    
    setSelectedTokens(validTokens);
    
    // Calculate INR value
    const inrValue = validTokens * redemptionData.token_rate;
    onTokensSelected(validTokens, inrValue);
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
        <div className="h-8 bg-gray-200 rounded"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-red-500 text-sm ${className}`}>
        {error}
      </div>
    );
  }

  if (!wallet || !redemptionData) {
    return null;
  }

  const canRedeem = redemptionData.available_tokens >= redemptionData.min_redemption_tokens;
  const maxRedeemableTokens = Math.min(redemptionData.redeemable_tokens, redemptionData.available_tokens);

  return (
    <div className={`border rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-lg">🪙 Use Tokens</h3>
        <div className="text-sm text-gray-600">
          Available: {redemptionData.available_tokens} tokens
        </div>
      </div>

      {!canRedeem ? (
        <div className="text-gray-500 text-sm">
          <p>Minimum {redemptionData.min_redemption_tokens} tokens required for redemption.</p>
          <p>You have {redemptionData.available_tokens} tokens.</p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="text-sm text-gray-600">
            <p>• Maximum {redemptionData.max_redemption_percentage}% of order can be paid with tokens</p>
            <p>• 1 token = {formatINR(redemptionData.token_rate)}</p>
            <p>• You can redeem up to {maxRedeemableTokens} tokens</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tokens to use:
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="range"
                min="0"
                max={maxRedeemableTokens}
                step="10"
                value={selectedTokens}
                onChange={(e) => handleTokenChange(parseInt(e.target.value))}
                className="flex-1"
              />
              <input
                type="number"
                min="0"
                max={maxRedeemableTokens}
                value={selectedTokens}
                onChange={(e) => handleTokenChange(parseInt(e.target.value) || 0)}
                className="w-20 px-2 py-1 border rounded text-sm"
              />
            </div>
          </div>

          {selectedTokens > 0 && (
            <div className="bg-green-50 border border-green-200 rounded p-3">
              <div className="text-sm space-y-1">
                <div className="flex justify-between">
                  <span>Tokens used:</span>
                  <span className="font-semibold">{selectedTokens}</span>
                </div>
                <div className="flex justify-between">
                  <span>Token value:</span>
                  <span className="font-semibold text-green-600">
                    -{formatINR(selectedTokens * redemptionData.token_rate)}
                  </span>
                </div>
                <div className="flex justify-between border-t pt-1">
                  <span>Amount to pay:</span>
                  <span className="font-semibold">
                    {formatINR(orderAmount - (selectedTokens * redemptionData.token_rate))}
                  </span>
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Remaining tokens:</span>
                  <span>{redemptionData.available_tokens - selectedTokens}</span>
                </div>
              </div>
            </div>
          )}

          <div className="flex space-x-2">
            <button
              type="button"
              onClick={() => handleTokenChange(maxRedeemableTokens)}
              className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
            >
              Use Max
            </button>
            <button
              type="button"
              onClick={() => handleTokenChange(0)}
              className="px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
            >
              Clear
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TokenRedemption;
