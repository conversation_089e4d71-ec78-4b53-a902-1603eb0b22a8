#!/usr/bin/env python
"""
Test script for Flappy Bird API integration
"""
import os
import sys
import django
from django.conf import settings

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import TestCase, Client
from django.urls import reverse
from gaming.models import GameSession
from wallet.models import Wallet
from gaming.flappy_bird_api import start_flappy_bird_game, submit_flappy_bird_score
import json

def test_flappy_bird_integration():
    """Test the Flappy Bird API integration"""
    print("🎮 Testing Flappy Bird API Integration...")
    
    # Create test user
    try:
        user = User.objects.get(username='testuser')
        print(f"✅ Using existing test user: {user.username}")
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        print(f"✅ Created test user: {user.username}")
    
    # Create or get wallet
    wallet, created = Wallet.objects.get_or_create(
        user=user,
        defaults={'balance': 100}
    )
    if created:
        print(f"✅ Created wallet with balance: {wallet.balance}")
    else:
        wallet.balance = 100
        wallet.save()
        print(f"✅ Updated wallet balance to: {wallet.balance}")
    
    # Test 1: Start game
    print("\n🚀 Test 1: Starting Flappy Bird game...")
    client = Client()
    client.force_login(user)
    
    start_response = client.post('/api/gaming/flappy-bird/start/', 
                                content_type='application/json',
                                data=json.dumps({'difficulty': 'medium'}))
    
    print(f"Start game response status: {start_response.status_code}")
    start_data = start_response.json()
    print(f"Start game response: {start_data}")
    
    if start_data.get('success'):
        session_id = start_data['session_id']
        print(f"✅ Game started successfully! Session ID: {session_id}")
        
        # Test 2: Submit winning score (10+ pipes)
        print("\n🏆 Test 2: Submitting winning score...")
        submit_response = client.post('/api/gaming/flappy-bird/submit-score/',
                                    content_type='application/json',
                                    data=json.dumps({
                                        'session_id': session_id,
                                        'score': 150,
                                        'pipes_passed': 15,
                                        'game_duration': 45.5,
                                        'exit_reason': 'collision'
                                    }))
        
        print(f"Submit score response status: {submit_response.status_code}")
        submit_data = submit_response.json()
        print(f"Submit score response: {submit_data}")
        
        if submit_data.get('success'):
            print(f"✅ Score submitted successfully!")
            print(f"   Tokens change: {submit_data.get('tokens_change', 0)}")
            print(f"   New balance: {submit_data.get('new_balance', 0)}")
            print(f"   Message: {submit_data.get('message', 'N/A')}")
        else:
            print(f"❌ Score submission failed: {submit_data.get('error', 'Unknown error')}")
    else:
        print(f"❌ Game start failed: {start_data.get('error', 'Unknown error')}")
    
    # Test 3: Get stats
    print("\n📊 Test 3: Getting Flappy Bird stats...")
    stats_response = client.get('/api/gaming/flappy-bird/stats/')
    print(f"Stats response status: {stats_response.status_code}")
    stats_data = stats_response.json()
    print(f"Stats response: {stats_data}")
    
    # Test 4: Test game page access
    print("\n🎯 Test 4: Testing game page access...")
    game_page_response = client.get('/api/gaming/flappy-bird/')
    print(f"Game page response status: {game_page_response.status_code}")
    
    if game_page_response.status_code == 200:
        print("✅ Game page accessible!")
    else:
        print(f"❌ Game page not accessible: {game_page_response.status_code}")
    
    print("\n🎮 Flappy Bird API Integration Test Complete!")

if __name__ == '__main__':
    test_flappy_bird_integration()
