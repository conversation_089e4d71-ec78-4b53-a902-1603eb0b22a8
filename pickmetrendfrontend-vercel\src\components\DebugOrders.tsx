import React, { useState } from 'react';
import { api } from '../services/api';

const DebugOrders: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const runDebugTests = async () => {
    setLoading(true);
    const results: any = {};

    try {
      // Test 1: Check if token exists
      const token = localStorage.getItem('access_token');
      results.tokenExists = !!token;
      results.tokenPreview = token ? `${token.substring(0, 20)}...` : 'No token';

      // Test 2: Check API base URL
      results.apiBaseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';

      // Test 3: Test basic connectivity
      try {
        const response = await fetch(`${results.apiBaseUrl}/api/orders/`, {
          method: 'GET',
          headers: {
            'Authorization': `JWT ${token}`,
            'Content-Type': 'application/json',
          },
        });
        
        results.connectivityTest = {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
        };

        if (response.ok) {
          const data = await response.json();
          results.apiResponse = {
            success: true,
            dataKeys: Object.keys(data),
            resultsCount: data.results ? data.results.length : 'No results key',
          };
        } else {
          const errorText = await response.text();
          results.apiResponse = {
            success: false,
            error: errorText.substring(0, 200),
          };
        }
      } catch (fetchError: any) {
        results.connectivityTest = {
          error: fetchError.message,
        };
      }

      // Test 4: Test with configured API service
      try {
        const apiResponse = await api.get('/api/orders/');
        results.apiServiceTest = {
          success: true,
          status: apiResponse.status,
          dataKeys: Object.keys(apiResponse.data),
        };
      } catch (apiError: any) {
        results.apiServiceTest = {
          success: false,
          error: apiError.message,
          status: apiError.response?.status,
          statusText: apiError.response?.statusText,
          responseData: apiError.response?.data,
        };
      }

      // Test 5: Check localStorage
      results.localStorage = {
        accessToken: !!localStorage.getItem('access_token'),
        refreshToken: !!localStorage.getItem('refresh_token'),
        allKeys: Object.keys(localStorage),
      };

    } catch (error: any) {
      results.generalError = error.message;
    }

    setDebugInfo(results);
    setLoading(false);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">🔍 Orders Debug Tool</h2>
      
      <button
        onClick={runDebugTests}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {loading ? 'Running Tests...' : 'Run Debug Tests'}
      </button>

      {debugInfo && (
        <div className="mt-6 space-y-4">
          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-semibold mb-2">🔑 Authentication</h3>
            <p>Token exists: {debugInfo.tokenExists ? '✅ Yes' : '❌ No'}</p>
            <p>Token preview: {debugInfo.tokenPreview}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-semibold mb-2">🌐 API Configuration</h3>
            <p>Base URL: {debugInfo.apiBaseUrl}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-semibold mb-2">🔗 Connectivity Test</h3>
            {debugInfo.connectivityTest.error ? (
              <p className="text-red-600">❌ Error: {debugInfo.connectivityTest.error}</p>
            ) : (
              <>
                <p>Status: {debugInfo.connectivityTest.status} {debugInfo.connectivityTest.statusText}</p>
                <p>Success: {debugInfo.connectivityTest.ok ? '✅ Yes' : '❌ No'}</p>
              </>
            )}
          </div>

          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-semibold mb-2">📡 API Response</h3>
            {debugInfo.apiResponse.success ? (
              <>
                <p className="text-green-600">✅ API call successful</p>
                <p>Data keys: {JSON.stringify(debugInfo.apiResponse.dataKeys)}</p>
                <p>Results count: {debugInfo.apiResponse.resultsCount}</p>
              </>
            ) : (
              <p className="text-red-600">❌ Error: {debugInfo.apiResponse.error}</p>
            )}
          </div>

          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-semibold mb-2">⚙️ API Service Test</h3>
            {debugInfo.apiServiceTest.success ? (
              <>
                <p className="text-green-600">✅ API service working</p>
                <p>Status: {debugInfo.apiServiceTest.status}</p>
                <p>Data keys: {JSON.stringify(debugInfo.apiServiceTest.dataKeys)}</p>
              </>
            ) : (
              <>
                <p className="text-red-600">❌ API service error: {debugInfo.apiServiceTest.error}</p>
                <p>Status: {debugInfo.apiServiceTest.status}</p>
                <p>Status text: {debugInfo.apiServiceTest.statusText}</p>
                {debugInfo.apiServiceTest.responseData && (
                  <pre className="text-xs mt-2 bg-red-50 p-2 rounded">
                    {JSON.stringify(debugInfo.apiServiceTest.responseData, null, 2)}
                  </pre>
                )}
              </>
            )}
          </div>

          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-semibold mb-2">💾 Local Storage</h3>
            <p>Access token: {debugInfo.localStorage.accessToken ? '✅ Present' : '❌ Missing'}</p>
            <p>Refresh token: {debugInfo.localStorage.refreshToken ? '✅ Present' : '❌ Missing'}</p>
            <p>All keys: {debugInfo.localStorage.allKeys.join(', ')}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-semibold mb-2">📋 Full Debug Data</h3>
            <pre className="text-xs bg-white p-2 rounded border overflow-auto max-h-40">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default DebugOrders;
