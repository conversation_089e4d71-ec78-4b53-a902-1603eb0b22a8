from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings
from customer_communication.models import SupportTicket

class Command(BaseCommand):
    help = 'Test direct email functionality without Celery'

    def handle(self, *args, **options):
        # Get the first support ticket
        ticket = SupportTicket.objects.first()
        
        if not ticket:
            self.stdout.write(self.style.ERROR('No support tickets found'))
            return
        
        self.stdout.write(self.style.SUCCESS(f'Using support ticket: {ticket.id}'))
        
        # Prepare email content
        subject = f"Support Ticket Received - {ticket.subject}"
        message = f"""
        Dear {ticket.name},
        
        Thank you for contacting PickMeTrend support. We have received your ticket regarding:
        
        "{ticket.subject}"
        
        Our team will review your inquiry and get back to you as soon as possible.
        
        Ticket ID: {ticket.id}
        
        Thank you for your patience.
        
        Best regards,
        PickMeTrend Support Team
        <EMAIL>
        +91 9353014895
        """
        
        # Send the email directly
        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.SUPPORT_EMAIL,
                recipient_list=[ticket.email],
                fail_silently=False,
            )
            self.stdout.write(self.style.SUCCESS('Support ticket confirmation email sent successfully'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error sending email: {str(e)}'))
