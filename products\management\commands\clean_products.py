import logging
from django.core.management.base import BaseCommand
from products.models import Product, ProductImage
from printify.models import PrintifyProduct

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Clean products to keep only Printify products and fix images'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting product cleanup'))
        
        # Step 1: Count products before cleanup
        total_before = Product.objects.count()
        printify_before = Product.objects.exclude(printify_id__isnull=True).exclude(printify_id='').count()
        non_printify_before = total_before - printify_before
        
        self.stdout.write(f"Before cleanup:")
        self.stdout.write(f"Total products: {total_before}")
        self.stdout.write(f"Printify products: {printify_before}")
        self.stdout.write(f"Non-Printify products: {non_printify_before}")
        
        # Step 2: Delete non-Printify products
        if non_printify_before > 0:
            self.stdout.write(self.style.WARNING(f"Deleting {non_printify_before} non-Printify products"))
            deleted_null = Product.objects.filter(printify_id__isnull=True).delete()
            deleted_empty = Product.objects.filter(printify_id='').delete()
            self.stdout.write(self.style.SUCCESS(f"Deleted non-Printify products"))
        else:
            self.stdout.write(self.style.SUCCESS("No non-Printify products to delete"))
        
        # Step 3: Fix Printify product images
        printify_products = PrintifyProduct.objects.all()
        self.stdout.write(f"Found {printify_products.count()} Printify products to process")
        
        for printify_product in printify_products:
            self.stdout.write(f"Processing Printify product: {printify_product.title} (ID: {printify_product.printify_id})")
            
            # Find corresponding Django product
            try:
                product = Product.objects.get(printify_id=printify_product.printify_id)
                self.stdout.write(f"Found corresponding Django product: {product.name} (ID: {product.id})")
                
                # Get images from Printify product
                images = printify_product.images_json
                if not images:
                    self.stdout.write(self.style.WARNING("No images found in Printify product"))
                    continue
                    
                self.stdout.write(f"Found {len(images)} images in Printify product")
                
                # Delete existing product images
                deleted_count = ProductImage.objects.filter(product=product).delete()
                self.stdout.write(f"Deleted {deleted_count[0]} existing product images")
                
                # Create new product images
                for i, image_data in enumerate(images):
                    image_url = image_data.get('src', '')
                    if image_url:
                        ProductImage.objects.create(
                            product=product,
                            image=image_url,
                            image_url=image_url,
                            is_primary=(i == 0),
                            alt_text=f"{product.name} - Image {i+1}"
                        )
                        self.stdout.write(f"Created image {i+1}: {image_url}")
                    else:
                        self.stdout.write(self.style.WARNING(f"Skipped image {i+1}: No URL found"))
                
                self.stdout.write(self.style.SUCCESS(f"Successfully updated images for {product.name}"))
                
            except Product.DoesNotExist:
                self.stdout.write(self.style.WARNING(f"No Django product found with Printify ID: {printify_product.printify_id}"))
                self.stdout.write("Creating new product from Printify data...")
                
                # Create new product from Printify data
                from django.utils.text import slugify
                import decimal
                
                # Get the first variant for pricing
                variants = printify_product.variants_json
                price = decimal.Decimal('0.00')
                if variants and len(variants) > 0:
                    variant = variants[0]
                    price_str = variant.get('price', '0')
                    try:
                        price = decimal.Decimal(price_str)
                    except (decimal.InvalidOperation, TypeError):
                        price = decimal.Decimal('0.00')
                
                # Create the product
                slug = slugify(printify_product.title)
                product = Product.objects.create(
                    name=printify_product.title,
                    slug=slug,
                    description=printify_product.description or '',
                    price=price,
                    stock=100,  # Default stock
                    printify_id=printify_product.printify_id,
                    is_active=True
                )
                self.stdout.write(self.style.SUCCESS(f"Created new product: {product.name} (ID: {product.id})"))
                
                # Create product images
                images = printify_product.images_json
                if images:
                    for i, image_data in enumerate(images):
                        image_url = image_data.get('src', '')
                        if image_url:
                            ProductImage.objects.create(
                                product=product,
                                image=image_url,
                                image_url=image_url,
                                is_primary=(i == 0),
                                alt_text=f"{product.name} - Image {i+1}"
                            )
                            self.stdout.write(f"Created image {i+1}: {image_url}")
                        else:
                            self.stdout.write(self.style.WARNING(f"Skipped image {i+1}: No URL found"))
                
                self.stdout.write(self.style.SUCCESS(f"Successfully created product and images for {printify_product.title}"))
        
        # Step 4: Print summary
        total_after = Product.objects.count()
        printify_after = Product.objects.exclude(printify_id__isnull=True).exclude(printify_id='').count()
        non_printify_after = total_after - printify_after
        
        self.stdout.write(f"\nAfter cleanup:")
        self.stdout.write(f"Total products: {total_after}")
        self.stdout.write(f"Printify products: {printify_after}")
        self.stdout.write(f"Non-Printify products: {non_printify_after}")
        self.stdout.write(f"Total product images: {ProductImage.objects.count()}")
        
        self.stdout.write(self.style.SUCCESS('Product cleanup completed successfully'))
