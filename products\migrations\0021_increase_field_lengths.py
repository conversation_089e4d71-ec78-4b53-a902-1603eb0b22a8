# Generated manually to fix <PERSON><PERSON><PERSON><PERSON> length issues
# This migration increases the max_length of various fields to accommodate longer data from external APIs

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0020_product_allow_token_discount_and_more'),
    ]

    operations = [
        # Increase printify_id field length in Product model
        migrations.AlterField(
            model_name='product',
            name='printify_id',
            field=models.CharField(blank=True, db_index=True, help_text='Printify product ID for synced products', max_length=255, null=True),
        ),
        
        # Increase alt_text field length in ProductImage model
        migrations.AlterField(
            model_name='productimage',
            name='alt_text',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        
        # Increase variant_id field length in ProductVariant model
        migrations.AlterField(
            model_name='productvariant',
            name='variant_id',
            field=models.CharField(help_text='Variant ID from Printify (SKU)', max_length=255),
        ),
        
        # Increase color field length in ProductVariant model
        migrations.AlterField(
            model_name='productvariant',
            name='color',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        
        # Increase size field length in ProductVariant model
        migrations.AlterField(
            model_name='productvariant',
            name='size',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
