import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import ProductCard from '../components/products/ProductCard';

// API URL
const BASE_URL = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
const API_URL = `${BASE_URL}/api`;

console.log('Home component API URL:', API_URL);

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  compare_price?: number;
  discount_percentage?: number;
  stock: number;
  main_image?: {
    id: number;
    image: string;
    alt_text?: string;
  };
  is_featured: boolean;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image?: string;
}

const Home: React.FC = () => {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        console.log('Fetching featured products from:', `${API_URL}/products/items/?is_featured=true`);

        // Fetch featured products
        const productsRes = await axios.get(`${API_URL}/products/items/?is_featured=true`);
        console.log('Featured products response:', productsRes.data);

        // Handle both paginated and non-paginated responses
        const products = productsRes.data.results || productsRes.data;
        console.log('Featured products:', products);
        setFeaturedProducts(products);

        console.log('Fetching categories from:', `${API_URL}/products/categories/`);

        // Fetch categories
        const categoriesRes = await axios.get(`${API_URL}/products/categories/`);
        console.log('Categories response:', categoriesRes.data);

        // Handle both paginated and non-paginated responses
        const categories = categoriesRes.data.results || categoriesRes.data;
        console.log('Categories:', categories);
        setCategories(categories);

        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div>
      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 overflow-hidden">
        {/* Background image */}
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=1200&auto=format&fit=crop"
            alt="Fashion and Gaming Lifestyle"
            className="w-full h-full object-cover object-center opacity-40"
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 via-purple-900/70 to-pink-900/80"></div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 right-0 -mt-16 -mr-16 w-64 h-64 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 bg-gradient-to-tr from-pink-400 to-purple-600 rounded-full opacity-15 animate-pulse"></div>

        {/* Content */}
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
          <div className="text-center">
            {/* Brand tagline with emojis */}
            <div className="mb-6">
              <span className="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white font-medium border border-white border-opacity-30">
                <span className="mr-2">🎮</span>
                Play • Shop • Win
                <span className="ml-2">🛍️</span>
              </span>
            </div>

            <h1 className="text-4xl font-extrabold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
              <span className="block">Style Meets</span>
              <span className="block bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Gaming at PickMeTrend
              </span>
            </h1>

            <p className="mt-6 max-w-2xl mx-auto text-xl text-gray-200 sm:text-2xl leading-relaxed">
              Discover trendy fashion, play exciting games, earn tokens, and shop smarter.
              <span className="block mt-2 text-cyan-300 font-semibold">Your style, your game, your rewards!</span>
            </p>

            {/* Feature highlights */}
            <div className="mt-8 flex flex-wrap justify-center gap-4 text-sm">
              <span className="px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white border border-white border-opacity-30">
                🎯 Battle Arena
              </span>
              <span className="px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white border border-white border-opacity-30">
                🪙 Earn Tokens
              </span>
              <span className="px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white border border-white border-opacity-30">
                👕 Custom T-Shirts
              </span>
              <span className="px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white border border-white border-opacity-30">
                💰 Smart Discounts
              </span>
            </div>

            <div className="mt-12 max-w-md mx-auto sm:flex sm:justify-center md:mt-16 gap-4">
              <div className="mb-4 sm:mb-0">
                <Link
                  to="/shop?category=trendy-designs"
                  className="w-full flex items-center justify-center px-8 py-4 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-200 shadow-2xl md:text-lg md:px-10"
                >
                  <span className="mr-2">🛍️</span>
                  Shop Now
                </Link>
              </div>
              <div>
                <Link
                  to="/game-dashboard"
                  className="w-full flex items-center justify-center px-8 py-4 border-2 border-white border-opacity-30 text-base font-semibold rounded-xl text-white bg-white bg-opacity-10 backdrop-blur-sm hover:bg-opacity-20 transform hover:scale-105 transition-all duration-200 shadow-2xl md:text-lg md:px-10"
                >
                  <span className="mr-2">🎮</span>
                  Start Gaming
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Gaming & Wallet Promotional Banners */}
      <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              🎮 Play, Shop & Earn with PickMeTrend! 💰
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover our exciting gaming features and smart wallet system designed to enhance your shopping experience
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Gaming Banner */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 p-8 text-white shadow-2xl transform hover:scale-105 transition-all duration-300">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-4">
                  <span className="text-4xl mr-3">🎮</span>
                  <h3 className="text-2xl font-bold">Play & Win</h3>
                </div>

                <p className="text-lg mb-6 text-blue-100">
                  Challenge friends, compete in battles, and earn tokens while having fun!
                  Turn your gaming skills into shopping rewards.
                </p>

                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium">🎯 Battle Arena</span>
                  <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium">🏆 Tournaments</span>
                  <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium">🪙 Earn Tokens</span>
                </div>

                <Link
                  to="/game-dashboard"
                  className="inline-flex items-center px-6 py-3 bg-white text-purple-700 font-semibold rounded-lg hover:bg-gray-100 transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🚀</span>
                  Start Playing Now
                </Link>
              </div>
            </div>

            {/* Wallet Banner */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-700 p-8 text-white shadow-2xl transform hover:scale-105 transition-all duration-300">
              <div className="absolute top-0 left-0 -mt-6 -ml-6 w-28 h-28 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 right-0 -mb-10 -mr-10 w-36 h-36 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-4">
                  <span className="text-4xl mr-3">💰</span>
                  <h3 className="text-2xl font-bold">Smart Wallet</h3>
                </div>

                <p className="text-lg mb-6 text-emerald-100">
                  Manage your tokens, get exclusive discounts, and enjoy seamless payments.
                  Your digital wallet for smarter shopping!
                </p>

                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium">💳 Easy Payments</span>
                  <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium">🎁 Token Discounts</span>
                  <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium">📊 Track Earnings</span>
                </div>

                <Link
                  to="/wallet"
                  className="inline-flex items-center px-6 py-3 bg-white text-teal-700 font-semibold rounded-lg hover:bg-gray-100 transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">💎</span>
                  Explore Wallet
                </Link>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-white rounded-xl shadow-lg">
              <div className="text-3xl mb-2">🎯</div>
              <div className="text-2xl font-bold text-gray-900">Play Games</div>
              <div className="text-gray-600">Compete & Win Tokens</div>
            </div>
            <div className="text-center p-6 bg-white rounded-xl shadow-lg">
              <div className="text-3xl mb-2">🪙</div>
              <div className="text-2xl font-bold text-gray-900">Earn Rewards</div>
              <div className="text-gray-600">Convert to Discounts</div>
            </div>
            <div className="text-center p-6 bg-white rounded-xl shadow-lg">
              <div className="text-3xl mb-2">🛍️</div>
              <div className="text-2xl font-bold text-gray-900">Shop Smart</div>
              <div className="text-gray-600">Save with Token Discounts</div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Products Section */}
      <div className="bg-gradient-to-br from-gray-50 via-white to-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="relative inline-block">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">🌟 Featured Products</h2>
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"></div>
            </div>
            <p className="text-xl text-gray-600 mt-6 max-w-2xl mx-auto">
              Discover our handpicked collection of trending fashion items and exclusive designs
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-600 border-t-transparent mx-auto mb-4"></div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">Loading Amazing Products</h3>
                <p className="text-gray-600">Please wait while we fetch the latest items...</p>
              </div>
            </div>
          ) : error ? (
            <div className="max-w-2xl mx-auto">
              <div className="bg-red-50 border border-red-200 rounded-2xl p-8 shadow-lg text-center">
                <span className="text-6xl mb-4 block">⚠️</span>
                <h3 className="text-xl font-semibold text-red-800 mb-2">Oops! Something went wrong</h3>
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          ) : featuredProducts.length === 0 ? (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto bg-white rounded-2xl shadow-2xl p-12 border border-gray-100">
                <span className="text-8xl mb-6 block">🛍️</span>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">No Featured Products</h3>
                <p className="text-gray-600 mb-8">We're working on adding amazing products. Check back soon!</p>
                <Link
                  to="/shop"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🔍</span>
                  Browse All Products
                </Link>
              </div>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
                {featuredProducts.map((product) => (
                  <div key={product.id} className="group">
                    <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-300 border border-gray-100">
                      <ProductCard
                        id={product.id}
                        name={product.name}
                        slug={product.slug}
                        price={product.price}
                        comparePrice={product.compare_price}
                        stock={product.stock}
                        mainImage={product.main_image}
                        isFeatured={product.is_featured}
                      />
                    </div>
                  </div>
                ))}
              </div>

              <div className="text-center mt-12">
                <Link
                  to="/shop"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🛍️</span>
                  View All Products
                </Link>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose PickMeTrend?</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Experience the perfect blend of quality, convenience, and gaming rewards
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-2xl shadow-2xl p-8 text-center transform hover:scale-105 transition-all duration-300 border border-gray-100">
              <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-r from-green-400 to-emerald-500 text-white mb-6">
                <span className="text-2xl">🚚</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Free Shipping</h3>
              <p className="text-gray-600">
                Free shipping on all orders over ₹1,000. Fast delivery across India with international shipping available.
              </p>
            </div>

            <div className="bg-white rounded-2xl shadow-2xl p-8 text-center transform hover:scale-105 transition-all duration-300 border border-gray-100">
              <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-400 to-purple-500 text-white mb-6">
                <span className="text-2xl">✅</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Quality Guaranteed</h3>
              <p className="text-gray-600">
                Premium quality products with rigorous quality checks and a 30-day money-back guarantee for your peace of mind.
              </p>
            </div>

            <div className="bg-white rounded-2xl shadow-2xl p-8 text-center transform hover:scale-105 transition-all duration-300 border border-gray-100">
              <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-r from-purple-400 to-pink-500 text-white mb-6">
                <span className="text-2xl">💬</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">24/7 Support</h3>
              <p className="text-gray-600">
                Our dedicated customer support team is available round the clock to assist you with any queries or concerns.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;