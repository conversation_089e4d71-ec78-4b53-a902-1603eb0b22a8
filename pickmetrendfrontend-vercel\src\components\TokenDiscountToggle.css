.token-discount-toggle {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
    margin: 16px 0;
    transition: all 0.3s ease;
}

.token-discount-toggle:hover {
    border-color: #ffd700;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.2);
}

/* Header */
.token-discount-toggle__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

/* Label and checkbox */
.token-discount-toggle__label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: #333;
}

.token-discount-toggle__checkbox {
    display: none;
}

.token-discount-toggle__checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.token-discount-toggle__checkbox:checked + .token-discount-toggle__checkmark {
    background: #ffd700;
    border-color: #e6c200;
}

.token-discount-toggle__checkbox:checked + .token-discount-toggle__checkmark:after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #333;
    font-weight: bold;
    font-size: 12px;
}

/* Balance display */
.token-discount-toggle__balance {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
    font-size: 14px;
}

.token-discount-toggle__icon {
    font-size: 16px;
}

/* Details section */
.token-discount-toggle__details {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
    border: 1px solid #e9ecef;
}

.token-discount-toggle__breakdown {
    margin-bottom: 16px;
}

.token-discount-toggle__row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 14px;
}

.token-discount-toggle__row--total {
    border-top: 1px solid #e9ecef;
    margin-top: 8px;
    padding-top: 8px;
    font-weight: 600;
}

.token-discount-toggle__value {
    font-weight: 600;
}

.token-discount-toggle__value--discount {
    color: #28a745;
}

.token-discount-toggle__value--final {
    color: #007bff;
    font-size: 16px;
}

/* Eligible items */
.token-discount-toggle__eligible-items h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #666;
}

.token-discount-toggle__eligible-items ul {
    margin: 0;
    padding-left: 16px;
    font-size: 13px;
    color: #666;
}

.token-discount-toggle__eligible-items li {
    margin-bottom: 4px;
}

/* State variants */
.token-discount-toggle--loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-style: italic;
}

.token-discount-toggle__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.token-discount-toggle--error {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #dc3545;
    background: #f8d7da;
    border-color: #f5c6cb;
}

.token-discount-toggle__error-icon {
    font-size: 18px;
}

.token-discount-toggle--unavailable {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    background: #f8f9fa;
    border-color: #dee2e6;
}

.token-discount-toggle--no-tokens {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    background: #f8f9fa;
    border-color: #dee2e6;
}

.token-discount-toggle__earn-link {
    color: #007bff;
    text-decoration: none;
    font-weight: 600;
    margin-left: 8px;
}

.token-discount-toggle__earn-link:hover {
    text-decoration: underline;
}

.token-discount-toggle--disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* Responsive */
@media (max-width: 768px) {
    .token-discount-toggle__header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .token-discount-toggle__balance {
        font-size: 13px;
    }
    
    .token-discount-toggle__row {
        font-size: 13px;
    }
    
    .token-discount-toggle__value--final {
        font-size: 15px;
    }
}
