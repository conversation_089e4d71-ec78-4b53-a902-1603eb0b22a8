import React, { useState } from 'react';
import SpinWheel from './SpinWheel';

/**
 * Test component for Spin Wheel functionality
 * Use this to test the Spin Wheel in isolation
 */
const SpinWheelTest: React.FC = () => {
  const [balance, setBalance] = useState(100);
  const [lastReward, setLastReward] = useState<any>(null);
  const [rewardHistory, setRewardHistory] = useState<any[]>([]);

  const handleRewardWon = (reward: any) => {
    console.log('🎉 Reward won:', reward);
    setLastReward(reward);
    setRewardHistory(prev => [reward, ...prev.slice(0, 4)]); // Keep last 5 rewards
  };

  const handleBalanceUpdate = (newBalance: number) => {
    console.log('💰 Balance updated:', newBalance);
    setBalance(newBalance);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-100 to-pink-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
            🎡 Spin Wheel Test
          </h1>
          <p className="text-gray-600">Test the Spin Wheel functionality</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Spin Wheel */}
          <div className="lg:col-span-2">
            <SpinWheel 
              onRewardWon={handleRewardWon}
              onBalanceUpdate={handleBalanceUpdate}
            />
          </div>

          {/* Test Panel */}
          <div className="space-y-6">
            {/* Current Status */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-bold mb-4 text-gray-800">📊 Current Status</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Balance:</span>
                  <span className="font-bold text-orange-600">{balance} 🪙</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Reward:</span>
                  <span className="font-medium text-green-600">
                    {lastReward ? lastReward.name || lastReward.type : 'None'}
                  </span>
                </div>
              </div>
            </div>

            {/* Last Reward Details */}
            {lastReward && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-bold mb-4 text-gray-800">🎁 Last Reward</h3>
                <div className="space-y-2">
                  <div><strong>Name:</strong> {lastReward.name}</div>
                  <div><strong>Type:</strong> {lastReward.type}</div>
                  <div><strong>Value:</strong> {lastReward.value}</div>
                  {lastReward.description && (
                    <div><strong>Description:</strong> {lastReward.description}</div>
                  )}
                  {lastReward.new_balance && (
                    <div><strong>New Balance:</strong> {lastReward.new_balance} 🪙</div>
                  )}
                </div>
              </div>
            )}

            {/* Reward History */}
            {rewardHistory.length > 0 && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-bold mb-4 text-gray-800">📜 Recent Rewards</h3>
                <div className="space-y-2">
                  {rewardHistory.map((reward, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-gray-600">#{index + 1}</span>
                      <span className="font-medium">{reward.name || reward.type}</span>
                      <span className="text-green-600">+{reward.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Debug Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-bold text-gray-700 mb-2">🔧 Debug Info</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <div>Check browser console for detailed logs</div>
                <div>Network tab shows API calls</div>
                <div>Rewards update balance automatically</div>
              </div>
            </div>

            {/* Test Instructions */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-bold text-blue-700 mb-2">📋 Test Checklist</h4>
              <div className="text-sm text-blue-600 space-y-1">
                <div>✅ Wheel loads properly</div>
                <div>✅ "Spin Now" button works</div>
                <div>✅ Wheel animates smoothly</div>
                <div>✅ Rewards are displayed</div>
                <div>✅ Balance updates correctly</div>
                <div>✅ Cooldown timer works</div>
                <div>✅ Error handling works</div>
              </div>
            </div>
          </div>
        </div>

        {/* API Test Buttons */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-bold mb-4 text-gray-800">🔧 API Test Tools</h3>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => {
                fetch('/api/gaming/spin-wheel/status/')
                  .then(res => res.json())
                  .then(data => console.log('Status API:', data))
                  .catch(err => console.error('Status API Error:', err));
              }}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Test Status API
            </button>
            
            <button
              onClick={() => {
                console.log('Current balance:', balance);
                console.log('Last reward:', lastReward);
                console.log('Reward history:', rewardHistory);
              }}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              Log Current State
            </button>
            
            <button
              onClick={() => {
                setLastReward(null);
                setRewardHistory([]);
                console.log('Test data cleared');
              }}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              Clear Test Data
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpinWheelTest;
