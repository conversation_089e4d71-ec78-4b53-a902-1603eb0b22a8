{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" type="text/css" href="{% static "admin/css/changelists.css" %}">
  <style>
    .service-card {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 20px;
        background-color: #f9f9f9;
    }
    
    .service-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 3px;
        font-weight: bold;
        margin-right: 10px;
    }
    
    .status-on {
        background-color: #dff0d8;
        color: #3c763d;
    }
    
    .status-off {
        background-color: #f2dede;
        color: #a94442;
    }
    
    .toggle-button {
        display: inline-block;
        padding: 8px 15px;
        border-radius: 3px;
        color: white !important;
        text-decoration: none;
        font-weight: bold;
        cursor: pointer;
    }
    
    .enable-button {
        background-color: #5cb85c;
    }
    
    .disable-button {
        background-color: #d9534f;
    }
    
    .service-description {
        margin-top: 10px;
        color: #666;
    }
    
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        background-color: #333;
        z-index: 1000;
        display: none;
    }
    
    .toast-success {
        background-color: #5cb85c;
    }
    
    .toast-error {
        background-color: #d9534f;
    }
  </style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url 'admin:service_control_servicecontrol_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; {% trans 'Service Control Dashboard' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <h1>Service Control Dashboard</h1>
    <p>Use this dashboard to enable or disable background services.</p>
    
    <div class="service-card">
        <h2>Celery Task Queue</h2>
        <div class="service-status {% if service_control.celery_enabled %}status-on{% else %}status-off{% endif %}">
            {% if service_control.celery_enabled %}ON{% else %}OFF{% endif %}
        </div>
        
        {% if service_control.celery_enabled %}
        <a href="{% url 'admin:toggle-celery' %}" class="toggle-button disable-button" id="toggle-celery">Disable</a>
        {% else %}
        <a href="{% url 'admin:toggle-celery' %}" class="toggle-button enable-button" id="toggle-celery">Enable</a>
        {% endif %}
        
        <div class="service-description">
            <p>Celery is responsible for processing background tasks such as sending emails, processing orders, and syncing with external services.</p>
            <p><strong>When disabled:</strong> Background tasks will not be processed. This can be useful for maintenance or debugging.</p>
        </div>
    </div>
    
    <div class="service-card">
        <h2>Redis Cache</h2>
        <div class="service-status {% if service_control.redis_enabled %}status-on{% else %}status-off{% endif %}">
            {% if service_control.redis_enabled %}ON{% else %}OFF{% endif %}
        </div>
        
        {% if service_control.redis_enabled %}
        <a href="{% url 'admin:toggle-redis' %}" class="toggle-button disable-button" id="toggle-redis">Disable</a>
        {% else %}
        <a href="{% url 'admin:toggle-redis' %}" class="toggle-button enable-button" id="toggle-redis">Enable</a>
        {% endif %}
        
        <div class="service-description">
            <p>Redis is used for caching, session storage, and as a message broker for Celery.</p>
            <p><strong>When disabled:</strong> Caching will be disabled and performance may be affected. Celery tasks may also be affected.</p>
        </div>
    </div>
    
    <div class="service-card">
        <h2>API Endpoints</h2>
        <p>You can also control services via the API:</p>
        <ul>
            <li><code>GET /service-control/status/</code> - Get current status</li>
            <li><code>POST /service-control/toggle-celery/</code> - Toggle Celery</li>
            <li><code>POST /service-control/toggle-redis/</code> - Toggle Redis</li>
        </ul>
        <p>Note: API endpoints require admin or staff authentication.</p>
    </div>
</div>

<div id="toast" class="toast"></div>

<script>
    // Function to show toast message
    function showToast(message, type) {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = 'toast';
        
        if (type === 'success') {
            toast.classList.add('toast-success');
        } else if (type === 'error') {
            toast.classList.add('toast-error');
        }
        
        toast.style.display = 'block';
        
        // Hide toast after 3 seconds
        setTimeout(function() {
            toast.style.display = 'none';
        }, 3000);
    }
    
    // Function to toggle service via AJAX
    function toggleService(url, serviceType) {
        fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.ok) {
                showToast(`${serviceType} toggled successfully`, 'success');
                // Reload page to update UI
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                showToast(`Failed to toggle ${serviceType}`, 'error');
            }
        })
        .catch(error => {
            showToast(`Error: ${error.message}`, 'error');
        });
    }
    
    // Add event listeners to toggle buttons
    document.getElementById('toggle-celery').addEventListener('click', function(e) {
        e.preventDefault();
        toggleService(this.href, 'Celery');
    });
    
    document.getElementById('toggle-redis').addEventListener('click', function(e) {
        e.preventDefault();
        toggleService(this.href, 'Redis');
    });
</script>
{% endblock %}
