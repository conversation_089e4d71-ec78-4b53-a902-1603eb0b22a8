import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

// API URL
const BASE_URL = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
const API_URL = `${BASE_URL}/api`;
const CART_URL = `${API_URL}/orders/cart/`;

// Enable debug mode only in development
const DEBUG = process.env.NODE_ENV === 'development';

// Log API URLs for debugging (only in development)
if (DEBUG) {
  console.log('Cart API URLs:');
  console.log('BASE_URL:', BASE_URL);
  console.log('API_URL:', API_URL);
  console.log('CART_URL:', CART_URL);
}

// Logger function
const log = (message: string, data?: any) => {
  if (DEBUG) {
    if (data) {
      console.log(`[CartContext] ${message}`, data);
    } else {
      console.log(`[CartContext] ${message}`);
    }
  }
};

// Configure axios defaults
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.patch['Content-Type'] = 'application/json';

// Add request interceptor to include auth token
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      // Use JWT format as configured in the backend
      config.headers['Authorization'] = `JWT ${token}`;
      log('Adding auth token to request:', `JWT ${token.substring(0, 10)}...`);
    } else {
      log('No auth token available for request');
    }
    return config;
  },
  (error) => {
    log('Request interceptor error', error);
    return Promise.reject(error);
  }
);

// Add response interceptor to log responses
axios.interceptors.response.use(
  (response) => {
    log(`Response from ${response.config.url}`, {
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error) => {
    if (error.response) {
      log(`Error response from ${error.config?.url}`, {
        status: error.response.status,
        data: error.response.data
      });
    } else if (error.request) {
      log('No response received', error.request);
    } else {
      log('Request setup error', error.message);
    }
    return Promise.reject(error);
  }
);

// Types
interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  compare_price?: number;
  main_image?: {
    id: number;
    image: string;
    alt_text?: string;
  };
}

interface CartItem {
  id: number;
  product: Product;
  quantity: number;
  total_price: number;
}

interface Cart {
  id: string;
  items: CartItem[];
  total_price: number;
  total_items: number;
}

interface CartContextType {
  cart: Cart | null;
  loading: boolean;
  error: string | null;
  addToCart: (productId: string, quantity: number) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  updateCartItem: (itemId: number, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  refetchCart: () => void;
}

interface CartProviderProps {
  children: ReactNode;
}

// Create context
export const CartContext = createContext<CartContextType>({
  cart: null,
  loading: false,
  error: null,
  addToCart: async () => {},
  removeFromCart: async () => {},
  updateCartItem: async () => {},
  clearCart: async () => {},
  refetchCart: () => {},
});

// Provider component
export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const { isAuthenticated, user } = useAuth();

  // Fetch user's cart
  const fetchCart = async () => {
    // Only fetch cart if user is authenticated
    if (!isAuthenticated) {
      log('User not authenticated, skipping cart fetch');
      setCart(null);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      log('Fetching cart from: ' + CART_URL);

      // First, try to get the user's cart
      const res = await axios.get(CART_URL);
      log('Cart response received', res.data);

      // Handle the response based on its format
      if (Array.isArray(res.data)) {
        // DRF often returns a list for list endpoints
        log('Response is an array of length: ' + res.data.length);

        if (res.data.length > 0) {
          log('Using first cart in array', res.data[0]);
          setCart(res.data[0]);
        } else {
          // No cart found, create one
          log('No carts found in array, creating a new one');
          await createNewCart();
        }
      } else if (res.data && typeof res.data === 'object') {
        // Check if it's a valid cart object with expected properties
        if (res.data.id) {
          log('Response is a single cart object with ID: ' + res.data.id);
          setCart(res.data);
        } else if (res.data.results && Array.isArray(res.data.results)) {
          // Handle paginated response
          log('Response is paginated with ' + res.data.results.length + ' results');

          if (res.data.results.length > 0) {
            log('Using first cart from paginated results', res.data.results[0]);
            setCart(res.data.results[0]);
          } else {
            // No cart found, create one
            log('No carts found in paginated results, creating a new one');
            await createNewCart();
          }
        } else {
          log('Response is an object but not a valid cart', res.data);
          // Try to create a new cart
          await createNewCart();
        }
      } else {
        log('Unexpected response format', res.data);
        // Try to create a new cart as a fallback
        await createNewCart();
      }
    } catch (err: any) {
      log('Error fetching cart', err);

      if (err.response) {
        log('Response error details', {
          status: err.response.status,
          data: err.response.data,
          url: err.config?.url
        });

        if (err.response.status === 404) {
          // Create a new cart if one doesn't exist
          log('Got 404, creating new cart');
          await createNewCart();
        } else if (err.response.status === 401) {
          log('Authentication error (401) when fetching cart');
          // Check if token is still in localStorage but might be expired
          const token = localStorage.getItem('access_token');
          if (token) {
            log('Token exists but might be expired, clearing cart state');
            setCart(null);
            setError('Your session has expired. Please log in again.');
          } else {
            setError('You need to be logged in to view your cart');
          }
        } else {
          setError(err.response.data?.detail || `Error ${err.response.status}: Failed to load cart`);
        }
      } else if (err.request) {
        log('No response received', err.request);
        setError('No response from server. Please check your connection.');
      } else {
        log('Error setting up request', err.message);
        setError(`Request error: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Helper function to create a new cart
  const createNewCart = async () => {
    log('Creating a new cart');
    try {
      const cartRes = await axios.post(CART_URL);
      log('New cart created', cartRes.data);

      if (cartRes.data && cartRes.data.id) {
        log('Setting cart with ID: ' + cartRes.data.id);
        setCart(cartRes.data);
      } else {
        log('Invalid cart data received', cartRes.data);
        setError('Failed to create cart: Invalid response');
      }
    } catch (cartErr: any) {
      log('Error creating cart', cartErr);

      // If we get a 400 error saying cart already exists, try to fetch it again
      if (cartErr.response?.status === 400 &&
          cartErr.response?.data?.detail?.includes('already have a cart')) {
        log('Cart already exists (400 error), fetching existing cart');
        try {
          // Try to get the cart again
          const getCartRes = await axios.get(CART_URL);
          log('Fetched existing cart', getCartRes.data);

          if (Array.isArray(getCartRes.data) && getCartRes.data.length > 0) {
            log('Using first cart from array', getCartRes.data[0]);
            setCart(getCartRes.data[0]);
          } else if (getCartRes.data && getCartRes.data.id) {
            log('Using single cart object', getCartRes.data);
            setCart(getCartRes.data);
          } else if (getCartRes.data && getCartRes.data.results &&
                    Array.isArray(getCartRes.data.results) &&
                    getCartRes.data.results.length > 0) {
            log('Using first cart from paginated results', getCartRes.data.results[0]);
            setCart(getCartRes.data.results[0]);
          } else {
            log('Failed to get existing cart after 400 error', getCartRes.data);
            setError('Failed to get your cart');
          }
        } catch (fetchErr) {
          log('Error fetching existing cart', fetchErr);
          setError('Failed to get your cart');
        }
      } else {
        setError(cartErr.response?.data?.detail || 'Failed to create shopping cart');
      }
    }
  };

  // Fetch cart when authenticated state changes or user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchCart();
    } else {
      setCart(null);
    }
  }, [isAuthenticated, user]);

  // Function to explicitly refetch the cart (used by other components)
  const refetchCartData = () => {
    if (isAuthenticated && user) {
      fetchCart();
    }
  };

  // Add item to cart
  const addToCart = async (productId: string, quantity: number) => {
    setLoading(true);
    setError(null);

    log(`Adding product ${productId} to cart with quantity ${quantity}`);

    try {
      // First, try to fetch the cart to make sure we have the latest data
      try {
        log('Fetching latest cart data before adding item');
        const cartRes = await axios.get(CART_URL);
        log('Current cart data:', cartRes.data);

        // Handle different response formats
        if (Array.isArray(cartRes.data) && cartRes.data.length > 0) {
          setCart(cartRes.data[0]);
        } else if (cartRes.data && cartRes.data.id) {
          setCart(cartRes.data);
        } else if (cartRes.data && cartRes.data.results && cartRes.data.results.length > 0) {
          setCart(cartRes.data.results[0]);
        }
      } catch (fetchErr: any) {
        log('Error fetching cart before adding item:', fetchErr);
        // If 404, we'll create a new cart below
        if (fetchErr.response?.status !== 404) {
          log('Non-404 error fetching cart:', fetchErr);
        }
      }

      // Ensure we have a cart
      let cartId = cart?.id;

      if (!cartId) {
        log('No cart found, creating a new one');
        try {
          const newCartRes = await axios.post(CART_URL);
          log('New cart created:', newCartRes.data);

          if (newCartRes.data && newCartRes.data.id) {
            setCart(newCartRes.data);
            cartId = newCartRes.data.id;
          } else {
            throw new Error('Invalid cart data received');
          }
        } catch (createErr: any) {
          log('Error creating cart:', createErr);

          // If we get a 400 error saying cart already exists, try to fetch it again
          if (createErr.response?.status === 400 &&
              createErr.response?.data?.detail?.includes('already have a cart')) {
            log('Cart already exists, fetching existing cart');

            try {
              const existingCartRes = await axios.get(CART_URL);
              log('Fetched existing cart:', existingCartRes.data);

              if (Array.isArray(existingCartRes.data) && existingCartRes.data.length > 0) {
                setCart(existingCartRes.data[0]);
                cartId = existingCartRes.data[0].id;
              } else if (existingCartRes.data && existingCartRes.data.id) {
                setCart(existingCartRes.data);
                cartId = existingCartRes.data.id;
              } else if (existingCartRes.data && existingCartRes.data.results &&
                        existingCartRes.data.results.length > 0) {
                setCart(existingCartRes.data.results[0]);
                cartId = existingCartRes.data.results[0].id;
              } else {
                throw new Error('Could not find existing cart');
              }
            } catch (fetchExistingErr) {
              log('Error fetching existing cart:', fetchExistingErr);
              setError('Failed to access your cart. Please try again.');
              setLoading(false);
              return;
            }
          } else {
            setError(createErr.response?.data?.detail || 'Failed to create cart');
            setLoading(false);
            return;
          }
        }
      }

      if (!cartId) {
        log('No cart ID available after cart creation/fetch attempts');
        setError('Failed to get cart ID');
        setLoading(false);
        return;
      }

      // Now add the item to the cart
      log(`Adding product ${productId} to cart ${cartId}`);

      const addItemUrl = `${CART_URL}${cartId}/add_item/`;
      log('Add item URL:', addItemUrl);

      const addItemData = {
        product_id: productId,
        quantity: quantity
      };
      log('Add item data:', addItemData);

      const res = await axios.post(addItemUrl, addItemData);
      log('Add to cart response:', res.data);

      if (res.data) {
        log('Setting cart with updated data', res.data);
        setCart(res.data);
        setError(null);
      } else {
        log('Invalid response data', res.data);
        setError('Invalid response from server');
      }
    } catch (err: any) {
      log('Error adding to cart', err);

      if (err.response) {
        log('Response error details', {
          status: err.response.status,
          data: err.response.data,
          url: err.config?.url
        });

        // Special handling for common errors
        if (err.response.status === 404) {
          setError('Product or cart not found');
        } else if (err.response.status === 401) {
          setError('You need to be logged in to add items to cart');
        } else {
          setError(err.response.data?.detail || `Error ${err.response.status}: Failed to add item to cart`);
        }
      } else if (err.request) {
        log('No response received', err.request);
        setError('No response from server. Please check your connection.');
      } else {
        log('Error setting up request', err.message);
        setError(`Request error: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Remove item from cart
  const removeFromCart = async (itemId: number) => {
    // Use a local error variable to avoid unnecessary state updates
    let localError = null;

    try {
      const cartId = cart?.id;
      if (!cartId) {
        setError('No cart available');
        return;
      }

      log(`Removing item ${itemId} from cart ${cartId}`);

      const res = await axios.post(`${CART_URL}${cartId}/remove_item/`, {
        item_id: itemId,
      });

      log('Remove from cart response', res.data);

      // Only update the cart state, not the loading state
      setCart(res.data);
      setError(null);
    } catch (err: any) {
      log('Error removing from cart', err);

      if (err.response) {
        localError = err.response.data?.detail || `Error ${err.response.status}: Failed to remove item`;
      } else if (err.request) {
        localError = 'No response from server. Please check your connection.';
      } else {
        localError = `Request error: ${err.message}`;
      }

      // Only set error if there is one, to avoid unnecessary re-renders
      if (localError) {
        setError(localError);
      }
    }
  };

  // Update cart item quantity
  const updateCartItem = async (itemId: number, quantity: number) => {
    // Use a local loading state to avoid full UI refreshes
    let localError = null;

    try {
      const cartId = cart?.id;
      if (!cartId) {
        setError('No cart available');
        return;
      }

      log(`Updating item ${itemId} in cart ${cartId} to quantity ${quantity}`);

      const res = await axios.post(`${CART_URL}${cartId}/update_item/`, {
        item_id: itemId,
        quantity,
      });

      log('Update cart response', res.data);

      // Only update the cart state, not the loading state
      setCart(res.data);
      setError(null);
    } catch (err: any) {
      log('Error updating cart', err);

      if (err.response) {
        localError = err.response.data?.detail || `Error ${err.response.status}: Failed to update item`;
      } else if (err.request) {
        localError = 'No response from server. Please check your connection.';
      } else {
        localError = `Request error: ${err.message}`;
      }

      // Only set error if there is one, to avoid unnecessary re-renders
      if (localError) {
        setError(localError);
      }
    }
  };

  // Clear cart
  const clearCart = async () => {
    setLoading(true);
    setError(null);

    try {
      const cartId = cart?.id;
      if (!cartId) {
        setError('No cart available');
        setLoading(false);
        return;
      }

      console.log(`Clearing cart ${cartId}`);

      const res = await axios.post(`${CART_URL}${cartId}/clear/`);

      console.log('Clear cart response:', res.data);
      setCart(res.data);
    } catch (err: any) {
      console.error('Error clearing cart:', err);

      if (err.response) {
        setError(err.response.data?.detail || `Error ${err.response.status}: Failed to clear cart`);
      } else if (err.request) {
        setError('No response from server. Please check your connection.');
      } else {
        setError(`Request error: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <CartContext.Provider
      value={{
        cart,
        loading,
        error,
        addToCart,
        removeFromCart,
        updateCartItem,
        clearCart,
        refetchCart: refetchCartData, // Use the new function
      }}
    >
      {children}
    </CartContext.Provider>
  );
};