#!/usr/bin/env python
"""
Debug Razorpay Order Creation
============================

This script tests the Razorpay order creation directly to identify the issue.
"""

import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'local_test_settings')
django.setup()

from orders.razorpay_utils import create_razorpay_order
from razorpay_settings.utils import get_razorpay_keys, initialize_razorpay_client
from decimal import Decimal

def debug_razorpay_order():
    """Debug Razorpay order creation step by step"""
    
    print("🔍 Debugging Razorpay Order Creation")
    print("=" * 50)
    
    try:
        # Step 1: Check Razorpay keys
        print("\n1️⃣ Checking Razorpay configuration...")
        key_id, key_secret = get_razorpay_keys()
        print(f"   Key ID: {key_id[:10]}..." if key_id else "   Key ID: None")
        print(f"   Key Secret: {'*' * len(key_secret) if key_secret else 'None'}")
        
        if not key_id or not key_secret:
            print("❌ Razorpay keys not configured properly")
            return False
        
        # Step 2: Test client initialization
        print("\n2️⃣ Testing Razorpay client initialization...")
        try:
            client = initialize_razorpay_client()
            print(f"✅ Client initialized successfully")
            print(f"   Auth: {client.auth[0][:10]}..." if client.auth else "   Auth: None")
        except Exception as e:
            print(f"❌ Client initialization failed: {str(e)}")
            return False
        
        # Step 3: Test simple order creation
        print("\n3️⃣ Testing simple order creation...")
        try:
            test_order_id = "test_order_123"
            test_amount = Decimal('10.00')  # ₹10
            
            print(f"   Creating order for: {test_amount} INR")
            print(f"   Order ID: {test_order_id}")
            
            razorpay_order = create_razorpay_order(test_order_id, test_amount)
            
            print("✅ Order created successfully!")
            print(f"   Razorpay Order ID: {razorpay_order.get('id')}")
            print(f"   Amount: {razorpay_order.get('amount')} paise")
            print(f"   Currency: {razorpay_order.get('currency')}")
            print(f"   Status: {razorpay_order.get('status')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Order creation failed: {str(e)}")
            print(f"   Error type: {type(e).__name__}")
            
            # Try to get more details
            import traceback
            print(f"   Full traceback:")
            traceback.print_exc()
            
            return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_razorpay():
    """Test Razorpay directly without our utils"""
    
    print("\n🧪 Testing Direct Razorpay Integration")
    print("=" * 50)
    
    try:
        import razorpay
        
        # Get keys from settings
        key_id = getattr(settings, 'RAZORPAY_KEY_ID', None)
        key_secret = getattr(settings, 'RAZORPAY_KEY_SECRET', None)
        
        print(f"Direct settings - Key ID: {key_id[:10]}..." if key_id else "Key ID: None")
        print(f"Direct settings - Key Secret: {'*' * len(key_secret) if key_secret else 'None'}")
        
        if not key_id or not key_secret:
            print("❌ Razorpay keys not found in settings")
            return False
        
        # Create client directly
        client = razorpay.Client(auth=(key_id, key_secret))
        print("✅ Direct client created")
        
        # Test order creation
        order_data = {
            'amount': 1000,  # ₹10 in paise
            'currency': 'INR',
            'receipt': 'test_receipt_123',
            'payment_capture': 1
        }
        
        print(f"Creating order with data: {order_data}")
        order = client.order.create(order_data)
        
        print("✅ Direct order creation successful!")
        print(f"   Order: {order}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct Razorpay test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 Starting Razorpay Debug Session")
    
    # Test our utils
    success1 = debug_razorpay_order()
    
    # Test direct Razorpay
    success2 = test_direct_razorpay()
    
    print("\n🎯 Debug Summary:")
    print(f"   Our Utils: {'✅ Working' if success1 else '❌ Failed'}")
    print(f"   Direct Razorpay: {'✅ Working' if success2 else '❌ Failed'}")
    
    if success1 and success2:
        print("\n🎉 Razorpay integration is working!")
        print("   The issue might be elsewhere in the token purchase flow.")
    elif success2 and not success1:
        print("\n🔧 Direct Razorpay works but our utils have issues.")
        print("   Check the razorpay_utils.py implementation.")
    else:
        print("\n❌ Razorpay integration has fundamental issues.")
        print("   Check credentials and network connectivity.")
