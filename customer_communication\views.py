from django.shortcuts import render, get_object_or_404
from django.conf import settings
from rest_framework import viewsets, permissions, status, generics
from rest_framework.response import Response
from rest_framework.decorators import action
from .models import SupportTicket, TicketResponse, CustomerFeedback
from .serializers import (
    SupportTicketSerializer,
    SupportTicketDetailSerializer,
    TicketResponseSerializer,
    CustomerFeedbackSerializer
)
from .tasks import send_support_ticket_confirmation, send_order_confirmation_email


class SupportTicketViewSet(viewsets.ModelViewSet):
    """
    API endpoint for support tickets.
    """
    serializer_class = SupportTicketSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        """
        Return tickets for the current user or all tickets for staff.
        """
        user = self.request.user
        if user.is_staff:
            return SupportTicket.objects.all()
        elif user.is_authenticated:
            return SupportTicket.objects.filter(user=user)
        return SupportTicket.objects.none()

    def get_serializer_class(self):
        """
        Return different serializers based on the action.
        """
        if self.action == 'retrieve':
            return SupportTicketDetailSerializer
        return SupportTicketSerializer

    def perform_create(self, serializer):
        """
        Create a new support ticket and send confirmation email.
        """
        ticket = serializer.save()

        # Send confirmation email asynchronously
        send_support_ticket_confirmation.delay(ticket.id)

    @action(detail=True, methods=['post'])
    def respond(self, request, pk=None):
        """
        Add a response to a support ticket.
        """
        ticket = self.get_object()
        serializer = TicketResponseSerializer(
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            serializer.save(ticket=ticket)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CustomerFeedbackViewSet(viewsets.ModelViewSet):
    """
    API endpoint for customer feedback.
    """
    serializer_class = CustomerFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        Return feedback for the current user or all feedback for staff.
        """
        user = self.request.user
        if user.is_staff:
            return CustomerFeedback.objects.all()
        return CustomerFeedback.objects.filter(user=user)


class SupportFormView(generics.CreateAPIView):
    """
    API endpoint for the support form.
    This endpoint is public and doesn't require authentication.
    """
    serializer_class = SupportTicketSerializer
    permission_classes = [permissions.AllowAny]

    def perform_create(self, serializer):
        """
        Create a new support ticket and send confirmation email.
        """
        ticket = serializer.save()

        # Send confirmation email asynchronously
        send_support_ticket_confirmation.delay(ticket.id)


def support_form_view(request):
    """
    Render the support form page.
    """
    return render(request, 'customer_communication/support_form.html', {
        'title': 'Help & Support',
        'description': 'Contact our support team for assistance.'
    })


def feedback_form_view(request, order_id):
    """
    Render the feedback form page.
    """
    return render(request, 'customer_communication/feedback_form.html', {
        'title': 'Order Feedback',
        'description': 'Share your feedback about your order.',
        'order_id': order_id
    })
