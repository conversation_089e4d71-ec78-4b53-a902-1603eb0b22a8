import React, { useState, useEffect } from 'react';
import { returnRequestAPI, CreateReturnRequest, ReturnEligibility } from '../../services/api';

interface ReturnRequestFormProps {
  orderId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const ReturnRequestForm: React.FC<ReturnRequestFormProps> = ({ orderId, onSuccess, onCancel }) => {
  const [eligibility, setEligibility] = useState<ReturnEligibility | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState<CreateReturnRequest>({
    order_id: orderId,
    reason: '',
    reason_detail: ''
  });
  const [errors, setErrors] = useState<any>({});

  const reasons = returnRequestAPI.getReturnReasons();

  useEffect(() => {
    checkEligibility();
  }, [orderId]);

  const checkEligibility = async () => {
    try {
      const response = await returnRequestAPI.checkReturnEligibility(orderId);
      setEligibility(response.data);
    } catch (error) {
      console.error('Error checking eligibility:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setErrors({});

    try {
      await returnRequestAPI.createReturnRequest(formData);
      onSuccess();
    } catch (error: any) {
      if (error.response?.data) {
        setErrors(error.response.data);
      } else {
        setErrors({ general: 'Failed to submit return request. Please try again.' });
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!eligibility?.can_return) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-red-800 mb-2">Cannot Request Return</h3>
        <p className="text-red-700">{eligibility?.reason}</p>
        <button
          onClick={onCancel}
          className="mt-4 px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
        >
          Close
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Request Return</h3>
      
      {errors.general && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-700">{errors.general}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
            Reason for Return *
          </label>
          <select
            id="reason"
            name="reason"
            value={formData.reason}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Select a reason</option>
            {reasons.map(reason => (
              <option key={reason.value} value={reason.value}>
                {reason.label}
              </option>
            ))}
          </select>
          {errors.reason && <p className="text-red-600 text-sm mt-1">{errors.reason}</p>}
        </div>

        <div>
          <label htmlFor="reason_detail" className="block text-sm font-medium text-gray-700 mb-1">
            Detailed Explanation *
          </label>
          <textarea
            id="reason_detail"
            name="reason_detail"
            value={formData.reason_detail}
            onChange={handleChange}
            required
            rows={4}
            placeholder="Please provide detailed information about the issue..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
          {errors.reason_detail && <p className="text-red-600 text-sm mt-1">{errors.reason_detail}</p>}
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-2">What happens next?</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• We'll review your return request within 24-48 hours</li>
            <li>• You'll receive an email with our decision</li>
            <li>• If approved, we'll provide return instructions</li>
            <li>• Refunds are processed within 5-10 business days after we receive the item</li>
          </ul>
        </div>

        <div className="flex space-x-4">
          <button
            type="submit"
            disabled={submitting}
            className="flex-1 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? 'Submitting...' : 'Submit Return Request'}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default ReturnRequestForm;
