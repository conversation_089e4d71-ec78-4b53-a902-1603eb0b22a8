"""
Signal handlers for service control.

These signals are used to perform actions when service statuses change.
"""
import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import ServiceControl

logger = logging.getLogger(__name__)


@receiver(post_save, sender=ServiceControl)
def service_status_changed(sender, instance, created, **kwargs):
    """
    Signal handler for when a ServiceControl instance is saved.
    
    This is called whenever a ServiceControl instance is created or updated.
    It logs the current status of services.
    
    Args:
        sender: The model class (ServiceControl)
        instance: The actual instance being saved
        created: Boolean; True if a new record was created
        **kwargs: Additional keyword arguments
    """
    celery_status = "enabled" if instance.celery_enabled else "disabled"
    redis_status = "enabled" if instance.redis_enabled else "disabled"
    
    if created:
        logger.info(f"Service Control created: Celery is {celery_status}, Redis is {redis_status}")
    else:
        logger.info(f"Service Control updated: Celery is {celery_status}, Redis is {redis_status}")
    
    # You could add additional logic here, such as:
    # - Sending notifications to administrators
    # - Updating system configurations
    # - Restarting services
