"""
Color Match Game API (New GameSession System)
============================================

Comprehensive API endpoints for Color Match game with token management using GameSession
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .game_session_service import GameSessionService
import json


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_color_match_game(request):
    """
    Start a new Color Match game with 2 token participation fee
    
    POST /api/gaming/color-match/start/
    {
        "difficulty": "medium"
    }
    """
    try:
        # Start game session using the comprehensive service
        result = GameSessionService.start_game_session(request.user, 'color_match')
        
        if result['success']:
            return Response({
                'success': True,
                'game_id': result['session_id'],
                'rounds': 5,  # 5 rounds total
                'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                'difficulty': result.get('difficulty', 'medium'),
                'can_play': True,
                'balance': result['balance'],
                'message': result['message'],
                'is_resume': result.get('is_resume', False)
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'success': False,
                'error': result['error'],
                'can_play': False,
                'balance': result.get('balance', 0)
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_color_match_game(request):
    """
    Complete a Color Match game with result and token calculation
    
    POST /api/gaming/color-match/complete/
    {
        "game_id": "uuid",
        "result": "win|loss|draw",
        "rounds": [
            {"target_color": "red", "player_choice": "red", "bot_choice": "blue", "winner": "player"},
            {"target_color": "blue", "player_choice": "green", "bot_choice": "blue", "winner": "bot"}
        ],
        "final_score": {"player": 3, "bot": 2},
        "duration": 120
    }
    """
    try:
        game_id = request.data.get('game_id')
        result = request.data.get('result')
        
        if not game_id or not result:
            return Response({
                'error': 'Missing game_id or result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if result not in ['win', 'loss', 'draw']:
            return Response({
                'error': 'Invalid game result. Must be win, loss, or draw',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Prepare game data
        game_data = {
            'rounds': request.data.get('rounds', []),
            'final_score': request.data.get('final_score', {}),
            'duration': request.data.get('duration', 0),
            'max_rounds': 5
        }

        # Complete game session
        completion_result = GameSessionService.complete_game_session(game_id, result, game_data)

        if completion_result['success']:
            return Response({
                'success': True,
                'tokens_earned': completion_result['tokens_change'],
                'new_balance': completion_result['new_balance'],
                'balance_in_inr': completion_result['new_balance'] * 0.1,
                'transaction_type': f'color_match_{result}',
                'description': completion_result['message'],
                'can_play_more': completion_result['new_balance'] >= 2,
                'status': completion_result['status'],
                'result': completion_result['result']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': completion_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def forfeit_color_match_game(request):
    """
    Forfeit a Color Match game
    
    POST /api/gaming/color-match/forfeit/
    {
        "game_id": "uuid"
    }
    """
    try:
        game_id = request.data.get('game_id')
        
        if not game_id:
            return Response({
                'error': 'Missing game_id',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Forfeit game session
        forfeit_result = GameSessionService.forfeit_game_session(game_id)

        if forfeit_result['success']:
            return Response({
                'success': True,
                'tokens_earned': forfeit_result['tokens_change'],
                'new_balance': forfeit_result['new_balance'],
                'balance_in_inr': forfeit_result['new_balance'] * 0.1,
                'transaction_type': 'color_match_forfeit',
                'description': forfeit_result['message'],
                'can_play_more': forfeit_result['new_balance'] >= 2
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': forfeit_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_color_match_stats(request):
    """
    Get Color Match game statistics for the user
    
    GET /api/gaming/color-match/stats/
    """
    try:
        stats_result = GameSessionService.get_user_game_stats(request.user, 'color_match')
        
        if stats_result['success']:
            return Response({
                'success': True,
                'stats': stats_result['stats']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': stats_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_color_match_session(request, game_id):
    """
    Get Color Match game session details
    
    GET /api/gaming/color-match/session/<game_id>/
    """
    try:
        session_result = GameSessionService.get_session_details(game_id, request.user)
        
        if session_result['success']:
            return Response({
                'success': True,
                'session': session_result['session']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': session_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
