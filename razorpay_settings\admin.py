from django.contrib import admin
from django.utils.html import format_html
from .models import RazorpaySettings


@admin.register(RazorpaySettings)
class RazorpaySettingsAdmin(admin.ModelAdmin):
    """
    Admin configuration for RazorpaySettings model.

    Provides a clean interface to manage Razorpay settings with:
    - A toggle for switching between Test and Live modes
    - Secure display of API keys
    - Custom fieldsets for better organization
    """
    list_display = ['__str__', 'mode_display', 'current_key_id', 'created_at', 'updated_at']
    readonly_fields = ['created_at', 'updated_at', 'mode_display', 'current_key_id']
    fieldsets = (
        ('Mode Settings', {
            'fields': ('is_live', 'mode_display'),
            'description': 'Toggle between Test and Live modes. Be careful when switching to Live mode!'
        }),
        ('Test Mode Credentials', {
            'fields': ('test_key_id', 'test_key_secret'),
            'description': 'API credentials for Test mode (safe for testing)'
        }),
        ('Live Mode Credentials', {
            'fields': ('live_key_id', 'live_key_secret'),
            'description': 'API credentials for Live mode (handles real transactions)',
            'classes': ('collapse',)  # Collapsed by default for safety
        }),
        ('Current Active Credentials', {
            'fields': ('current_key_id',),
            'description': 'These are the credentials currently being used based on the selected mode'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        """Only allow adding if no instance exists."""
        return not RazorpaySettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of the settings instance."""
        return False

    def mode_display(self, obj):
        """Display the current mode with color coding."""
        if obj.is_live:
            return format_html(
                '<span style="color: #d35400; font-weight: bold;">LIVE MODE</span> '
                '<span style="color: red;">(Real transactions)</span>'
            )
        return format_html(
            '<span style="color: #27ae60; font-weight: bold;">TEST MODE</span> '
            '<span style="color: #2980b9;">(Safe for testing)</span>'
        )
    mode_display.short_description = "Current Mode"

    def current_key_id(self, obj):
        """Display the currently active key ID based on the mode."""
        key_id, _ = obj.get_keys()
        if obj.is_live:
            return format_html(
                '<span style="color: #d35400;">{}</span>', key_id
            )
        return format_html(
            '<span style="color: #27ae60;">{}</span>', key_id
        )
    current_key_id.short_description = "Active Key ID"
