"""
Game Session API
===============

Comprehensive API endpoints for managing game sessions and tokens
for all 5 games with exact token rules implementation.
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from wallet.models import Wallet
from .game_session_service import GameSessionService
from .models import GameSession
import json


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_game_session(request):
    """
    Start a new game session with 2 token participation fee
    
    POST /api/gaming/session/start/
    {
        "game_type": "tic_tac_toe|color_match|memory_card|number_guessing|rock_paper_scissors"
    }
    """
    try:
        game_type = request.data.get('game_type')
        
        if not game_type:
            return Response({
                'success': False,
                'error': 'game_type is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        valid_games = ['tic_tac_toe', 'color_match', 'memory_card', 'number_guessing', 'rock_paper_scissors']
        if game_type not in valid_games:
            return Response({
                'success': False,
                'error': f'Invalid game_type. Must be one of: {", ".join(valid_games)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Start game session
        result = GameSessionService.start_game_session(request.user, game_type)
        
        if result['success']:
            return Response(result, status=status.HTTP_201_CREATED)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_game_session(request):
    """
    Complete a game session with result
    
    POST /api/gaming/session/complete/
    {
        "session_id": "uuid",
        "result": "win|loss|draw|forfeit",
        "game_data": {
            "moves": [...],
            "score": 100,
            "duration": 120
        }
    }
    """
    try:
        session_id = request.data.get('session_id')
        result = request.data.get('result')
        game_data = request.data.get('game_data', {})
        
        if not session_id:
            return Response({
                'success': False,
                'error': 'session_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not result:
            return Response({
                'success': False,
                'error': 'result is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        valid_results = ['win', 'loss', 'draw', 'forfeit']
        if result not in valid_results:
            return Response({
                'success': False,
                'error': f'Invalid result. Must be one of: {", ".join(valid_results)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Complete game session
        completion_result = GameSessionService.complete_game_session(session_id, result, game_data)
        
        if completion_result['success']:
            return Response(completion_result, status=status.HTTP_200_OK)
        else:
            return Response(completion_result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def forfeit_game_session(request):
    """
    Forfeit an active game session
    
    POST /api/gaming/session/forfeit/
    {
        "session_id": "uuid"
    }
    """
    try:
        session_id = request.data.get('session_id')
        
        if not session_id:
            return Response({
                'success': False,
                'error': 'session_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Forfeit game session
        result = GameSessionService.forfeit_game_session(session_id)
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_session_status(request, session_id):
    """
    Get status of a specific game session
    
    GET /api/gaming/session/{session_id}/
    """
    try:
        result = GameSessionService.get_session_status(session_id)
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_active_sessions(request):
    """
    Get all active or pending replay sessions for the user
    
    GET /api/gaming/session/active/
    """
    try:
        sessions = GameSessionService.get_user_active_sessions(request.user)
        
        return Response({
            'success': True,
            'sessions': sessions,
            'count': len(sessions)
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_game_history(request):
    """
    Get user's game history
    
    GET /api/gaming/session/history/?game_type=tic_tac_toe&limit=20
    """
    try:
        game_type = request.GET.get('game_type')
        limit = int(request.GET.get('limit', 10))
        
        if limit > 100:
            limit = 100  # Cap at 100 records
        
        history = GameSessionService.get_user_game_history(
            request.user, 
            game_type=game_type, 
            limit=limit
        )
        
        return Response({
            'success': True,
            'history': history,
            'count': len(history)
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_stats(request):
    """
    Get comprehensive user gaming statistics
    
    GET /api/gaming/session/stats/
    """
    try:
        user = request.user
        
        # Get wallet info
        try:
            wallet = Wallet.objects.get(user=user)
            current_balance = wallet.balance
        except Wallet.DoesNotExist:
            current_balance = 0
        
        # Get session statistics
        total_sessions = GameSession.objects.filter(user=user).count()
        completed_sessions = GameSession.objects.filter(user=user, status='completed').count()
        
        # Get results breakdown
        wins = GameSession.objects.filter(user=user, result='win').count()
        losses = GameSession.objects.filter(user=user, result='loss').count()
        draws = GameSession.objects.filter(user=user, result='draw').count()
        forfeits = GameSession.objects.filter(user=user, result='forfeit').count()
        
        # Calculate total tokens earned/lost
        completed_games = GameSession.objects.filter(
            user=user, 
            status__in=['completed', 'incomplete']
        )
        
        total_tokens_earned = sum(
            session.tokens_awarded for session in completed_games 
            if session.tokens_awarded > 0
        )
        
        total_tokens_spent = sum(
            abs(session.net_token_change) for session in completed_games 
            if session.net_token_change < 0
        )
        
        # Game type breakdown
        game_breakdown = {}
        for game_type, display_name in GameSession.GAME_TYPES:
            game_sessions = GameSession.objects.filter(user=user, game_type=game_type)
            game_breakdown[game_type] = {
                'display_name': display_name,
                'total_games': game_sessions.count(),
                'wins': game_sessions.filter(result='win').count(),
                'losses': game_sessions.filter(result='loss').count(),
                'draws': game_sessions.filter(result='draw').count(),
                'forfeits': game_sessions.filter(result='forfeit').count(),
            }
        
        return Response({
            'success': True,
            'stats': {
                'current_balance': current_balance,
                'total_sessions': total_sessions,
                'completed_sessions': completed_sessions,
                'wins': wins,
                'losses': losses,
                'draws': draws,
                'forfeits': forfeits,
                'win_rate': round((wins / completed_sessions * 100), 2) if completed_sessions > 0 else 0,
                'total_tokens_earned': total_tokens_earned,
                'total_tokens_spent': total_tokens_spent,
                'net_tokens': total_tokens_earned - total_tokens_spent,
                'game_breakdown': game_breakdown
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cleanup_abandoned_sessions(request):
    """
    Admin endpoint to cleanup abandoned sessions
    
    POST /api/gaming/session/cleanup/
    """
    try:
        # Only allow staff users
        if not request.user.is_staff:
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        count = GameSessionService.cleanup_abandoned_sessions()
        
        return Response({
            'success': True,
            'message': f'Cleaned up {count} abandoned sessions'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
