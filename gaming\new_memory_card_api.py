"""
Memory Card Game API (New GameSession System)
============================================

Comprehensive API endpoints for Memory Card game with token management using GameSession
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .game_session_service import GameSessionService
import json


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_memory_card_game(request):
    """
    Start a new Memory Card game with 2 token participation fee
    
    POST /api/gaming/memory-card/start/
    {
        "difficulty": "medium"
    }
    """
    try:
        # Start game session using the comprehensive service
        result = GameSessionService.start_game_session(request.user, 'memory_card')
        
        if result['success']:
            return Response({
                'success': True,
                'game_id': result['session_id'],
                'cards': 16,  # 16 cards total (8 pairs)
                'pairs': 8,   # 8 pairs to match
                'difficulty': result.get('difficulty', 'medium'),
                'can_play': True,
                'balance': result['balance'],
                'message': result['message'],
                'is_resume': result.get('is_resume', False)
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'success': False,
                'error': result['error'],
                'can_play': False,
                'balance': result.get('balance', 0)
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_memory_card_game(request):
    """
    Complete a Memory Card game with result and token calculation
    
    POST /api/gaming/memory-card/complete/
    {
        "game_id": "uuid",
        "result": "win|loss|draw",
        "matches": {
            "player": 4,
            "bot": 4
        },
        "turns": 12,
        "duration": 180
    }
    """
    try:
        game_id = request.data.get('game_id')
        result = request.data.get('result')
        
        if not game_id or not result:
            return Response({
                'error': 'Missing game_id or result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if result not in ['win', 'loss', 'draw']:
            return Response({
                'error': 'Invalid game result. Must be win, loss, or draw',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Prepare game data
        game_data = {
            'matches': request.data.get('matches', {}),
            'turns': request.data.get('turns', 0),
            'duration': request.data.get('duration', 0),
            'total_pairs': 8
        }

        # Complete game session
        completion_result = GameSessionService.complete_game_session(game_id, result, game_data)

        if completion_result['success']:
            return Response({
                'success': True,
                'tokens_earned': completion_result['tokens_change'],
                'new_balance': completion_result['new_balance'],
                'balance_in_inr': completion_result['new_balance'] * 0.1,
                'transaction_type': f'memory_card_{result}',
                'description': completion_result['message'],
                'can_play_more': completion_result['new_balance'] >= 2,
                'status': completion_result['status'],
                'result': completion_result['result']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': completion_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def forfeit_memory_card_game(request):
    """
    Forfeit a Memory Card game
    
    POST /api/gaming/memory-card/forfeit/
    {
        "game_id": "uuid"
    }
    """
    try:
        game_id = request.data.get('game_id')
        
        if not game_id:
            return Response({
                'error': 'Missing game_id',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Forfeit game session
        forfeit_result = GameSessionService.forfeit_game_session(game_id)

        if forfeit_result['success']:
            return Response({
                'success': True,
                'tokens_earned': forfeit_result['tokens_change'],
                'new_balance': forfeit_result['new_balance'],
                'balance_in_inr': forfeit_result['new_balance'] * 0.1,
                'transaction_type': 'memory_card_forfeit',
                'description': forfeit_result['message'],
                'can_play_more': forfeit_result['new_balance'] >= 2
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': forfeit_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_memory_card_stats(request):
    """
    Get Memory Card game statistics for the user
    
    GET /api/gaming/memory-card/stats/
    """
    try:
        stats_result = GameSessionService.get_user_game_stats(request.user, 'memory_card')
        
        if stats_result['success']:
            return Response({
                'success': True,
                'stats': stats_result['stats']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': stats_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_memory_card_session(request, game_id):
    """
    Get Memory Card game session details
    
    GET /api/gaming/memory-card/session/<game_id>/
    """
    try:
        session_result = GameSessionService.get_session_details(game_id, request.user)
        
        if session_result['success']:
            return Response({
                'success': True,
                'session': session_result['session']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': session_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
