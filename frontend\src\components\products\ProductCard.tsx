import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import { useAuth } from '../../contexts/AuthContext';
import { formatINR } from '../../utils/currencyFormatter';
import { getImageUrl, handleImageError, testImageUrl } from '../../utils/imageUtils';
import ShareProduct from '../ui/ShareProduct';
import TokenBadge from '../TokenBadge';

interface ProductProps {
  id: string;
  name: string;
  slug: string;
  price: number;
  comparePrice?: number;
  stock: number;
  mainImage?: {
    id: number;
    image: string;
    alt_text?: string;
  };
  isFeatured?: boolean;
  token_discount_available?: boolean;
  token_discount_info?: {
    percentage: number;
    max_amount?: number;
    max_discount_for_quantity_1?: {
      max_tokens: number;
      max_inr_discount: number;
      discount_percentage: number;
    };
  };
}

const ProductCard: React.FC<ProductProps> = ({
  id,
  name,
  slug,
  price,
  comparePrice,
  stock,
  mainImage,
  isFeatured,
  token_discount_available,
  token_discount_info,
}) => {
  const [imageError, setImageError] = useState(false);
  const [addingToCart, setAddingToCart] = useState(false);
  const [addToCartError, setAddToCartError] = useState<string | null>(null);
  const [addToCartSuccess, setAddToCartSuccess] = useState(false);
  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Calculate discount percentage
  const discountPercentage = comparePrice && price < comparePrice
    ? Math.round(((comparePrice - price) / comparePrice) * 100)
    : 0;

  // Handle adding to cart
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Reset states
    setAddToCartError(null);
    setAddToCartSuccess(false);

    if (stock <= 0) return;

    // Check if user is authenticated
    if (!isAuthenticated) {
      // Redirect to login page with return URL
      const returnUrl = window.location.pathname;
      navigate(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
      return;
    }

    try {
      setAddingToCart(true);
      // Since we don't have variant information in the ProductCard,
      // we'll pass undefined for the variant_id, which will add the default variant
      await addToCart(id, 1);
      setAddToCartSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setAddToCartSuccess(false);
      }, 3000);
    } catch (err: any) {
      console.error('Failed to add product to cart:', err);
      setAddToCartError(err.message || 'Failed to add product to cart');

      // Hide error message after 3 seconds
      setTimeout(() => {
        setAddToCartError(null);
      }, 3000);
    } finally {
      setAddingToCart(false);
    }
  };

  // Add image validation on component mount
  useEffect(() => {
    if (mainImage?.image) {
      // Validate image URL in development mode
      if (process.env.NODE_ENV === 'development') {
        testImageUrl(mainImage.image).then(isValid => {
          if (!isValid) {
            console.warn(`⚠️ Image validation failed for product: ${name}`);
            console.warn(`Image URL: ${mainImage.image}`);
          }
        });
      }
    }
  }, [mainImage?.image, name]);

  return (
    <div className="group relative bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
      {/* Product Image */}
      <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200 group-hover:opacity-80">
        <Link to={`/product/${slug}`}>
          {mainImage && mainImage.image && !imageError ? (
            <img
              src={getImageUrl(mainImage.image)}
              alt={mainImage.alt_text || name}
              className="w-full h-64 object-cover object-center"
              onLoad={() => {
                console.log(`✅ Image loaded successfully for ${name}`);
              }}
              onError={(e) => {
                handleImageError(e, mainImage.image, `ProductCard-${name}`);
                setImageError(true);
                const target = e.target as HTMLImageElement;
                target.onerror = null; // Prevent infinite loops
                target.src = 'https://via.placeholder.com/400x400?text=No+Image';
              }}
            />
          ) : (
            <div className="w-full h-64 bg-gray-200 flex items-center justify-center">
              <span className="text-gray-400 text-sm text-center px-4">
                {!mainImage
                  ? 'No image data available'
                  : !mainImage.image
                    ? 'Empty image URL'
                    : 'Image failed to load'}
              </span>
            </div>
          )}
        </Link>
      </div>

      {/* Product Info */}
      <div className="p-4">
        <Link to={`/product/${slug}`} className="block">
          <h3 className="text-sm font-medium text-gray-900 truncate">{name}</h3>
        </Link>

        {/* Price */}
        <div className="mt-2 flex items-center space-x-2">
          {comparePrice && price < comparePrice ? (
            <>
              {/* Discounted Price - Blue color as requested */}
              <p className="text-lg font-bold text-primary-600">{formatINR(price)}</p>
              {/* Original Price - Crossed out */}
              <p className="text-sm text-gray-500 line-through">{formatINR(comparePrice)}</p>
            </>
          ) : (
            /* Regular Price */
            <p className="text-lg font-semibold text-gray-900">{formatINR(price)}</p>
          )}
        </div>

        {/* Discount Badge */}
        {discountPercentage > 0 && (
          <div className="absolute top-0 right-0 mt-2 mr-2 bg-red-600 text-white px-2 py-1 text-xs font-bold rounded">
            Save {discountPercentage}%
          </div>
        )}

        {/* Featured Badge */}
        {isFeatured && (
          <div className="absolute top-0 left-0 mt-2 ml-2 bg-primary-600 text-white px-2 py-1 text-xs font-bold rounded">
            Featured
          </div>
        )}

        {/* Token Discount Badge */}
        {token_discount_available && token_discount_info && (
          <div className="absolute bottom-2 left-2">
            <TokenBadge
              product={{
                token_discount_available,
                token_discount_info
              }}
              size="small"
              showPercentage={true}
              showIcon={true}
            />
          </div>
        )}

        {/* Stock Status and Share */}
        <div className="mt-2 flex items-center justify-between">
          <div>
            {stock > 0 ? (
              <span className="text-xs text-green-600">In Stock</span>
            ) : (
              <span className="text-xs text-red-600">Out of Stock</span>
            )}
          </div>

          {/* Compact Share Button */}
          <ShareProduct
            productName={name}
            productUrl={`/product/${slug}`}
            productPrice={formatINR(price)}
            productImage={mainImage ? getImageUrl(mainImage.image) : undefined}
            className="scale-75 origin-right"
          />
        </div>

        {/* Buttons */}
        <div className="mt-4 grid grid-cols-2 gap-2">
          <Link
            to={`/product/${slug}`}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md text-sm font-medium text-center inline-block"
          >
            View Details
          </Link>

          <button
            onClick={handleAddToCart}
            disabled={stock <= 0 || addingToCart}
            className={`${
              stock <= 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-primary-600 hover:bg-primary-700'
            } text-white py-2 px-4 rounded-md text-sm font-medium text-center inline-block disabled:opacity-70`}
          >
            {addingToCart ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Adding...
              </span>
            ) : stock <= 0 ? (
              'Out of Stock'
            ) : (
              'Add to Cart'
            )}
          </button>
        </div>

        {/* Success message */}
        {addToCartSuccess && (
          <div className="mt-2 bg-green-50 border border-green-200 text-green-800 px-3 py-2 rounded-md text-xs">
            Product added to cart successfully!
          </div>
        )}

        {/* Error message */}
        {addToCartError && (
          <div className="mt-2 bg-red-50 border border-red-200 text-red-800 px-3 py-2 rounded-md text-xs">
            {addToCartError}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductCard;