import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { api } from '../services/api';
import { formatINR } from '../utils/currencyFormatter';

interface OrderItem {
  id: number;
  product_id: number;
  product_name: string;
  product_image?: string;
  quantity?: number;
  price?: number;
}

interface ShippingAddress {
  first_name: string;
  last_name: string;
  email: string;
  address: string;
  city: string;
  postal_code: string;
  country: string;
}

interface Order {
  id: number;
  order_number: string;
  status: string;
  created_at: string;
  items?: OrderItem[];
  shipping_address?: ShippingAddress;
  payment_method: string;
  subtotal?: number;
  shipping_cost?: number;
  tax?: number;
  total?: number;
  tracking_number?: string;
}

// Helper function to safely format currency values
const formatCurrency = (value: any): string => {
  if (value === null || value === undefined) {
    return '0.00';
  }

  try {
    // Convert to string first to handle any type
    const numValue = parseFloat(String(value).replace(/[^0-9.-]+/g, ''));
    // Check if it's a valid number
    return isNaN(numValue) ? '0.00' : numValue.toFixed(2);
  } catch (e) {
    console.error('Error formatting currency value:', value, e);
    return '0.00';
  }
};

const OrderDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        const response = await api.get(`/api/orders/${id}/`);
        setOrder(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load order details');
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [id]);

  if (loading) {
    return <div className="container mx-auto p-4">Loading order details...</div>;
  }

  if (error) {
    return <div className="container mx-auto p-4 text-red-500">{error}</div>;
  }

  if (!order) {
    return <div className="container mx-auto p-4">Order not found</div>;
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <Link to="/orders" className="text-blue-600 hover:underline">
          ← Back to Orders
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Order #{order.order_number}</h1>
        <span className={`px-3 py-1 rounded-full ${getStatusColor(order.status)}`}>
          {order.status}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Order Information</h2>
          <p className="mb-2">
            <span className="font-medium">Order Date:</span>{' '}
            {new Date(order.created_at).toLocaleDateString()}
          </p>
          <p className="mb-2">
            <span className="font-medium">Order Status:</span> {order.status}
          </p>
          <p className="mb-2">
            <span className="font-medium">Payment Method:</span> {order.payment_method}
          </p>
          {order.tracking_number && (
            <p className="mb-2">
              <span className="font-medium">Tracking Number:</span> {order.tracking_number}
            </p>
          )}
          <div className="mt-4">
            <Link
              to={`/order/${order.id}/tracking`}
              className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Track Order
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Shipping Address</h2>
          {order.shipping_address ? (
            <>
              <p className="mb-2">{order.shipping_address.first_name} {order.shipping_address.last_name}</p>
              <p className="mb-2">{order.shipping_address.email}</p>
              <p className="mb-2">{order.shipping_address.address}</p>
              <p className="mb-2">
                {order.shipping_address.city}, {order.shipping_address.postal_code}
              </p>
              <p className="mb-2">{order.shipping_address.country}</p>
            </>
          ) : (
            <p className="mb-2">No shipping address available</p>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
          <div className="flex justify-between mb-2">
            <span>Subtotal:</span>
            <span>{formatINR(order.subtotal)}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span>Shipping:</span>
            <span>{formatINR(order.shipping_cost)}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span>Tax:</span>
            <span>{formatINR(order.tax)}</span>
          </div>
          <div className="flex justify-between font-bold text-lg pt-2 border-t">
            <span>Total:</span>
            <span>{formatINR(order.total)}</span>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Order Items</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {order.items && order.items.length > 0 ? (
                order.items.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-14 w-14">
                          <img
                            className="h-14 w-14 object-cover rounded"
                            src={item.product_image || 'https://via.placeholder.com/50'}
                            alt={item.product_name}
                          />
                        </div>
                        <div className="ml-4">
                          <Link
                            to={`/product/${item.product_id}`}
                            className="text-blue-600 hover:underline"
                          >
                            {item.product_name}
                          </Link>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">{formatINR(item.price)}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{item.quantity || 1}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {formatINR((item.price || 0) * (item.quantity || 1))}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                    No items in this order
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default OrderDetail;