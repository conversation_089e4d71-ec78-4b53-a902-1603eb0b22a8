import React, { useState } from 'react';
import TokenPurchaseModal from './TokenPurchaseModal';

interface TokenPurchaseButtonProps {
  currentBalance: number;
  onTokensAdded: (tokensAdded: number) => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  children?: React.ReactNode;
  className?: string;
}

const TokenPurchaseButton: React.FC<TokenPurchaseButtonProps> = ({
  currentBalance,
  onTokensAdded,
  variant = 'primary',
  size = 'md',
  showIcon = true,
  children,
  className = ''
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSuccess = (tokensAdded: number) => {
    onTokensAdded(tokensAdded);
    // You could also show a success toast here
  };

  // Define button styles based on variant and size
  const getButtonStyles = () => {
    const baseStyles = 'font-semibold rounded-xl transition-all duration-200 flex items-center justify-center';
    
    const variantStyles = {
      primary: 'bg-gradient-to-r from-emerald-600 to-teal-600 text-white hover:from-emerald-700 hover:to-teal-700 shadow-lg hover:shadow-xl',
      secondary: 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl',
      outline: 'border-2 border-emerald-600 text-emerald-600 hover:bg-emerald-600 hover:text-white'
    };

    const sizeStyles = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-6 py-4 text-lg'
    };

    return `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`;
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 'w-4 h-4';
      case 'md': return 'w-5 h-5';
      case 'lg': return 'w-6 h-6';
      default: return 'w-5 h-5';
    }
  };

  return (
    <>
      <button
        onClick={() => {
          console.log('🪙 TokenPurchaseButton clicked, opening modal...');
          setIsModalOpen(true);
        }}
        className={getButtonStyles()}
      >
        {showIcon && (
          <svg 
            className={`${getIconSize()} mr-2`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 6v6m0 0v6m0-6h6m-6 0H6" 
            />
          </svg>
        )}
        {children || 'Buy Tokens'}
      </button>

      <TokenPurchaseModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleSuccess}
        currentBalance={currentBalance}
      />
    </>
  );
};

export default TokenPurchaseButton;
