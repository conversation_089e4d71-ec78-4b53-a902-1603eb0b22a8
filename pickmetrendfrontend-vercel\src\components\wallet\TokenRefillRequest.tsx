import React, { useState, useEffect } from 'react';
import { api } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface TokenRefillRequestProps {
  onRequestSubmitted?: () => void;
  className?: string;
}

interface RefillEligibility {
  can_request: boolean;
  message: string;
  current_balance: number;
  pending_requests: number;
}

const TokenRefillRequest: React.FC<TokenRefillRequestProps> = ({ 
  onRequestSubmitted, 
  className = '' 
}) => {
  const { user, isAuthenticated } = useAuth();
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [eligibility, setEligibility] = useState<RefillEligibility | null>(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (isAuthenticated) {
      checkEligibility();
    }
  }, [isAuthenticated]);

  const checkEligibility = async () => {
    try {
      const response = await api.post('/api/wallet/check-refill-eligibility/');
      setEligibility(response.data);
    } catch (err: any) {
      console.error('Error checking eligibility:', err);
      setError('Failed to check eligibility');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!eligibility?.can_request) {
      setError(eligibility?.message || 'Cannot request refill at this time');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await api.post('/api/wallet/token-requests/', {
        message: message.trim() || undefined
      });

      setSuccess('Token refill request submitted successfully! Our team will review it shortly.');
      setMessage('');
      
      // Refresh eligibility
      await checkEligibility();
      
      if (onRequestSubmitted) {
        onRequestSubmitted();
      }
    } catch (err: any) {
      console.error('Error submitting request:', err);
      setError(err.response?.data?.message || 'Failed to submit request');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className={`text-center p-4 ${className}`}>
        <p className="text-gray-600">Please log in to request token refills</p>
      </div>
    );
  }

  if (!eligibility) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          🪙 Request Token Refill
        </h3>
        <div className="text-sm text-gray-600 space-y-1">
          <p>Current Balance: <span className="font-medium">{eligibility.current_balance} tokens</span></p>
          {eligibility.pending_requests > 0 && (
            <p>Pending Requests: <span className="font-medium text-orange-600">{eligibility.pending_requests}</span></p>
          )}
        </div>
      </div>

      {!eligibility.can_request ? (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-yellow-500 text-xl mr-2">⚠️</span>
            <div>
              <p className="text-yellow-800 font-medium">Cannot Request Refill</p>
              <p className="text-yellow-700 text-sm">{eligibility.message}</p>
            </div>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
              Message (Optional)
            </label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Tell us why you need a token refill (optional)"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              rows={3}
              maxLength={500}
            />
            <p className="text-xs text-gray-500 mt-1">
              {message.length}/500 characters
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-green-800 text-sm">{success}</p>
            </div>
          )}

          <button
            type="submit"
            disabled={loading || !eligibility.can_request}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
              </span>
            ) : (
              'Submit Token Refill Request'
            )}
          </button>
        </form>
      )}

      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">💡 How Token Refills Work</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• You can only request refills when your balance is 0</li>
          <li>• Our team reviews requests manually</li>
          <li>• Typical refill amount is 50 tokens</li>
          <li>• Processing usually takes 24-48 hours</li>
          <li>• You'll be notified when your request is processed</li>
        </ul>
      </div>
    </div>
  );
};

export default TokenRefillRequest;
