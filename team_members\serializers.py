from rest_framework import serializers
from .models import TeamMember

class TeamMemberSerializer(serializers.ModelSerializer):
    """
    Serializer for the TeamMember model.
    
    Provides serialization for team member data with additional fields
    for the photo URL.
    """
    photo_url = serializers.SerializerMethodField()
    
    class Meta:
        model = TeamMember
        fields = [
            'id', 'name', 'role', 'photo_url', 'linkedin_url', 'order'
        ]
    
    def get_photo_url(self, obj):
        """Get the URL for the team member's photo."""
        if obj.photo:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.photo.url)
            return obj.photo.url
        return None
