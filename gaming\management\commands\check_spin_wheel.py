from django.core.management.base import BaseCommand
from gaming.models import SpinWheelReward, SpinWheelSettings, SpinWheelHistory
from django.contrib.auth.models import User
from django.test import RequestFactory
from rest_framework.test import force_authenticate


class Command(BaseCommand):
    help = 'Check and diagnose Spin Wheel setup in production'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Automatically fix issues found',
        )
        parser.add_argument(
            '--test-api',
            action='store_true',
            help='Test the API endpoint',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🎡 SPIN WHEEL DIAGNOSTIC'))
        self.stdout.write('=' * 50)
        
        # Check database setup
        issues_found = self.check_database()
        
        # Fix issues if requested
        if options['fix'] and issues_found:
            self.stdout.write('\n🔧 FIXING ISSUES...')
            self.fix_issues()
            
            # Re-check after fixes
            self.stdout.write('\n🔄 RE-CHECKING AFTER FIXES...')
            issues_found = self.check_database()
        
        # Test API if requested
        if options['test_api']:
            self.test_api_endpoint()
        
        # Final summary
        self.stdout.write('\n' + '=' * 50)
        if not issues_found:
            self.stdout.write(self.style.SUCCESS('🎉 SPIN WHEEL IS READY!'))
            self.stdout.write('No issues found. The 500 errors should be resolved.')
        else:
            self.stdout.write(self.style.ERROR('⚠️ ISSUES FOUND!'))
            self.stdout.write('Run with --fix to automatically resolve issues.')
            self.stdout.write('Or run: python manage.py setup_spin_wheel')

    def check_database(self):
        """Check database setup and return True if issues found"""
        issues_found = False
        
        # Check settings
        settings_count = SpinWheelSettings.objects.count()
        self.stdout.write(f'📊 Settings records: {settings_count}')
        
        if settings_count == 0:
            self.stdout.write(self.style.ERROR('❌ No settings found!'))
            issues_found = True
        else:
            settings = SpinWheelSettings.objects.first()
            self.stdout.write(f'✅ Settings found - Active: {settings.is_active}')
        
        # Check rewards
        rewards_count = SpinWheelReward.objects.count()
        active_rewards = SpinWheelReward.objects.filter(is_active=True).count()
        self.stdout.write(f'📊 Rewards: {rewards_count} total, {active_rewards} active')
        
        if active_rewards == 0:
            self.stdout.write(self.style.ERROR('❌ No active rewards found!'))
            issues_found = True
        else:
            self.stdout.write('✅ Active rewards found')
            
            # Check probability sum
            total_prob = sum(r.probability for r in SpinWheelReward.objects.filter(is_active=True))
            self.stdout.write(f'📈 Total probability: {total_prob:.3f}')
            if abs(total_prob - 1.0) > 0.01:
                self.stdout.write(self.style.WARNING(f'⚠️ Probability sum should be close to 1.0'))
        
        # Check history
        history_count = SpinWheelHistory.objects.count()
        self.stdout.write(f'📊 Spin history records: {history_count}')
        
        # Test API components
        try:
            from gaming.spin_wheel_api import _check_database_setup
            db_ok, db_message = _check_database_setup()
            
            if db_ok:
                self.stdout.write('✅ API database check: PASS')
            else:
                self.stdout.write(self.style.ERROR(f'❌ API database check: FAIL - {db_message}'))
                issues_found = True
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ API component error: {str(e)}'))
            issues_found = True
        
        return issues_found

    def fix_issues(self):
        """Automatically fix common issues"""
        try:
            # Create settings if missing
            if SpinWheelSettings.objects.count() == 0:
                settings = SpinWheelSettings.objects.create(
                    cooldown_hours=24,
                    wheel_segments=8,
                    animation_duration=3000,
                    min_token_reward=1,
                    max_token_reward=50,
                    scratch_card_probability=0.2,
                    is_active=True,
                    maintenance_mode=False,
                )
                self.stdout.write(self.style.SUCCESS('✅ Created default settings'))
            
            # Create rewards if missing
            if SpinWheelReward.objects.filter(is_active=True).count() == 0:
                default_rewards = [
                    {'name': '5 Tokens', 'reward_type': 'tokens', 'value': 5, 'probability': 0.25},
                    {'name': '10 Tokens', 'reward_type': 'tokens', 'value': 10, 'probability': 0.20},
                    {'name': '15 Tokens', 'reward_type': 'tokens', 'value': 15, 'probability': 0.15},
                    {'name': '25 Tokens', 'reward_type': 'tokens', 'value': 25, 'probability': 0.10},
                    {'name': 'Scratch Card', 'reward_type': 'scratch_card', 'value': 1, 'probability': 0.15},
                    {'name': '5% Discount', 'reward_type': 'discount', 'value': 5, 'probability': 0.08},
                    {'name': '10% Discount', 'reward_type': 'discount', 'value': 10, 'probability': 0.05},
                    {'name': '2 Tokens', 'reward_type': 'tokens', 'value': 2, 'probability': 0.02},
                ]
                
                for reward_data in default_rewards:
                    SpinWheelReward.objects.create(
                        name=reward_data['name'],
                        reward_type=reward_data['reward_type'],
                        value=reward_data['value'],
                        probability=reward_data['probability'],
                        is_active=True,
                        extra_data={}
                    )
                
                self.stdout.write(self.style.SUCCESS(f'✅ Created {len(default_rewards)} rewards'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Fix failed: {str(e)}'))

    def test_api_endpoint(self):
        """Test the actual API endpoint"""
        self.stdout.write('\n🧪 TESTING API ENDPOINT...')
        
        try:
            from gaming.spin_wheel_api import spin_wheel_status
            
            # Create test request
            factory = RequestFactory()
            request = factory.get('/api/gaming/spin-wheel/status/')
            
            # Get or create test user
            user, created = User.objects.get_or_create(
                username='test_spin_user',
                defaults={'email': '<EMAIL>'}
            )
            force_authenticate(request, user=user)
            
            # Test the API
            response = spin_wheel_status(request)
            
            self.stdout.write(f'Status Code: {response.status_code}')
            
            if response.status_code == 200 and response.data.get('success'):
                self.stdout.write(self.style.SUCCESS('✅ API ENDPOINT WORKING!'))
                self.stdout.write(f'Can spin: {response.data.get("can_spin")}')
                self.stdout.write(f'Available rewards: {len(response.data.get("available_rewards", []))}')
                self.stdout.write(f'Current balance: {response.data.get("current_balance")}')
            else:
                self.stdout.write(self.style.ERROR('❌ API ENDPOINT FAILED!'))
                self.stdout.write(f'Error: {response.data.get("error", "Unknown error")}')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ API test failed: {str(e)}'))
            import traceback
            traceback.print_exc()
