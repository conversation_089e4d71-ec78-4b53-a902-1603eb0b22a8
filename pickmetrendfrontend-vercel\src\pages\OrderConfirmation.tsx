import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { formatINR } from '../utils/currencyFormatter';

interface OrderItem {
  id: number;
  product: {
    id: string;
    name: string;
    slug: string;
    price: number;
    images?: Array<{
      id: string;
      image_url?: string;
      is_primary: boolean;
    }>;
  };
  price: number;
  quantity: number;
  variant_id?: string;
  printify_order_id?: string;
  total_price: number;
}

interface Order {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  total: number;
  status: string;
  payment_status: string;
  payment_method: string;
  payment_id?: string;
  notes?: string;
  tracking_number?: string;
  razorpay_order_id?: string;
  razorpay_payment_id?: string;
  razorpay_signature?: string;
  items: OrderItem[];
  created_at: string;
  updated_at?: string;
}

const OrderConfirmation = () => {
  const { id } = useParams<{ id: string }>();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);
        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
        const response = await axios.get(`${baseUrl}/api/orders/${id}/`, {
          headers: {
            'Authorization': `JWT ${localStorage.getItem('access_token')}`
          }
        });

        setOrder(response.data);
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching order:', err);
        setError(err.response?.data?.detail || 'Failed to load order details');
        setLoading(false);
      }
    };

    if (id) {
      fetchOrder();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error || 'Order not found'}
        </div>
        <Link to="/orders" className="text-primary-600 hover:underline">
          ← Back to orders
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-8">
        <div className="flex items-center">
          <svg className="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
          </svg>
          <h2 className="text-xl font-bold">Order Confirmed!</h2>
        </div>
        <p className="mt-2">Thank you for your order. We've received your payment and are processing your order now.</p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">
            Order #{order.id.substring(0, 8)}
          </h1>
          <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
          </span>
        </div>

        <div className="border-b pb-4 mb-4">
          <p className="text-gray-600">
            <span className="font-medium">Order Date:</span> {new Date(order.created_at).toLocaleDateString()}
          </p>
          <p className="text-gray-600">
            <span className="font-medium">Payment Method:</span> {order.payment_method}
          </p>
          <p className="text-gray-600">
            <span className="font-medium">Payment Status:</span> {order.payment_status}
          </p>
        </div>

        <h2 className="text-xl font-semibold mb-4">Items</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {order.items.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {item.product?.images && item.product.images.length > 0 && (
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <img
                            className="h-10 w-10 rounded-md object-cover"
                            src={item.product.images.find(img => img.is_primary)?.image_url || item.product.images[0].image_url}
                            alt={item.product.name}
                          />
                        </div>
                      )}
                      <div>
                        <div className="text-sm font-medium text-gray-900">{item.product.name}</div>
                        {item.variant_id && (
                          <div className="text-sm text-gray-500">Variant ID: {item.variant_id}</div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatINR(item.price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatINR(item.total_price || (item.price * item.quantity))}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-6 border-t pt-4">
          {/* Calculate subtotal from items */}
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Subtotal:</span>
            <span>{formatINR(order.items.reduce((sum, item) => sum + item.total_price, 0))}</span>
          </div>

          {/* We don't have shipping cost in the API response, so we'll estimate it */}
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Shipping:</span>
            <span>{formatINR(0)}</span>
          </div>

          {/* We don't have tax in the API response */}
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Tax:</span>
            <span>{formatINR(0)}</span>
          </div>

          <div className="flex justify-between font-bold text-lg">
            <span>Total:</span>
            <span>{formatINR(order.total)}</span>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Shipping Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium mb-2">Shipping Address</h3>
            <p>{order.full_name}</p>
            <p>{order.address}</p>
            <p>{order.city}, {order.state} {order.zipcode}</p>
            <p>{order.country}</p>
          </div>
          <div>
            <h3 className="font-medium mb-2">Contact Information</h3>
            <p>Email: {order.email}</p>
            <p>Phone: {order.phone}</p>
          </div>
        </div>
      </div>

      <div className="flex justify-between mt-8">
        <Link to="/shop" className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
          Continue Shopping
        </Link>
        <Link to="/orders" className="px-6 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300">
          View All Orders
        </Link>
      </div>
    </div>
  );
};

export default OrderConfirmation;
