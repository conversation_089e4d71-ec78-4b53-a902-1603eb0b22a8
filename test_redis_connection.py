#!/usr/bin/env python3
"""
Test script to verify Redis connection with Railway Redis URL
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')

# Setup Django
django.setup()

from dropshipping_backend.settings import REDIS_URL, get_redis_connection, REDIS_AVAILABLE

def test_redis_connection():
    """Test Redis connection"""
    print(f"Testing Redis connection...")
    print(f"Redis URL: {REDIS_URL}")
    print(f"Redis Available: {REDIS_AVAILABLE}")
    
    try:
        # Test connection
        redis_client = get_redis_connection()
        
        # Test basic operations
        redis_client.set('test_key', 'test_value')
        value = redis_client.get('test_key')
        redis_client.delete('test_key')
        
        print("✅ Redis connection test successful!")
        print(f"✅ Test value retrieved: {value}")
        
        # Test Redis info
        info = redis_client.info()
        print(f"✅ Redis server version: {info.get('redis_version', 'Unknown')}")
        print(f"✅ Connected clients: {info.get('connected_clients', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis connection test failed: {e}")
        return False

if __name__ == "__main__":
    test_redis_connection() 