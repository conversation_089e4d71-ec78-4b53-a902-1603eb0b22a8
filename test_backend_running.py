#!/usr/bin/env python
"""
Test if Django Backend is Running
=================================

This script tests if the Django backend server is running and accessible.
"""

import requests
import time

def test_backend_connection():
    """Test if the backend server is running"""
    
    backend_url = "http://127.0.0.1:8000"
    
    print("🧪 Testing Backend Connection")
    print("=" * 40)
    print(f"🌐 Testing URL: {backend_url}")
    
    # Test basic connection
    try:
        print("📡 Testing basic connection...")
        response = requests.get(f"{backend_url}/", timeout=5)
        print(f"✅ Backend is running! Status: {response.status_code}")
        
        # Test admin interface
        print("📡 Testing admin interface...")
        admin_response = requests.get(f"{backend_url}/admin/", timeout=5)
        print(f"✅ Admin interface accessible! Status: {admin_response.status_code}")
        
        # Test API endpoints
        print("📡 Testing API endpoints...")
        
        # Test token packs endpoint (will return 401 without auth, but that's expected)
        api_response = requests.get(f"{backend_url}/api/wallet/token-packs/", timeout=5)
        print(f"✅ Token packs API responding! Status: {api_response.status_code}")
        
        if api_response.status_code == 401:
            print("   ℹ️  401 Unauthorized is expected (authentication required)")
        
        print("\n🎉 Backend is running successfully!")
        print(f"🌐 Access your backend at: {backend_url}")
        print(f"🔧 Admin interface: {backend_url}/admin/")
        print(f"📊 API endpoints: {backend_url}/api/")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Backend server is not running")
        print("\n💡 To start the backend server:")
        print("   1. Open a new terminal")
        print("   2. Navigate to: C:\\Users\\<USER>\\OneDrive\\Desktop\\pickmetrendofficial-render")
        print("   3. Run: python manage.py runserver 8000 --settings=local_test_settings")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ Connection timeout - Backend server might be starting up")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def wait_for_backend(max_wait=30):
    """Wait for backend to start up"""
    
    print(f"⏳ Waiting for backend to start (max {max_wait} seconds)...")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://127.0.0.1:8000/", timeout=2)
            print(f"✅ Backend started successfully after {i+1} seconds!")
            return True
        except:
            print(f"   ⏳ Waiting... ({i+1}/{max_wait})")
            time.sleep(1)
    
    print(f"❌ Backend did not start within {max_wait} seconds")
    return False

if __name__ == '__main__':
    # First try immediate connection
    if not test_backend_connection():
        # If failed, wait and try again
        if wait_for_backend():
            test_backend_connection()
        else:
            print("\n🔧 Manual Backend Startup Instructions:")
            print("=" * 50)
            print("1. Open a new PowerShell/Command Prompt")
            print("2. Navigate to the backend directory:")
            print("   cd C:\\Users\\<USER>\\OneDrive\\Desktop\\pickmetrendofficial-render")
            print("3. Start the Django server:")
            print("   python manage.py runserver 8000 --settings=local_test_settings")
            print("4. Wait for 'Starting development server at http://127.0.0.1:8000/'")
            print("5. Then run this test again: python test_backend_running.py")
