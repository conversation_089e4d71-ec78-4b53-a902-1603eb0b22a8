/**
 * Utility functions for phone number handling and privacy
 */

/**
 * Masks the last 5 digits of a phone number for privacy
 * @param phoneNumber - The phone number to mask
 * @param maskChar - Character to use for masking (default: 'X')
 * @returns Masked phone number
 */
export const maskPhoneNumber = (phoneNumber: string, maskChar: string = 'X'): string => {
  if (!phoneNumber) return '';
  
  // Remove all non-digit characters except + at the beginning
  const cleaned = phoneNumber.replace(/(?!^\+)\D/g, '');
  
  // If the number is too short, return as is
  if (cleaned.length <= 5) return phoneNumber;
  
  // Get the part to keep (all except last 5 digits)
  const keepPart = cleaned.slice(0, -5);
  
  // Create masked part (5 X's)
  const maskedPart = maskChar.repeat(5);
  
  // Reconstruct with original formatting for the kept part
  let result = '';
  let cleanedIndex = 0;
  
  for (let i = 0; i < phoneNumber.length; i++) {
    const char = phoneNumber[i];
    
    if (char === '+' && i === 0) {
      result += char;
    } else if (/\d/.test(char)) {
      if (cleanedIndex < keepPart.length) {
        result += char;
        cleanedIndex++;
      } else {
        // We've reached the part to mask
        break;
      }
    } else if (cleanedIndex < keepPart.length) {
      // Keep formatting characters only for the non-masked part
      result += char;
    }
  }
  
  // Add the masked part
  result += maskedPart;
  
  return result;
};

/**
 * Company contact information with privacy protection
 */
export const COMPANY_CONTACT = {
  // Full number for actual calling/linking (keep this private)
  FULL_PHONE: '+************',
  
  // Masked number for display
  DISPLAY_PHONE: '+91 935XXXXX',
  
  // Email
  EMAIL: '<EMAIL>',
  
  // Alternative email
  INFO_EMAIL: '<EMAIL>',
  
  // Business hours
  BUSINESS_HOURS: 'Monday–Friday, 10 AM to 6 PM (IST)',
  
  // Address
  ADDRESS: {
    line1: '#42, 2nd Floor',
    line2: 'MG Road, Bangalore',
    line3: 'Karnataka, India 560001'
  }
};

/**
 * Get the appropriate phone number for different contexts
 */
export const getPhoneNumber = (context: 'display' | 'link' = 'display'): string => {
  switch (context) {
    case 'link':
      // Use full number for tel: links and actual functionality
      return COMPANY_CONTACT.FULL_PHONE;
    case 'display':
    default:
      // Use masked number for display purposes
      return COMPANY_CONTACT.DISPLAY_PHONE;
  }
};

/**
 * Format phone number for display with country code
 */
export const formatPhoneForDisplay = (phone: string): string => {
  if (!phone) return '';
  
  // If it's our company number, return the masked version
  if (phone.includes('9353014895') || phone.includes('935XXXXX')) {
    return COMPANY_CONTACT.DISPLAY_PHONE;
  }
  
  // For other numbers, apply general masking
  return maskPhoneNumber(phone);
};

/**
 * Check if a phone number should be masked for privacy
 */
export const shouldMaskPhone = (phone: string): boolean => {
  // Add logic here for which numbers should be masked
  // For now, mask company numbers
  return phone.includes('9353014895') || phone.includes('935');
};
