import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useWallet } from '../../hooks/useWallet';
// @ts-ignore
import Confetti from 'react-confetti';

interface BattleData {
  id: string;
  game_type: string;
  game_display_name: string;
  status: string;
  result?: string;
  player1: {
    id: number;
    username: string;
  };
  player2?: {
    id: number;
    username: string;
  };
  is_ai_battle: boolean;
  game_state: any;
  moves_history: any[];
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

interface BattleArenaProps {
  battleId: string;
  onBattleComplete: () => void;
}

const BattleArena: React.FC<BattleArenaProps> = ({ battleId, onBattleComplete }) => {
  const { fetchWallet } = useWallet();
  const [battle, setBattle] = useState<BattleData | null>(null);
  const [gameState, setGameState] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedMove, setSelectedMove] = useState<string | number | null>(null);
  const [loading, setLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [moveAnimation, setMoveAnimation] = useState<string | null>(null);

  const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';

  const getAuthHeaders = () => {
    const token = localStorage.getItem('access_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  };

  // Fetch battle data from API
  const fetchBattleData = async () => {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/gaming/battles/${battleId}/`,
        { headers: getAuthHeaders() }
      );

      setBattle(response.data);
      setGameState(response.data.game_state);
      setIsConnected(true);

      // If battle is completed, refresh wallet and auto-return to lobby after 3 seconds
      if (response.data.status === 'completed') {
        // Refresh wallet to show updated token balance
        fetchWallet();

        setTimeout(() => {
          onBattleComplete();
        }, 3000);
      }
    } catch (err: any) {
      console.error('Failed to fetch battle data:', err);
      setError('Failed to load battle data');
    } finally {
      setLoading(false);
    }
  };

  // Poll for battle updates every 2 seconds
  useEffect(() => {
    fetchBattleData();

    const interval = setInterval(() => {
      if (battle?.status !== 'completed') {
        fetchBattleData();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [battleId]);

  // Stop polling when battle is completed
  useEffect(() => {
    if (battle?.status === 'completed') {
      // Stop polling by not setting up new intervals
    }
  }, [battle?.status]);

  useEffect(() => {
    if (battle?.status === 'completed' && battle.result === 'player1_win') {
      setShowConfetti(true);
      setTimeout(() => setShowConfetti(false), 3000);
    }
  }, [battle?.status, battle?.result]);

  const handleStartBattle = async () => {
    try {
      await axios.post(
        `${API_BASE_URL}/api/gaming/battles/${battleId}/start/`,
        {},
        { headers: getAuthHeaders() }
      );
      // Refresh battle data
      fetchBattleData();
    } catch (err: any) {
      console.error('Failed to start battle:', err);
      setError('Failed to start battle');
    }
  };

  const handleMakeMove = async (move: string | number) => {
    if (battle?.status === 'in_progress') {
      try {
        await axios.post(
          `${API_BASE_URL}/api/gaming/battles/${battleId}/move/`,
          { move },
          { headers: getAuthHeaders() }
        );
        setSelectedMove(null);
        // Refresh battle data
        fetchBattleData();
      } catch (err: any) {
        console.error('Failed to make move:', err);
        setError('Failed to make move');
      }
    }
  };

  const renderProgressBar = () => {
    if (!gameState?.round || !gameState?.max_rounds) return null;
    const percent = Math.min(100, Math.round((gameState.round - 1) / gameState.max_rounds * 100));
    return (
      <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
        <div
          className="bg-gradient-to-r from-blue-400 to-purple-500 h-3 rounded-full transition-all duration-500"
          style={{ width: `${percent}%` }}
        ></div>
      </div>
    );
  };

  const renderGameInterface = () => {
    if (!battle || !gameState) {
      console.log('Missing battle or gameState:', { battle, gameState });
      return null;
    }

    // Get game type from multiple possible sources
    const gameType = (typeof battle.game_type === 'object' ? (battle.game_type as any)?.name : battle.game_type) || gameState.game_type || gameState.type;

    console.log('=== GAME TYPE DEBUG ===');
    console.log('Game type detected:', gameType);
    console.log('battle.game_type:', battle.game_type);
    console.log('gameState.game_type:', gameState.game_type);
    console.log('gameState.type:', gameState.type);
    console.log('Full gameState:', gameState);
    console.log('======================');

    // Normalize game type (handle variations)
    let normalizedGameType = gameType;
    if (gameType === 'Rock Paper Scissors' || gameType === 'rock_paper_scissors') {
      normalizedGameType = 'rock_paper_scissors';
    } else if (gameType === 'Number Guessing Battle' || gameType === 'number_guessing') {
      normalizedGameType = 'number_guessing';
    }

    console.log('Normalized game type:', normalizedGameType);

    switch (normalizedGameType) {
      case 'rock_paper_scissors':
        console.log('Rendering Rock Paper Scissors');
        return renderRockPaperScissors();
      case 'number_guessing':
        console.log('Rendering Number Guessing');
        return renderNumberGuessing();
      default:
        console.log('Unknown game type, showing error');
        return (
          <div className="text-center p-4">
            <div className="text-red-600 mb-2">Unknown game type: "{gameType}"</div>
            <div className="text-sm text-gray-500 mb-2">
              Normalized: "{normalizedGameType}"
            </div>
            <div className="text-sm text-gray-500">
              Available types: rock_paper_scissors, number_guessing
            </div>
            <div className="text-xs text-gray-400 mt-2">
              Debug: Check browser console for more details
            </div>
          </div>
        );
    }
  };

  const renderRockPaperScissors = () => {
    const choices = ['rock', 'paper', 'scissors'];
    const emojis = { rock: '🪨', paper: '📄', scissors: '✂️' };
    const colors = { rock: 'bg-yellow-200', paper: 'bg-blue-200', scissors: 'bg-pink-200' };
    return (
      <div className="space-y-6">
        {renderProgressBar()}
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">
            Round {gameState?.round || 1} of {gameState?.max_rounds || 3}
          </h3>
          <div className="flex justify-center space-x-8">
            <div className="text-center">
              <div className="text-sm text-gray-600">You</div>
              <div className="text-2xl font-bold">{gameState?.player1_score || 0}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600">Opponent</div>
              <div className="text-2xl font-bold">{gameState?.player2_score || 0}</div>
            </div>
          </div>
        </div>

        {battle && battle.status === 'in_progress' && !gameState?.player1_move && (
          <div className="text-center">
            <p className="mb-4 text-lg font-semibold text-gray-700 animate-pulse">Choose your move:</p>
            <div className="flex justify-center space-x-6">
              {choices.map((choice) => (
                <button
                  key={choice}
                  onClick={() => {
                    setMoveAnimation(choice);
                    setTimeout(() => setMoveAnimation(null), 500);
                    handleMakeMove(choice);
                  }}
                  className={`px-8 py-6 text-4xl rounded-full shadow-lg transform transition-all duration-200 hover:scale-110 focus:outline-none ${colors[choice as keyof typeof colors]} ${moveAnimation === choice ? 'ring-4 ring-purple-400 scale-125' : ''}`}
                  style={{ fontSize: '3rem' }}
                >
                  {emojis[choice as keyof typeof emojis]}
                </button>
              ))}
            </div>
          </div>
        )}

        {gameState?.player1_move && (
          <div className="text-center text-gray-600 animate-pulse">
            Waiting for opponent...
          </div>
        )}

        {gameState?.round_results && gameState.round_results.length > 0 && (
          <div className="mt-6">
            <h4 className="font-semibold mb-2">Round History:</h4>
            <div className="space-y-2">
              {gameState.round_results.map((result: any, index: number) => (
                <div key={index} className="flex justify-between items-center p-2 bg-gray-100 rounded shadow-sm">
                  <span>Round {result.round}</span>
                  <span>
                    {emojis[result.player1_move as keyof typeof emojis]} vs {emojis[result.player2_move as keyof typeof emojis]}
                  </span>
                  <span className={`font-semibold ${
                    result.winner === 'player1' ? 'text-green-600 animate-bounce' : 
                    result.winner === 'player2' ? 'text-red-600 animate-shake' : 'text-gray-600 animate-fade'
                  }`}>
                    {result.winner === 'player1' ? 'You Win!' : 
                     result.winner === 'player2' ? 'Opponent Wins' : 'Draw'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderNumberGuessing = () => {
    return (
      <div className="space-y-6">
        {renderProgressBar()}
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">
            Round {gameState?.round || 1} of {gameState?.max_rounds || 5}
          </h3>
          <div className="flex justify-center space-x-8">
            <div className="text-center">
              <div className="text-sm text-gray-600">Your Score</div>
              <div className="text-2xl font-bold">{gameState?.player1_score || 0}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600">Opponent Score</div>
              <div className="text-2xl font-bold">{gameState?.player2_score || 0}</div>
            </div>
          </div>
        </div>

        {battle && battle.status === 'in_progress' && gameState?.player1_guess === null && (
          <div className="text-center">
            <p className="mb-4 text-lg font-semibold text-gray-700 animate-pulse">Guess a number between 1 and 100:</p>
            <div className="flex justify-center space-x-2">
              <input
                type="number"
                min="1"
                max="100"
                value={selectedMove || ''}
                onChange={(e) => setSelectedMove(parseInt(e.target.value))}
                className="px-3 py-2 border-2 border-purple-400 rounded-lg text-lg focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                placeholder="Enter number"
                style={{ width: '120px' }}
              />
              <button
                onClick={() => selectedMove && handleMakeMove(selectedMove)}
                disabled={!selectedMove}
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-purple-500 hover:to-blue-500 transition-all duration-200 text-lg font-semibold shadow-lg disabled:bg-gray-400"
              >
                Submit
              </button>
            </div>
          </div>
        )}

        {gameState?.player1_guess !== null && (
          <div className="text-center text-gray-600 animate-pulse">
            Your guess: {gameState.player1_guess} - Waiting for opponent...
          </div>
        )}

        {gameState?.round_results && gameState.round_results.length > 0 && (
          <div className="mt-6">
            <h4 className="font-semibold mb-2">Round History:</h4>
            <div className="space-y-2">
              {gameState.round_results.map((result: any, index: number) => (
                <div key={index} className="p-3 bg-gray-100 rounded shadow-sm">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold">Round {result.round}</span>
                    <span className={`font-semibold ${
                      result.winner === 'player1' ? 'text-green-600 animate-bounce' : 
                      result.winner === 'player2' ? 'text-red-600 animate-shake' : 'text-gray-600 animate-fade'
                    }`}>
                      {result.winner === 'player1' ? 'You Win!' : 
                       result.winner === 'player2' ? 'Opponent Wins' : 'Draw'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    Target: {result.target_number} | Your guess: {result.player1_guess} (off by {result.player1_distance}) | 
                    Opponent: {result.player2_guess} (off by {result.player2_distance})
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading || !battle) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading battle...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 relative" style={{ background: 'linear-gradient(135deg, #f0e9ff 0%, #e0f7fa 100%)', minHeight: '80vh', borderRadius: '2rem', boxShadow: '0 8px 32px rgba(80,0,200,0.12)' }}>
      {showConfetti && <Confetti numberOfPieces={200} recycle={false} width={window.innerWidth} height={window.innerHeight} />}
      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold mb-2">{(typeof battle.game_type === 'object' ? (battle.game_type as any)?.display_name : battle.game_type) || 'Game Battle'}</h1>
          <div className="flex justify-center items-center space-x-4">
            <span className="font-semibold">{battle.player1.username}</span>
            <span className="text-gray-500">vs</span>
            <span className="font-semibold">
              {battle.player2?.username || 'AI Bot'}
            </span>
          </div>
          <div className="mt-2">
            <span className={`px-3 py-1 rounded-full text-sm ${
              battle.status === 'waiting' ? 'bg-yellow-100 text-yellow-800' :
              battle.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
              battle.status === 'completed' ? 'bg-green-100 text-green-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {battle.status.replace('_', ' ').toUpperCase()}
            </span>
          </div>
        </div>

        {battle.status === 'waiting' && (
          <div className="text-center">
            <p className="mb-4">Battle is ready to start!</p>
            <button
              onClick={handleStartBattle}
              className="px-6 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Start Battle
            </button>
          </div>
        )}

        {battle.status === 'in_progress' && (
          <div>
            <div className="mb-4 p-2 bg-gray-100 rounded text-sm">
              <strong>Debug Info:</strong><br/>
              Game Type: {typeof battle.game_type === 'object' ? (battle.game_type as any)?.name : battle.game_type}<br/>
              Game State Type: {gameState?.game_type}<br/>
              Battle Status: {battle.status}<br/>
              Player1 Guess: {gameState?.player1_guess}<br/>
              Round: {gameState?.round}
            </div>
            {renderGameInterface()}
          </div>
        )}

        {battle.status === 'completed' && (
          <div className="text-center">
            <h2 className="text-xl font-bold mb-4">Battle Complete!</h2>
            <div className={`text-2xl font-bold mb-4 ${
              battle.result === 'player1_win' ? 'text-green-600' :
              battle.result === 'player2_win' ? 'text-red-600' :
              'text-gray-600'
            }`}>
              {battle.result === 'player1_win' ? '🎉 You Win!' :
               battle.result === 'player2_win' ? '😔 You Lose' :
               '🤝 Draw'}
            </div>

            {/* Token Transaction Info */}
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">Token Transaction:</div>
              {battle.result === 'player1_win' && (
                <div className="text-green-600 font-semibold">
                  🪙 +5 tokens earned for winning!
                </div>
              )}
              {battle.result === 'player2_win' && (
                <div className="text-red-600 font-semibold">
                  🪙 -1 token deducted for losing
                </div>
              )}
              {battle.result === 'draw' && (
                <div className="text-gray-600 font-semibold">
                  🤝 No token change for draw
                </div>
              )}
            </div>

            <p className="text-gray-600">Returning to lobby...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BattleArena;
