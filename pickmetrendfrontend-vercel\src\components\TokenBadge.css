.token-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    border-radius: 20px;
    padding: 4px 12px;
    font-weight: 600;
    font-size: 12px;
    border: 2px solid #e6c200;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.token-badge:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.token-badge:hover:before {
    left: 100%;
}

.token-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.4);
}

/* Size variants */
.token-badge--small {
    padding: 2px 8px;
    font-size: 10px;
    border-radius: 15px;
}

.token-badge--medium {
    padding: 4px 12px;
    font-size: 12px;
    border-radius: 20px;
}

.token-badge--large {
    padding: 6px 16px;
    font-size: 14px;
    border-radius: 25px;
}

/* Icon */
.token-badge__icon {
    font-size: 1.2em;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* Text */
.token-badge__text {
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.token-badge__percentage {
    background: rgba(255, 255, 255, 0.3);
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 0.9em;
    font-weight: 700;
}

/* Responsive */
@media (max-width: 768px) {
    .token-badge {
        font-size: 11px;
        padding: 3px 10px;
    }
    
    .token-badge--small {
        font-size: 9px;
        padding: 2px 6px;
    }
    
    .token-badge--large {
        font-size: 13px;
        padding: 5px 14px;
    }
}

/* Animation for new badges */
@keyframes tokenBadgeAppear {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.token-badge {
    animation: tokenBadgeAppear 0.3s ease-out;
}
