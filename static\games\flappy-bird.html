<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flappy Bird - PickMeTrend</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        .game-container {
            position: relative;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }

        #gameCanvas {
            display: block;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 70%, #90EE90 100%);
            cursor: pointer;
        }

        .game-ui {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .score-display {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            pointer-events: none;
        }

        .token-display {
            position: absolute;
            top: 20px;
            right: 20px;
            color: #FFD700;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            pointer-events: none;
        }

        .game-over-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            pointer-events: all;
        }

        .start-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            pointer-events: all;
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.9);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            pointer-events: all;
        }

        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
        }

        .btn-restart {
            background: #FF6B6B;
        }

        .btn-restart:hover {
            background: #FF5252;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .instructions {
            font-size: 14px;
            margin: 20px;
            line-height: 1.5;
        }

        .stats {
            margin: 20px 0;
            font-size: 18px;
        }

        .high-score {
            color: #FFD700;
            font-weight: bold;
        }

        .error-message {
            color: #FF6B6B;
            margin: 10px 0;
            font-size: 14px;
        }

        /* Mobile optimizations */
        @media (max-width: 480px) {
            .game-container {
                border-radius: 0;
                max-width: 100%;
                height: 100vh;
            }
            
            .score-display {
                font-size: 20px;
                top: 10px;
                left: 10px;
            }
            
            .token-display {
                font-size: 16px;
                top: 10px;
                right: 10px;
            }
            
            .instructions {
                font-size: 12px;
                margin: 15px;
            }
        }

        /* Prevent text selection */
        .game-container {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Prevent context menu on long press */
        .game-container {
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <canvas id="gameCanvas" width="400" height="600"></canvas>
        
        <div class="game-ui">
            <div class="score-display">
                Score: <span id="currentScore">0</span>
            </div>
            <div class="token-display">
                🪙 <span id="tokenBalance">--</span>
            </div>
        </div>

        <!-- Start Screen -->
        <div class="start-screen" id="startScreen">
            <h1>🐦 Flappy Bird</h1>
            <div class="instructions">
                <p><strong>How to Play:</strong></p>
                <p>• Tap or click to make the bird flap</p>
                <p>• Avoid pipes and the ground</p>
                <p>• Pass through pipe gaps to score</p>
                <br>
                <p><strong>Token Rewards:</strong></p>
                <p>• 2 tokens to play</p>
                <p>• +5 tokens for 10+ pipes</p>
                <p>• -1 extra token if less than 5 pipes</p>
            </div>
            <button class="btn" id="startGameBtn">Start Game (2 🪙)</button>
            <div class="error-message" id="startError"></div>
        </div>

        <!-- Loading Screen -->
        <div class="loading-screen" id="loadingScreen">
            <div class="spinner"></div>
            <p>Connecting to server...</p>
        </div>

        <!-- Game Over Screen -->
        <div class="game-over-screen" id="gameOverScreen">
            <h2>Game Over!</h2>
            <div class="stats">
                <p>Score: <span id="finalScore">0</span></p>
                <p>Pipes Passed: <span id="pipesPassed">0</span></p>
                <p class="high-score" id="highScoreText" style="display: none;">🎉 New High Score!</p>
            </div>
            <div id="tokenResult"></div>
            <button class="btn btn-restart" id="restartBtn">Play Again</button>
            <button class="btn" id="backToMenuBtn">Back to Menu</button>
        </div>
    </div>

    <script>
        // Game configuration
        const CONFIG = {
            CANVAS_WIDTH: 400,
            CANVAS_HEIGHT: 600,
            BIRD_SIZE: 30,
            PIPE_WIDTH: 60,
            PIPE_GAP: 150,
            PIPE_SPEED: 2,
            GRAVITY: 0.5,
            JUMP_FORCE: -8,
            GROUND_HEIGHT: 50
        };

        // Game state
        let gameState = {
            sessionId: null,
            score: 0,
            pipesPassed: 0,
            isPlaying: false,
            gameStartTime: null,
            tokenBalance: 0,
            bird: null,
            pipes: [],
            gameLoop: null
        };

        // API configuration - Update these URLs to match your Django backend
        const API_BASE = '/api/gaming/flappy-bird';

        // Store authentication token received from parent window
        let authToken = null;
        let isWaitingForAuth = false;

        // Get authentication token (JWT from React frontend or CSRF from Django)
        function getAuthToken() {
            // If we have a stored token from parent window, use it
            if (authToken) {
                return authToken;
            }

            try {
                // Try to get JWT token from localStorage (direct access)
                const jwtToken = localStorage.getItem('access_token');
                if (jwtToken) return { type: 'jwt', token: jwtToken };
            } catch (error) {
                console.warn('Could not get JWT token from localStorage:', error);
            }

            // Fallback to CSRF token for direct Django access
            return { type: 'csrf', token: getCSRFToken() };
        }

        // Request authentication token from parent window
        function requestAuthFromParent() {
            return new Promise((resolve, reject) => {
                if (isWaitingForAuth) {
                    reject(new Error('Already waiting for authentication'));
                    return;
                }

                isWaitingForAuth = true;

                // Set up message listener for auth response
                const handleAuthResponse = (event) => {
                    console.log('Received message from parent:', event.data);
                    if (event.data.type === 'authToken') {
                        window.removeEventListener('message', handleAuthResponse);
                        isWaitingForAuth = false;

                        if (event.data.token) {
                            authToken = { type: 'jwt', token: event.data.token };
                            console.log('Successfully received auth token from parent');
                            resolve(authToken);
                        } else {
                            console.error('No token received from parent:', event.data.error);
                            reject(new Error('No token received from parent'));
                        }
                    }
                };

                window.addEventListener('message', handleAuthResponse);

                // Request token from parent
                try {
                    if (window.parent && window.parent !== window) {
                        console.log('Requesting auth token from parent window...');
                        window.parent.postMessage({ type: 'requestAuth' }, '*');

                        // Timeout after 5 seconds
                        setTimeout(() => {
                            if (isWaitingForAuth) {
                                window.removeEventListener('message', handleAuthResponse);
                                isWaitingForAuth = false;
                                console.error('Authentication request timeout');
                                reject(new Error('Authentication request timeout'));
                            }
                        }, 5000);
                    } else {
                        isWaitingForAuth = false;
                        console.warn('Not running in iframe, cannot request auth from parent');
                        reject(new Error('Not in iframe'));
                    }
                } catch (error) {
                    isWaitingForAuth = false;
                    console.error('Error requesting auth from parent:', error);
                    reject(error);
                }
            });
        }

        // Get CSRF token from parent window (Django template)
        function getCSRFToken() {
            try {
                // Try to get from parent window first (when in iframe)
                if (window.parent && window.parent.document) {
                    const csrfToken = window.parent.document.querySelector('[name=csrfmiddlewaretoken]');
                    if (csrfToken) return csrfToken.value;
                }

                // Fallback to current window
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfToken) return csrfToken.value;

                // Try to get from cookies
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'csrftoken') return value;
                }

                return null;
            } catch (error) {
                console.error('Error getting CSRF token:', error);
                return null;
            }
        }
        
        // Initialize game
        document.addEventListener('DOMContentLoaded', function() {
            initializeGame();
            setupEventListeners();
            loadUserStats();
        });

        function initializeGame() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas size for mobile
            if (window.innerWidth < 480) {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                CONFIG.CANVAS_WIDTH = canvas.width;
                CONFIG.CANVAS_HEIGHT = canvas.height;
            }
            
            // Initialize bird
            gameState.bird = {
                x: CONFIG.CANVAS_WIDTH / 4,
                y: CONFIG.CANVAS_HEIGHT / 2,
                velocity: 0,
                size: CONFIG.BIRD_SIZE
            };
            
            // Initialize pipes
            gameState.pipes = [];
            
            // Draw initial state
            drawGame(ctx);
        }

        function setupEventListeners() {
            const canvas = document.getElementById('gameCanvas');
            const startBtn = document.getElementById('startGameBtn');
            const restartBtn = document.getElementById('restartBtn');
            const backToMenuBtn = document.getElementById('backToMenuBtn');
            
            // Start game
            startBtn.addEventListener('click', startNewGame);
            
            // Restart game
            restartBtn.addEventListener('click', startNewGame);
            
            // Back to menu
            backToMenuBtn.addEventListener('click', showStartScreen);
            
            // Game controls
            canvas.addEventListener('click', handleJump);
            canvas.addEventListener('touchstart', handleJump);
            
            // Keyboard controls
            document.addEventListener('keydown', function(e) {
                if (e.code === 'Space' && gameState.isPlaying) {
                    e.preventDefault();
                    handleJump();
                }
            });
            
            // Prevent scrolling on mobile
            document.addEventListener('touchmove', function(e) {
                e.preventDefault();
            }, { passive: false });
        }

        async function startNewGame() {
            showLoadingScreen();

            try {
                let auth = getAuthToken();

                // If no token available, try to request from parent window
                if (!auth.token) {
                    try {
                        auth = await requestAuthFromParent();
                    } catch (error) {
                        console.error('Authentication error:', error);
                        showError('Authentication error. Please refresh the page.');
                        showStartScreen();
                        return;
                    }
                }

                const headers = {
                    'Content-Type': 'application/json'
                };

                if (auth.type === 'jwt') {
                    headers['Authorization'] = `Bearer ${auth.token}`;
                } else {
                    headers['X-CSRFToken'] = auth.token;
                }

                const response = await fetch(`${API_BASE}/start/`, {
                    method: 'POST',
                    headers: headers,
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        difficulty: 'medium'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    gameState.sessionId = data.session_id;
                    gameState.tokenBalance = data.balance;
                    updateTokenDisplay();

                    // Notify parent window of token update
                    console.log('Game start response data:', data);
                    notifyParentWindow('tokenUpdate', { balance: data.balance });

                    resetGameState();
                    hideAllScreens();
                    startGameLoop();
                } else {
                    showError(data.error || 'Failed to start game');
                    showStartScreen();
                }
            } catch (error) {
                console.error('Error starting game:', error);
                showError('Connection error. Please try again.');
                showStartScreen();
            }
        }

        function resetGameState() {
            gameState.score = 0;
            gameState.pipesPassed = 0;
            gameState.isPlaying = true;
            gameState.gameStartTime = Date.now();

            // Reset bird
            gameState.bird.y = CONFIG.CANVAS_HEIGHT / 2;
            gameState.bird.velocity = 0;

            // Clear pipes
            gameState.pipes = [];

            // Update UI
            document.getElementById('currentScore').textContent = gameState.score;
        }

        function startGameLoop() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            gameState.gameLoop = setInterval(() => {
                if (gameState.isPlaying) {
                    updateGame();
                    drawGame(ctx);
                }
            }, 1000 / 60); // 60 FPS
        }

        function updateGame() {
            // Update bird physics
            gameState.bird.velocity += CONFIG.GRAVITY;
            gameState.bird.y += gameState.bird.velocity;

            // Check ground collision
            if (gameState.bird.y + gameState.bird.size >= CONFIG.CANVAS_HEIGHT - CONFIG.GROUND_HEIGHT) {
                endGame('ground');
                return;
            }

            // Check ceiling collision
            if (gameState.bird.y <= 0) {
                gameState.bird.y = 0;
                gameState.bird.velocity = 0;
            }

            // Update pipes
            updatePipes();

            // Check pipe collisions
            if (checkPipeCollisions()) {
                endGame('collision');
                return;
            }

            // Spawn new pipes
            if (gameState.pipes.length === 0 ||
                gameState.pipes[gameState.pipes.length - 1].x < CONFIG.CANVAS_WIDTH - 200) {
                spawnPipe();
            }
        }

        function updatePipes() {
            for (let i = gameState.pipes.length - 1; i >= 0; i--) {
                const pipe = gameState.pipes[i];
                pipe.x -= CONFIG.PIPE_SPEED;

                // Check if bird passed through pipe
                if (!pipe.passed && pipe.x + CONFIG.PIPE_WIDTH < gameState.bird.x) {
                    pipe.passed = true;
                    gameState.score++;
                    gameState.pipesPassed++;
                    document.getElementById('currentScore').textContent = gameState.score;
                }

                // Remove pipes that are off screen
                if (pipe.x + CONFIG.PIPE_WIDTH < 0) {
                    gameState.pipes.splice(i, 1);
                }
            }
        }

        function spawnPipe() {
            const minHeight = 50;
            const maxHeight = CONFIG.CANVAS_HEIGHT - CONFIG.PIPE_GAP - CONFIG.GROUND_HEIGHT - minHeight;
            const topHeight = Math.random() * (maxHeight - minHeight) + minHeight;

            gameState.pipes.push({
                x: CONFIG.CANVAS_WIDTH,
                topHeight: topHeight,
                bottomY: topHeight + CONFIG.PIPE_GAP,
                passed: false
            });
        }

        function checkPipeCollisions() {
            const bird = gameState.bird;

            for (const pipe of gameState.pipes) {
                // Check if bird is within pipe's x range
                if (bird.x + bird.size > pipe.x && bird.x < pipe.x + CONFIG.PIPE_WIDTH) {
                    // Check collision with top pipe
                    if (bird.y < pipe.topHeight) {
                        return true;
                    }
                    // Check collision with bottom pipe
                    if (bird.y + bird.size > pipe.bottomY) {
                        return true;
                    }
                }
            }
            return false;
        }

        function handleJump() {
            if (gameState.isPlaying) {
                gameState.bird.velocity = CONFIG.JUMP_FORCE;
            }
        }

        function drawGame(ctx) {
            // Clear canvas
            ctx.clearRect(0, 0, CONFIG.CANVAS_WIDTH, CONFIG.CANVAS_HEIGHT);

            // Draw background gradient
            const gradient = ctx.createLinearGradient(0, 0, 0, CONFIG.CANVAS_HEIGHT);
            gradient.addColorStop(0, '#87CEEB');
            gradient.addColorStop(0.7, '#98FB98');
            gradient.addColorStop(1, '#90EE90');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, CONFIG.CANVAS_WIDTH, CONFIG.CANVAS_HEIGHT);

            // Draw pipes
            drawPipes(ctx);

            // Draw ground
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(0, CONFIG.CANVAS_HEIGHT - CONFIG.GROUND_HEIGHT, CONFIG.CANVAS_WIDTH, CONFIG.GROUND_HEIGHT);

            // Draw bird
            drawBird(ctx);
        }

        function drawPipes(ctx) {
            ctx.fillStyle = '#228B22';

            for (const pipe of gameState.pipes) {
                // Draw top pipe
                ctx.fillRect(pipe.x, 0, CONFIG.PIPE_WIDTH, pipe.topHeight);

                // Draw bottom pipe
                ctx.fillRect(pipe.x, pipe.bottomY, CONFIG.PIPE_WIDTH,
                    CONFIG.CANVAS_HEIGHT - pipe.bottomY - CONFIG.GROUND_HEIGHT);

                // Draw pipe caps
                ctx.fillStyle = '#32CD32';
                ctx.fillRect(pipe.x - 5, pipe.topHeight - 20, CONFIG.PIPE_WIDTH + 10, 20);
                ctx.fillRect(pipe.x - 5, pipe.bottomY, CONFIG.PIPE_WIDTH + 10, 20);
                ctx.fillStyle = '#228B22';
            }
        }

        function drawBird(ctx) {
            const bird = gameState.bird;

            // Bird body
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(bird.x + bird.size/2, bird.y + bird.size/2, bird.size/2, 0, Math.PI * 2);
            ctx.fill();

            // Bird beak
            ctx.fillStyle = '#FFA500';
            ctx.beginPath();
            ctx.moveTo(bird.x + bird.size, bird.y + bird.size/2);
            ctx.lineTo(bird.x + bird.size + 10, bird.y + bird.size/2 - 5);
            ctx.lineTo(bird.x + bird.size + 10, bird.y + bird.size/2 + 5);
            ctx.closePath();
            ctx.fill();

            // Bird eye
            ctx.fillStyle = '#000';
            ctx.beginPath();
            ctx.arc(bird.x + bird.size/2 + 5, bird.y + bird.size/2 - 5, 3, 0, Math.PI * 2);
            ctx.fill();
        }

        async function endGame(reason) {
            gameState.isPlaying = false;
            clearInterval(gameState.gameLoop);

            const gameDuration = (Date.now() - gameState.gameStartTime) / 1000;

            showLoadingScreen();

            try {
                let auth = getAuthToken();

                // If no token available, try to request from parent window
                if (!auth.token) {
                    try {
                        auth = await requestAuthFromParent();
                    } catch (error) {
                        console.error('Authentication error during game end:', error);
                        showGameOverScreen({
                            score: gameState.score,
                            pipes_passed: gameState.pipesPassed,
                            tokens_change: 0,
                            new_balance: gameState.tokenBalance,
                            message: 'Authentication error'
                        });
                        return;
                    }
                }

                const headers = {
                    'Content-Type': 'application/json'
                };

                if (auth.type === 'jwt') {
                    headers['Authorization'] = `Bearer ${auth.token}`;
                } else {
                    headers['X-CSRFToken'] = auth.token;
                }

                const response = await fetch(`${API_BASE}/submit-score/`, {
                    method: 'POST',
                    headers: headers,
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        session_id: gameState.sessionId,
                        score: gameState.score,
                        pipes_passed: gameState.pipesPassed,
                        game_duration: gameDuration,
                        exit_reason: reason
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Notify parent window of token update
                    console.log('Game end response data:', data);
                    notifyParentWindow('tokenUpdate', { balance: data.new_balance });
                    showGameOverScreen(data);
                } else {
                    showError(data.error || 'Failed to submit score');
                    showGameOverScreen({
                        score: gameState.score,
                        pipes_passed: gameState.pipesPassed,
                        tokens_change: 0,
                        new_balance: gameState.tokenBalance,
                        message: 'Score submission failed'
                    });
                }
            } catch (error) {
                console.error('Error submitting score:', error);
                showGameOverScreen({
                    score: gameState.score,
                    pipes_passed: gameState.pipesPassed,
                    tokens_change: 0,
                    new_balance: gameState.tokenBalance,
                    message: 'Connection error'
                });
            }
        }

        function showGameOverScreen(data) {
            hideAllScreens();

            document.getElementById('finalScore').textContent = data.score || gameState.score;
            document.getElementById('pipesPassed').textContent = data.pipes_passed || gameState.pipesPassed;

            // Show high score if applicable
            if (data.is_high_score) {
                document.getElementById('highScoreText').style.display = 'block';
            } else {
                document.getElementById('highScoreText').style.display = 'none';
            }

            // Show token result
            const tokenResult = document.getElementById('tokenResult');
            const tokensChange = data.tokens_change || 0;
            const newBalance = data.new_balance || gameState.tokenBalance;

            let tokenMessage = data.message || 'Game completed';
            let tokenColor = tokensChange >= 0 ? '#4CAF50' : '#FF6B6B';

            tokenResult.innerHTML = `
                <p style="color: ${tokenColor}; font-weight: bold; margin: 10px 0;">
                    ${tokenMessage}
                </p>
                <p>New Balance: ${newBalance} 🪙</p>
            `;

            gameState.tokenBalance = newBalance;
            updateTokenDisplay();

            document.getElementById('gameOverScreen').style.display = 'flex';
        }

        function showStartScreen() {
            hideAllScreens();
            document.getElementById('startScreen').style.display = 'flex';
        }

        function showLoadingScreen() {
            hideAllScreens();
            document.getElementById('loadingScreen').style.display = 'flex';
        }

        function hideAllScreens() {
            document.getElementById('startScreen').style.display = 'none';
            document.getElementById('loadingScreen').style.display = 'none';
            document.getElementById('gameOverScreen').style.display = 'none';
        }

        function updateTokenDisplay() {
            document.getElementById('tokenBalance').textContent = gameState.tokenBalance;
        }

        function showError(message) {
            document.getElementById('startError').textContent = message;
        }

        async function loadUserStats() {
            try {
                let auth = getAuthToken();

                // If no token available, try to request from parent window
                if (!auth.token) {
                    try {
                        auth = await requestAuthFromParent();
                    } catch (error) {
                        console.warn('Could not get auth from parent:', error);
                        // Set a default balance for demo
                        gameState.tokenBalance = 0;
                        updateTokenDisplay();
                        return;
                    }
                }

                const headers = {};
                if (auth.type === 'jwt') {
                    headers['Authorization'] = `Bearer ${auth.token}`;
                } else {
                    headers['X-CSRFToken'] = auth.token;
                }

                const response = await fetch(`${API_BASE}/stats/`, {
                    method: 'GET',
                    headers: headers,
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        gameState.tokenBalance = data.stats.current_balance;
                        updateTokenDisplay();

                        // Notify parent window of token balance
                        console.log('Load stats response data:', data);
                        notifyParentWindow('tokenUpdate', { balance: data.stats.current_balance });
                    }
                }
            } catch (error) {
                console.error('Error loading user stats:', error);
                // Set a default balance for demo
                gameState.tokenBalance = 0;
                updateTokenDisplay();
            }
        }

        function notifyParentWindow(type, data) {
            try {
                if (window.parent && window.parent !== window) {
                    const message = { type };
                    if (data && typeof data === 'object') {
                        Object.assign(message, data);
                    }
                    console.log('Sending message to parent:', JSON.stringify(message, null, 2));
                    window.parent.postMessage(message, '*');
                }
            } catch (error) {
                console.error('Error notifying parent window:', error);
            }
        }
    </script>
</body>
</html>
