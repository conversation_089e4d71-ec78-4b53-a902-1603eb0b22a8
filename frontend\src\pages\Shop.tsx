import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { CartContext } from '../context/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { formatINR } from '../utils/currencyFormatter';

// Helper function to get query parameters
function useQuery() {
  return new URLSearchParams(useLocation().search);
}

// Define interfaces for type safety
interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image?: string;
}

interface ProductImage {
  id: number;
  image: string;
  alt_text?: string;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  compare_price?: number;
  discount_percentage?: number;
  stock: number;
  categories: Category[];
  image?: string;
  main_image?: ProductImage;
}

const Shop = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [addingToCart, setAddingToCart] = useState<{ [key: string]: boolean }>({});
  const [addToCartSuccess, setAddToCartSuccess] = useState<{ [key: string]: boolean }>({});
  const [addToCartError, setAddToCartError] = useState<{ [key: string]: string }>({});
  const query = useQuery();
  const categorySlug = query.get('category');
  const [activeCategory, setActiveCategory] = useState<string | null>(categorySlug);
  const navigate = useNavigate();

  // Get contexts
  const { addToCart } = useContext(CartContext);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    // Update active category when URL parameter changes
    setActiveCategory(categorySlug);
  }, [categorySlug]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        console.log('Fetching categories from:', `${process.env.REACT_APP_API_URL}/api/products/categories/`);
        const res = await axios.get(`${process.env.REACT_APP_API_URL}/api/products/categories/`);
        console.log('Categories response:', res.data);
        setCategories(res.data.results || res.data);
      } catch (err) {
        console.error('Failed to load categories', err);
      }
    };

    fetchCategories();
  }, []);

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        let url = `${process.env.REACT_APP_API_URL}/api/products/items/`;
        if (activeCategory) {
          url += `?categories__slug=${activeCategory}`;
        }
        console.log('Fetching products from:', url);
        const res = await axios.get(url);
        console.log('Products response:', res.data);
        setProducts(res.data.results || res.data);
        setLoading(false);
      } catch (err: any) {
        console.error('Failed to load products:', err);
        setError(err.message || 'Failed to load products');
        setLoading(false);
      }
    };

    fetchProducts();
  }, [activeCategory]);

  // Handle adding product to cart
  const handleAddToCart = async (productId: string) => {
    // Reset states for this product
    setAddToCartSuccess(prev => ({ ...prev, [productId]: false }));
    setAddToCartError(prev => ({ ...prev, [productId]: '' }));

    // Check if user is authenticated
    if (!isAuthenticated) {
      // Redirect to login page with return URL
      const returnUrl = window.location.pathname + window.location.search;
      navigate(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
      return;
    }

    try {
      setAddingToCart(prev => ({ ...prev, [productId]: true }));
      await addToCart(productId, 1);

      // Show success message
      setAddToCartSuccess(prev => ({ ...prev, [productId]: true }));

      // Hide success message after 3 seconds
      setTimeout(() => {
        setAddToCartSuccess(prev => ({ ...prev, [productId]: false }));
      }, 3000);
    } catch (err: any) {
      console.error('Failed to add product to cart:', err);

      // Show error message
      setAddToCartError(prev => ({
        ...prev,
        [productId]: err.message || 'Failed to add product to cart'
      }));

      // Hide error message after 3 seconds
      setTimeout(() => {
        setAddToCartError(prev => ({ ...prev, [productId]: '' }));
      }, 3000);
    } finally {
      setAddingToCart(prev => ({ ...prev, [productId]: false }));
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex justify-center items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-600 border-t-transparent mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Amazing Products</h2>
          <p className="text-gray-600">Please wait while we fetch the latest items...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex justify-center items-center">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Oops! Something went wrong</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
            >
              <span className="mr-2">🔄</span>
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Hero Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700">
        {/* Background decorative elements */}
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-64 h-64 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>

        <div className="relative container mx-auto px-4 py-12">
          <div className="text-white text-center">
            <div className="flex items-center justify-center mb-4">
              <span className="text-5xl mr-4">🛍️</span>
              <div>
                <h1 className="text-4xl font-bold mb-2">
                  {activeCategory ?
                    categories.find((cat) => cat.slug === activeCategory)?.name || 'Shop'
                    : 'All Products'}
                </h1>
                <p className="text-xl text-blue-100">
                  {activeCategory
                    ? `Browse our ${categories.find((cat) => cat.slug === activeCategory)?.name?.toLowerCase()} collection`
                    : 'Discover our complete product collection'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">

        {/* Category filters */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
            <div className="flex items-center mb-6">
              <span className="text-3xl mr-4">🏷️</span>
              <h2 className="text-2xl font-bold text-gray-900">Filter by Category</h2>
            </div>
            <div className="flex flex-wrap gap-3">
              <Link
                to="/shop"
                className={`px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-200 ${
                  !activeCategory
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg transform scale-105'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105'
                }`}
              >
                <span className="mr-2">🌟</span>
                All Products
              </Link>
              {categories.map((category) => (
                <Link
                  key={category.id}
                  to={`/shop?category=${category.slug}`}
                  className={`px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-200 ${
                    activeCategory === category.slug
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg transform scale-105'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105'
                  }`}
                >
                  {category.name}
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Products grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
          {products.length > 0 ? (
            products.map((product) => {
              // Calculate discount percentage
              const discountPercentage = product.compare_price && product.price < product.compare_price
                ? Math.round(((product.compare_price - product.price) / product.compare_price) * 100)
                : 0;

              return (
                <div key={product.id} className="group">
                  <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-300 border border-gray-100 relative">
                    <Link to={`/product/${product.slug}`}>
                      <div className="relative overflow-hidden">
                        <img
                          src={product.main_image?.image || product.image || 'https://via.placeholder.com/300x200?text=No+Image'}
                          alt={product.main_image?.alt_text || product.name}
                          className="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-300"
                          onLoad={() => {
                            console.log(`✅ Shop image loaded for: ${product.name}`);
                          }}
                          onError={(e) => {
                            console.error(`❌ Shop image failed for: ${product.name}`, product.main_image?.image);
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = 'https://via.placeholder.com/300x200?text=Image+Error';
                          }}
                        />
                        {/* Overlay on hover */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                      </div>
                    </Link>

                    {/* Discount Badge */}
                    {discountPercentage > 0 && (
                      <div className="absolute top-3 right-3 bg-gradient-to-r from-red-500 to-pink-600 text-white px-3 py-1 text-xs font-bold rounded-full shadow-lg z-10">
                        🔥 {discountPercentage}% OFF
                      </div>
                    )}

                    <div className="p-6">
                      <Link to={`/product/${product.slug}`}>
                        <h3 className="text-xl font-bold text-gray-900 mb-3 hover:text-purple-600 transition-colors duration-200">{product.name}</h3>
                      </Link>
                      <p className="text-gray-600 mb-4 line-clamp-2 text-sm">{product.description}</p>

                      <div className="space-y-4">
                        {/* Price Display */}
                        <div>
                          {product.compare_price && product.price < product.compare_price ? (
                            <div className="flex items-center space-x-2">
                              {/* Discounted Price */}
                              <span className="text-2xl font-bold text-purple-600">{formatINR(product.price)}</span>
                              {/* Original Price - Crossed out */}
                              <span className="text-sm text-gray-500 line-through">{formatINR(product.compare_price)}</span>
                            </div>
                          ) : (
                            /* Regular Price */
                            <span className="text-2xl font-bold text-gray-900">{formatINR(product.price)}</span>
                          )}
                        </div>

                        {/* Stock Status */}
                        <div>
                          {product.stock > 0 ? (
                            <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                              ✅ In Stock
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-3 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">
                              ❌ Out of Stock
                            </span>
                          )}
                        </div>

                        {/* Add to Cart Button */}
                        <div>
                          <button
                            onClick={() => handleAddToCart(product.id)}
                            disabled={product.stock <= 0 || addingToCart[product.id]}
                            className={`w-full py-3 px-6 rounded-xl text-sm font-semibold transition-all duration-300 ${
                              product.stock <= 0
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:transform-none'
                            }`}
                          >
                            {addingToCart[product.id] ? (
                              <span className="flex items-center justify-center">
                                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                                Adding...
                              </span>
                            ) : product.stock <= 0 ? (
                              <span className="flex items-center justify-center">
                                <span className="mr-2">❌</span>
                                Out of Stock
                              </span>
                            ) : (
                              <span className="flex items-center justify-center">
                                <span className="mr-2">🛒</span>
                                Add to Cart
                              </span>
                            )}
                          </button>
                        </div>

                        {/* Success message */}
                        {addToCartSuccess[product.id] && (
                          <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-xl text-sm font-medium">
                            <span className="mr-2">✅</span>
                            Product added to cart successfully!
                          </div>
                        )}

                        {/* Error message */}
                        {addToCartError[product.id] && (
                          <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-xl text-sm font-medium">
                            <span className="mr-2">⚠️</span>
                            {addToCartError[product.id]}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="col-span-full flex justify-center py-16">
              <div className="max-w-md mx-auto bg-white rounded-2xl shadow-2xl p-12 border border-gray-100 text-center">
                <span className="text-8xl mb-6 block">🔍</span>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">No Products Found</h3>
                <p className="text-gray-600 mb-8">
                  {activeCategory
                    ? `No products found in the ${categories.find((cat) => cat.slug === activeCategory)?.name?.toLowerCase()} category.`
                    : 'No products are available at the moment.'
                  }
                </p>
                <Link
                  to="/shop"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🌟</span>
                  View All Products
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Shop;