import React, { useState } from 'react';
import axios from 'axios';

const ApiTest: React.FC = () => {
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);

  const testApiConnection = async () => {
    setLoading(true);
    setResult('');
    
    const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
    
    try {
      console.log('Testing API connection...');
      console.log('Base URL:', baseUrl);
      
      // Test 1: API Root
      const apiRootUrl = `${baseUrl}/api/`;
      console.log('Testing API root:', apiRootUrl);
      
      const response = await axios.get(apiRootUrl);
      console.log('API Root Response:', response.data);
      
      setResult(`✅ API Connection Successful!\n\nBase URL: ${baseUrl}\nAPI Root: ${apiRootUrl}\nResponse: ${JSON.stringify(response.data, null, 2)}`);
      
    } catch (error: any) {
      console.error('API Test Error:', error);
      
      let errorMessage = `❌ API Connection Failed!\n\nBase URL: ${baseUrl}\nError: ${error.message}`;
      
      if (error.response) {
        errorMessage += `\nStatus: ${error.response.status}\nData: ${JSON.stringify(error.response.data, null, 2)}`;
      } else if (error.request) {
        errorMessage += `\nNo response received. Check if backend is running.`;
      }
      
      setResult(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    setResult('');
    
    const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
    
    try {
      console.log('Testing login...');
      
      const loginUrl = `${baseUrl}/api/auth/jwt/create/`;
      console.log('Login URL:', loginUrl);
      
      const loginData = {
        username: 'phinihas',
        password: '15Sixteen@'
      };
      
      const response = await axios.post(loginUrl, loginData);
      console.log('Login Response:', response.data);
      
      setResult(`✅ Login Successful!\n\nLogin URL: ${loginUrl}\nResponse: ${JSON.stringify(response.data, null, 2)}`);
      
    } catch (error: any) {
      console.error('Login Test Error:', error);
      
      let errorMessage = `❌ Login Failed!\n\nLogin URL: ${baseUrl}/api/auth/jwt/create/\nError: ${error.message}`;
      
      if (error.response) {
        errorMessage += `\nStatus: ${error.response.status}\nData: ${JSON.stringify(error.response.data, null, 2)}`;
      }
      
      setResult(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">API Connection Test</h1>
      
      <div className="mb-4 p-4 bg-blue-100 border border-blue-300 rounded">
        <h2 className="font-bold mb-2">Current Configuration:</h2>
        <p><strong>REACT_APP_API_URL:</strong> {process.env.REACT_APP_API_URL || 'Not set (using Railway deployment)'}</p>
        <p><strong>API Root URL:</strong> {`${process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app'}/api/`}</p>
        <p><strong>Login URL:</strong> {`${process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app'}/api/auth/jwt/create/`}</p>
      </div>
      
      <div className="space-x-4 mb-4">
        <button
          onClick={testApiConnection}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test API Connection'}
        </button>
        
        <button
          onClick={testLogin}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Login'}
        </button>
      </div>
      
      {result && (
        <div className="mt-4 p-4 bg-gray-100 border border-gray-300 rounded">
          <h3 className="font-bold mb-2">Test Result:</h3>
          <pre className="whitespace-pre-wrap text-sm">{result}</pre>
        </div>
      )}
    </div>
  );
};

export default ApiTest;
