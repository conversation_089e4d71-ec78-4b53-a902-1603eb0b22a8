"""
Example Celery tasks for service control.

These tasks demonstrate how to use the service control utilities
to conditionally execute Celery tasks.
"""
import logging
from celery import shared_task
from .utils import safe_task

logger = logging.getLogger(__name__)


@shared_task
def regular_celery_task(message="Hello from Celery"):
    """
    A regular Celery task that will run regardless of service control settings.
    
    Args:
        message: A message to log
        
    Returns:
        str: A success message
    """
    logger.info(f"Regular Celery task executed with message: {message}")
    return f"Task completed: {message}"


@safe_task
@shared_task
def controlled_celery_task(message="Hello from controlled Celery task"):
    """
    A Celery task that will only run if Celery is enabled in service control.
    
    This task is decorated with @safe_task, which checks if Celer<PERSON> is enabled
    before executing the task.
    
    Args:
        message: A message to log
        
    Returns:
        str: A success message
    """
    logger.info(f"Controlled Celery task executed with message: {message}")
    return f"Controlled task completed: {message}"


def example_task_usage():
    """
    Example of how to use the tasks with service control.
    
    This function demonstrates how to call Celery tasks with service control.
    """
    # This task will always be sent to Celery
    regular_celery_task.delay("This will always be sent to Celer<PERSON>")
    
    # This task will only be sent to Celery if <PERSON>lery is enabled in service control
    controlled_celery_task.delay("This will only be sent if Celery is enabled")
    
    # You can also use apply_async for more control
    regular_celery_task.apply_async(
        args=["Using apply_async"],
        countdown=10  # Run after 10 seconds
    )
    
    # The controlled task with apply_async
    controlled_celery_task.apply_async(
        args=["Using apply_async with controlled task"],
        countdown=10  # Run after 10 seconds
    )
