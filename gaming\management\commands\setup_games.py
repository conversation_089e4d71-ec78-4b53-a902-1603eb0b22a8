from django.core.management.base import BaseCommand
from gaming.models import GameType


class Command(BaseCommand):
    help = 'Setup initial game types'

    def handle(self, *args, **options):
        game_types = [
            {
                'name': 'rock_paper_scissors',
                'display_name': 'Rock Paper Scissors',
                'description': 'Classic battle game - Play vs AI or human opponents!',
                'rules': {
                    'choices': ['rock', 'paper', 'scissors'],
                    'win_conditions': {
                        'rock': 'scissors',
                        'paper': 'rock',
                        'scissors': 'paper'
                    },
                    'max_rounds': 3,
                    'token_cost': 2,
                    'win_reward': 5,
                    'draw_reward': 2,
                    'loss_penalty': 1
                }
            },
            {
                'name': 'number_guessing',
                'display_name': 'Number Guessing Battle',
                'description': 'Guess the number - Play vs AI or human opponents!',
                'rules': {
                    'min_number': 1,
                    'max_number': 100,
                    'max_attempts': 5,
                    'token_cost': 2,
                    'win_reward': 5,
                    'draw_reward': 2,
                    'loss_penalty': 1
                }
            },
            {
                'name': 'tic_tac_toe',
                'display_name': '<PERSON><PERSON><PERSON>',
                'description': 'Classic strategy game against AI. Get 3 in a row to win!',
                'rules': {
                    'board_size': 3,
                    'win_condition': '3_in_a_row',
                    'ai_difficulty': 'medium',
                    'token_cost': 2,
                    'win_reward': 5,
                    'draw_reward': 2,
                    'loss_penalty': 1
                }
            },
            {
                'name': 'color_match',
                'display_name': 'Color Match',
                'description': 'Remember color sequences - Play vs AI or human opponents!',
                'rules': {
                    'max_rounds': 5,
                    'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                    'sequence_grows': True,
                    'difficulty_levels': ['easy', 'medium', 'hard'],
                    'token_cost': 2,
                    'win_reward': 5,
                    'draw_reward': 2,
                    'loss_penalty': 1
                }
            },
            {
                'name': 'memory_card',
                'display_name': 'Memory Card Match',
                'description': 'Find matching card pairs - Play vs AI or human opponents!',
                'rules': {
                    'card_pairs': 8,
                    'total_cards': 16,
                    'card_types': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'],
                    'difficulty_levels': ['easy', 'medium', 'hard'],
                    'token_cost': 2,
                    'win_reward': 5,
                    'draw_reward': 2,
                    'loss_penalty': 1
                }
            },
            {
                'name': 'ludo',
                'display_name': 'Ludo',
                'description': '5-minute timer-based Ludo! Make as many valid moves as possible to win.',
                'rules': {
                    'game_duration': 300,
                    'scoring_method': 'move_count',
                    'ai_difficulty': 'medium',
                    'token_cost': 2,
                    'win_reward': 5,
                    'draw_reward': 2,
                    'loss_penalty': 1,
                    'configurable_tokens': [2, 4],
                    'game_modes': ['vs_bot', 'vs_user']
                }
            }
        ]

        for game_data in game_types:
            game_type, created = GameType.objects.get_or_create(
                name=game_data['name'],
                defaults={
                    'display_name': game_data['display_name'],
                    'description': game_data['description'],
                    'rules': game_data['rules'],
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created: {game_type.display_name}')
                )
            else:
                # Update existing game to ensure it's properly configured
                game_type.display_name = game_data['display_name']
                game_type.description = game_data['description']
                game_type.rules = game_data['rules']
                game_type.is_active = True
                game_type.save()
                self.stdout.write(
                    self.style.WARNING(f'🔄 Updated: {game_type.display_name}')
                )

        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS('🎉 All 6 games setup complete!')
        )

        # List all active games
        total_games = GameType.objects.filter(is_active=True).count()
        self.stdout.write(f'Total active games: {total_games}')

        self.stdout.write('\n📋 Active Games:')
        for game in GameType.objects.filter(is_active=True).order_by('name'):
            self.stdout.write(f'   • {game.display_name} ({game.name})')

        self.stdout.write('\n💡 All games are ready to play!')
        self.stdout.write('Color Match and Memory Card games now have full implementations!')
