import React, { useState } from 'react';

interface SimpleTokenPurchaseButtonProps {
  currentBalance: number;
  onTokensAdded: (tokensAdded: number) => void;
  className?: string;
  children?: React.ReactNode;
}

const SimpleTokenPurchaseButton: React.FC<SimpleTokenPurchaseButtonProps> = ({
  currentBalance,
  onTokensAdded,
  className = '',
  children = 'Buy Tokens'
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleClick = () => {
    console.log('🪙 SimpleTokenPurchaseButton clicked!');
    console.log('Current balance:', currentBalance);
    setIsModalOpen(true);
  };

  const handleClose = () => {
    console.log('💰 Modal closed');
    setIsModalOpen(false);
  };

  const handlePurchaseSuccess = (tokens: number) => {
    console.log('✅ Purchase successful, tokens added:', tokens);
    onTokensAdded(tokens);
    setIsModalOpen(false);
  };

  // Simple Modal Component
  const SimpleModal = () => {
    if (!isModalOpen) return null;

    return (
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        style={{ zIndex: 9999 }}
        onClick={handleClose}
      >
        <div 
          className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white p-6 rounded-t-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-3xl mr-3">💰</span>
                <div>
                  <h2 className="text-2xl font-bold">Buy Tokens</h2>
                  <p className="text-emerald-100">Current Balance: {currentBalance} tokens</p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="text-white hover:text-emerald-200 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Choose a Token Pack</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Mock token packs for testing */}
              {[
                { id: '1', name: 'Starter Pack', tokens: 100, price: 10, popular: false },
                { id: '2', name: 'Popular Pack', tokens: 500, price: 45, popular: true },
                { id: '3', name: 'Best Value Pack', tokens: 1000, price: 80, popular: false }
              ].map((pack) => (
                <div
                  key={pack.id}
                  onClick={() => {
                    console.log('Pack selected:', pack);
                    alert(`Selected: ${pack.name}\n${pack.tokens} tokens for ₹${pack.price}\n\nThis is a test - no real payment will be processed.`);
                    // Simulate successful purchase for testing
                    handlePurchaseSuccess(pack.tokens);
                  }}
                  className={`relative cursor-pointer border-2 rounded-xl p-6 transition-all duration-200 hover:scale-105 ${
                    pack.popular
                      ? 'border-emerald-500 bg-emerald-50'
                      : 'border-gray-200 bg-white hover:border-emerald-300'
                  }`}
                >
                  {pack.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                        POPULAR
                      </span>
                    </div>
                  )}

                  <div className="text-center">
                    <div className="text-3xl mb-2">🪙</div>
                    <h4 className="font-bold text-lg text-gray-900 mb-2">{pack.name}</h4>
                    <div className="text-2xl font-bold text-emerald-600 mb-1">
                      {pack.tokens} tokens
                    </div>
                    <div className="text-xl font-semibold text-gray-900 mb-2">
                      ₹{pack.price}
                    </div>
                    <div className="text-sm text-gray-600">
                      {(pack.tokens / pack.price).toFixed(1)} tokens per ₹
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">🧪 Test Mode</h4>
              <p className="text-sm text-blue-700">
                This is a simplified test version. Click any pack to simulate a purchase.
                No real payment will be processed.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <button
        onClick={handleClick}
        className={`font-semibold rounded-xl transition-all duration-200 flex items-center justify-center px-4 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white hover:from-emerald-700 hover:to-teal-700 shadow-lg hover:shadow-xl ${className}`}
      >
        <svg 
          className="w-5 h-5 mr-2" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 6v6m0 0v6m0-6h6m-6 0H6" 
          />
        </svg>
        {children}
      </button>

      <SimpleModal />
    </>
  );
};

export default SimpleTokenPurchaseButton;
