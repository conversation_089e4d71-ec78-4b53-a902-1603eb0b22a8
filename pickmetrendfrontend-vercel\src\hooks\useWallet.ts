import { useState, useEffect, useCallback } from 'react';
import { api } from '../services/api';

interface WalletTransaction {
  id: string;
  transaction_type: string;
  transaction_type_display: string;
  amount: number;
  amount_display: string;
  description: string;
  balance_after: number;
  created_at: string;
  game_id?: string;
  order_id?: string;
}

interface Wallet {
  id: string;
  balance: number;
  balance_in_inr: number;
  total_earned: number;
  total_spent: number;
  created_at: string;
  updated_at: string;
  recent_transactions: WalletTransaction[];
}

interface TokenRedemption {
  available_tokens: number;
  redeemable_tokens: number;
  redeemable_inr: number;
  remaining_amount: number;
  max_redemption_percentage: number;
  min_redemption_tokens: number;
  token_rate: number;
}

export const useWallet = () => {
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [transactions, setTransactions] = useState<WalletTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchWallet = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.get('/api/wallet/');
      setWallet(response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch wallet');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchTransactions = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.get('/api/wallet/transactions/');
      setTransactions(response.data.results || response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch transactions');
    } finally {
      setLoading(false);
    }
  }, []);

  const calculateTokenRedemption = useCallback(async (orderAmount: number): Promise<TokenRedemption | null> => {
    setError(null);

    try {
      const response = await api.post('/api/wallet/calculate-redemption/', {
        order_amount: orderAmount
      });
      return response.data;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to calculate redemption');
      return null;
    }
  }, []);

  const redeemTokens = useCallback(async (tokensToRedeem: number, orderId?: string): Promise<boolean> => {
    setError(null);

    try {
      const response = await api.post('/api/wallet/redeem/', {
        tokens_to_redeem: tokensToRedeem,
        order_id: orderId
      });

      if (response.data.success) {
        // Refresh wallet data
        await fetchWallet();
        return true;
      }
      return false;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to redeem tokens');
      return false;
    }
  }, [fetchWallet]);

  const refreshWallet = useCallback(async () => {
    await Promise.all([fetchWallet(), fetchTransactions()]);
  }, [fetchWallet, fetchTransactions]);

  useEffect(() => {
    refreshWallet();
  }, [refreshWallet]);

  return {
    wallet,
    transactions,
    loading,
    error,
    fetchWallet,
    fetchTransactions,
    calculateTokenRedemption,
    redeemTokens,
    refreshWallet
  };
};
