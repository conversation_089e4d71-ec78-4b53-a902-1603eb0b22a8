import React, { useState, useEffect, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { formatINR } from '../utils/currencyFormatter';
import ShareProduct from '../components/ui/ShareProduct';

// API URL
const BASE_URL = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
const API_URL = `${BASE_URL}/api`;

console.log('ProductDetail API URL:', API_URL);

// Helper function to check if a URL is complete or relative
const getImageUrl = (imageUrl: string) => {
  if (!imageUrl) {
    console.warn('ProductDetail: Empty image URL');
    return 'https://via.placeholder.com/400x400?text=No+Image';
  }

  // If it's already a complete URL (including Printify URLs), return it
  if (imageUrl.startsWith('http')) {
    return imageUrl;
  }

  // If it's a relative URL starting with /media, prepend the API base URL
  if (imageUrl.startsWith('/media')) {
    const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
    return `${baseUrl}${imageUrl}`;
  }

  // For other cases, return as is
  return imageUrl;
};

interface ProductImage {
  id: number;
  image: string;
  alt_text?: string;
  is_primary: boolean;
}

interface Review {
  id: number;
  user: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  rating: number;
  comment: string;
  created_at: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface ProductVariant {
  id: string;
  variant_id: string;
  title: string;
  color?: string;
  size?: string;
  price: number;
  sku?: string;
  options: {
    size?: string;
    color?: string;
    [key: string]: string | undefined;
  };
  is_available: boolean;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  compare_price?: number;
  discount_percentage: number;
  stock: number;
  categories: Category[];
  is_featured: boolean;
  images: ProductImage[];
  reviews: Review[];
  average_rating: number;
  created_at: string;
  updated_at: string;
  printify_id?: string;
  variants?: ProductVariant[];
  available_sizes?: string[];
  available_colors?: string[];
  variantsByColorAndSize?: {[key: string]: ProductVariant};
}

const ProductDetail: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [activeImage, setActiveImage] = useState<number | null>(null);
  const [reviewText, setReviewText] = useState<string>('');
  const [reviewRating, setReviewRating] = useState<number>(5);
  const [submittingReview, setSubmittingReview] = useState<boolean>(false);
  const [reviewError, setReviewError] = useState<string | null>(null);
  const [addingToCart, setAddingToCart] = useState<boolean>(false);
  const [addToCartSuccess, setAddToCartSuccess] = useState<boolean>(false);
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [availableColors, setAvailableColors] = useState<string[]>([]);
  const [availableSizesByColor, setAvailableSizesByColor] = useState<{[color: string]: string[]}>({});
  const [imageLoading, setImageLoading] = useState<boolean>(false);

  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();

  // Memoize image URLs to prevent blinking
  const memoizedImageUrls = useMemo(() => {
    if (!product || !product.images) return {};

    const urls: { [key: number]: string } = {};
    product.images.forEach(image => {
      urls[image.id] = getImageUrl(image.image);
    });
    return urls;
  }, [product?.images]);

  // Memoize active image URL
  const activeImageUrl = useMemo(() => {
    if (!product || !product.images || !activeImage) return '';

    const activeImg = product.images.find(img => img.id === activeImage);
    if (!activeImg) return product.images[0] ? getImageUrl(product.images[0].image) : '';

    return getImageUrl(activeImg.image);
  }, [product?.images, activeImage]);

  // Fetch product details
  useEffect(() => {
    const fetchProduct = async () => {
      if (!slug) return;

      setLoading(true);
      setError(null);

      try {
        console.log(`Fetching product details from: ${API_URL}/products/items/${slug}/`);

        const res = await axios.get(`${API_URL}/products/items/${slug}/`);
        console.log('Product data received:', res.data);
        console.log('Product variants:', res.data.variants);
        console.log('Product images:', res.data.images);

        if (!res.data || !res.data.id) {
          throw new Error('Invalid product data received');
        }

        const productData = res.data;

        // Process variants if available
        if (productData.variants && productData.variants.length > 0) {
          console.log('Product variants:', productData.variants);
          console.log('First variant details:', productData.variants[0]);

          // Extract available colors and sizes from variant titles
          const colors = new Set<string>();
          const sizes = new Set<string>();
          const sizesByColor: {[color: string]: Set<string>} = {};
          const variantsByColorAndSize: {[key: string]: ProductVariant} = {};

          productData.variants.forEach((variant: any, index: number) => {
            console.log(`Processing variant ${index}:`, variant);

            if (variant.title && typeof variant.title === 'string') {
              console.log(`Variant title: "${variant.title}"`);
              const parts = variant.title.split(' / ');
              console.log(`Title parts:`, parts);

              let color = 'Default';
              let size = 'Standard';

              if (parts.length >= 2) {
                // Format: "Color / Size"
                color = parts[0].trim();
                size = parts[1].trim();
                console.log(`Extracted color: "${color}", size: "${size}"`);
              } else {
                // If the title doesn't have the expected format, check if it's just a size
                const commonSizes = ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'];
                if (commonSizes.includes(variant.title.trim())) {
                  size = variant.title.trim();
                  console.log(`Extracted size only: "${size}"`);
                } else {
                  // Assume it's a color if not a common size
                  color = variant.title.trim();
                  console.log(`Extracted color only: "${color}"`);
                }
              }

              // Add to our collections
              if (color) colors.add(color);
              if (size) sizes.add(size);

              // Track sizes available for each color
              if (color) {
                if (!sizesByColor[color]) {
                  sizesByColor[color] = new Set<string>();
                }
                if (size) {
                  sizesByColor[color].add(size);
                }
              }

              // Store variant by color and size for quick lookup
              const key = `${color}|${size}`;
              variantsByColorAndSize[key] = variant;

              // Add options to the variant
              if (!variant.options) {
                variant.options = {};
              }
              variant.options.color = color;
              variant.options.size = size;
            } else {
              console.log(`Variant has no title or title is not a string:`, variant.title);
            }
          });

          // Convert Sets to Arrays
          productData.available_sizes = Array.from(sizes);
          const availableColorsArray = Array.from(colors);

          // Convert sizesByColor to use arrays instead of Sets
          const sizesByColorObj: {[color: string]: string[]} = {};
          Object.keys(sizesByColor).forEach(color => {
            sizesByColorObj[color] = Array.from(sizesByColor[color]);
          });

          console.log('Available colors:', availableColorsArray);
          console.log('Available sizes:', productData.available_sizes);
          console.log('Sizes by color:', sizesByColorObj);

          // Update state with our processed data
          setAvailableColors(availableColorsArray);
          setAvailableSizesByColor(sizesByColorObj);

          // Set default selections if available
          if (availableColorsArray.length > 0) {
            const defaultColor = availableColorsArray[0];
            setSelectedColor(defaultColor);

            // Set a default size based on the selected color
            if (sizesByColorObj[defaultColor] && sizesByColorObj[defaultColor].length > 0) {
              setSelectedSize(sizesByColorObj[defaultColor][0]);
            }
          }

          // Store the processed data on the product for later use
          productData.variantsByColorAndSize = variantsByColorAndSize;

          // Find the default variant based on selected size and color
          updateSelectedVariant(productData);
        }

        // Ensure all price values are numbers
        if (typeof productData.price === 'string') {
          productData.price = parseFloat(productData.price);
          console.log('Converted product price to number:', productData.price);
        }

        if (productData.compare_price && typeof productData.compare_price === 'string') {
          productData.compare_price = parseFloat(productData.compare_price);
          console.log('Converted compare price to number:', productData.compare_price);
        }

        // Ensure all variant prices are numbers
        if (productData.variants && productData.variants.length > 0) {
          productData.variants.forEach((variant: ProductVariant) => {
            if (typeof variant.price === 'string') {
              variant.price = parseFloat(variant.price as unknown as string);
              console.log(`Converted variant ${variant.id} price to number:`, variant.price);
            }
          });
        }

        setProduct(productData);

        // Set the active image to the first image
        if (productData.images && productData.images.length > 0) {
          console.log('Product images:', productData.images);

          // Find primary image or use first one
          const primaryImage = productData.images.find((img: ProductImage) => img.is_primary);
          const selectedImage = primaryImage || productData.images[0];

          console.log('Selected image:', selectedImage);
          setActiveImage(selectedImage.id);

          // Log image URLs for debugging
          productData.images.forEach((img: ProductImage, index: number) => {
            console.log(`Image ${index + 1}:`, img.image);
          });
        } else {
          console.log('No images found for product');
        }
      } catch (err: any) {
        console.error('Error fetching product:', err);

        let errorMessage = 'Failed to load product details. Please try again later.';

        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error('Error response status:', err.response.status);
          console.error('Error response data:', err.response.data);

          if (err.response.status === 404) {
            errorMessage = 'Product not found. It may have been removed or is no longer available.';
          } else if (err.response.status === 500) {
            errorMessage = 'Server error. Please try again later.';
          }
        } else if (err.request) {
          // The request was made but no response was received
          console.error('No response received:', err.request);
          errorMessage = 'No response from server. Please check your internet connection.';
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error('Error message:', err.message);
          errorMessage = `Error: ${err.message}`;
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [slug]);

  // Update selected variant when size or color changes
  useEffect(() => {
    if (product) {
      updateSelectedVariant();
    }
  }, [selectedSize, selectedColor]);

  // Helper function to extract size from variant title
  const extractSizeFromTitle = (title: string): string => {
    if (!title) return 'Standard';

    // Variant titles are typically in format "Color / Size"
    const parts = title.split(' / ');
    if (parts.length >= 2) {
      return parts[1].trim();
    }

    // If the title doesn't have the expected format, check if it's just a size
    const commonSizes = ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'];
    if (commonSizes.includes(title.trim())) {
      return title.trim();
    }

    // For other formats, just return the title as is
    return title;
  };

  // Helper function to extract color from variant title
  const extractColorFromTitle = (title: string): string => {
    if (!title) return 'Default';

    // Variant titles are typically in format "Color / Size"
    const parts = title.split(' / ');
    if (parts.length >= 2) {
      return parts[0].trim();
    }

    // If the title doesn't match the expected format, return a default value
    return 'Default';
  };

  // Update the selected variant based on color and size
  const updateSelectedVariant = (productData: Product = product!) => {
    if (!productData || !productData.variants || productData.variants.length === 0) return;

    console.log('Updating selected variant with color:', selectedColor, 'and size:', selectedSize);

    let variant: ProductVariant | undefined;

    // First try to find a variant using the lookup table
    if (productData.variantsByColorAndSize) {
      const key = `${selectedColor}|${selectedSize}`;
      variant = productData.variantsByColorAndSize[key];
      console.log(`Looking up variant with key "${key}":`, variant);
    }

    // If not found in the lookup table, search through all variants
    if (!variant) {
      console.log('Variant not found in lookup table, searching all variants');
      variant = productData.variants.find((v: any) => {
        const colorMatch = v.options?.color === selectedColor;
        const sizeMatch = v.options?.size === selectedSize;
        console.log(`Checking variant ${v.id}: color match=${colorMatch}, size match=${sizeMatch}`);
        return colorMatch && sizeMatch;
      });
    }

    // If still no match, try to find a variant with just the selected size
    if (!variant) {
      console.log('No exact match found, looking for variant with matching size only');
      variant = productData.variants.find((v: any) => v.options?.size === selectedSize);
    }

    // If still no match, try to find a variant with just the selected color
    if (!variant) {
      console.log('No size match found, looking for variant with matching color only');
      variant = productData.variants.find((v: any) => v.options?.color === selectedColor);
    }

    // If still no match, use the first variant
    if (!variant && productData.variants.length > 0) {
      console.log('No matching variant found, using first variant');
      variant = productData.variants[0];
    }

    console.log('Selected variant:', variant);
    if (variant) {
      console.log('Variant price:', variant.price, typeof variant.price);
      // Ensure the variant price is a number
      if (typeof variant.price === 'string') {
        variant.price = parseFloat(variant.price as unknown as string);
        console.log('Converted variant price to number:', variant.price);
      }
    }

    setSelectedVariant(variant || null);
  };

  // Handle size selection
  const handleSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = e.target.value;
    setSelectedSize(newSize);
    updateSelectedVariant();
  };

  // Handle color selection
  const handleColorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newColor = e.target.value;
    setSelectedColor(newColor);

    // Update available sizes for this color
    if (product && availableSizesByColor[newColor] && availableSizesByColor[newColor].length > 0) {
      // If the current selected size is not available for this color, select the first available size
      if (!availableSizesByColor[newColor].includes(selectedSize)) {
        setSelectedSize(availableSizesByColor[newColor][0]);
      }
    }

    updateSelectedVariant();
  };

  // Handle quantity changes
  const handleQuantityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setQuantity(parseInt(e.target.value));
  };

  // Handle add to cart
  const handleAddToCart = async () => {
    if (!product) return;

    // Check if we need to select a variant
    if (product.variants && product.variants.length > 0 && !selectedVariant) {
      alert('Please select size and color options before adding to cart');
      return;
    }

    console.log('Product object:', product);
    console.log('Product ID type:', typeof product.id);
    console.log('Product ID value:', product.id);
    console.log('Selected variant:', selectedVariant);

    setAddingToCart(true);
    try {
      // Since product.id is defined as string in the Product interface, we can use it directly
      const productId = product.id;

      // Prepare cart data - use variant_id (Printify ID) instead of id (database ID)
      const variantId = selectedVariant?.variant_id || selectedVariant?.id;
      const cartData = {
        productId,
        quantity,
        variantId: variantId,
        options: {
          size: selectedSize,
          color: selectedColor
        }
      };

      console.log('Adding to cart with data:', cartData);
      console.log('Selected variant full object:', selectedVariant);
      console.log('Using variant_id:', variantId);

      // Get the token to check authentication
      const token = localStorage.getItem('access_token');
      console.log('Authentication token exists:', !!token);

      // Pass the variant ID and options if a variant is selected
      await addToCart(productId, quantity, variantId, {
        size: selectedSize,
        color: selectedColor
      });
      console.log('Product successfully added to cart with variant ID:', selectedVariant?.id, 'and options:', { size: selectedSize, color: selectedColor });
      setAddToCartSuccess(true);

      // Reset success message after 3 seconds
      setTimeout(() => {
        setAddToCartSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Error adding to cart:', err);
      alert('Failed to add product to cart. Please check the console for details.');
    } finally {
      setAddingToCart(false);
    }
  };

  // Handle review submission
  const handleReviewSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!product || !isAuthenticated) return;

    setSubmittingReview(true);
    try {
      await axios.post(`${API_URL}/products/items/${product.slug}/review/`, {
        rating: reviewRating,
        comment: reviewText
      });

      // Refresh product data to show new review
      const res = await axios.get(`${API_URL}/products/items/${slug}/`);
      setProduct(res.data);

      // Reset form
      setReviewText('');
      setReviewRating(5);
      setReviewError(null);
    } catch (err: any) {
      console.error('Error submitting review:', err);
      setReviewError(err.response?.data?.detail || 'Failed to submit review. Please try again.');
    } finally {
      setSubmittingReview(false);
    }
  };

  // Return loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-160px)]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Return error state
  if (error || !product) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error || 'Product not found'}
        </div>
        <div className="mt-6">
          <Link to="/shop" className="text-primary-600 hover:text-primary-500">
            ← Back to products
          </Link>
        </div>
      </div>
    );
  }

  // Helper function to render star ratings
  const renderStarRating = (rating: number, max: number = 5) => {
    return (
      <div className="flex items-center">
        {Array.from({ length: max }).map((_, i) => (
          <svg
            key={i}
            className={`h-5 w-5 ${
              i < rating ? 'text-yellow-400' : 'text-gray-300'
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M10 15.934l-6.18 3.254.183-7.32L.003 7.936l7.243-1.053L10 .264l2.754 6.619 7.243 1.053-4 3.932.183 7.32z"
              clipRule="evenodd"
            />
          </svg>
        ))}
      </div>
    );
  };

  // Debug logs outside the JSX
  console.log('Product price:', product.price, typeof product.price);
  if (selectedVariant) {
    console.log('Selected variant price:', selectedVariant.price, typeof selectedVariant.price);
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Breadcrumbs */}
      <nav className="flex mb-8" aria-label="Breadcrumb">
        <ol className="flex items-center space-x-2">
          <li>
            <Link to="/" className="text-gray-500 hover:text-gray-700">Home</Link>
          </li>
          <li className="flex items-center">
            <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
            <Link to="/shop" className="ml-2 text-gray-500 hover:text-gray-700">Shop</Link>
          </li>
          {product.categories && product.categories.length > 0 && (
            <li className="flex items-center">
              <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <Link to={`/shop?category=${product.categories[0].slug}`} className="ml-2 text-gray-500 hover:text-gray-700">
                {product.categories[0].name}
              </Link>
            </li>
          )}
          <li className="flex items-center">
            <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
            <span className="ml-2 text-gray-800 font-medium">{product.name}</span>
          </li>
        </ol>
      </nav>

      {/* Product Detail */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="bg-gray-100 rounded-lg overflow-hidden relative">
            {activeImageUrl ? (
              <>
                {imageLoading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  </div>
                )}
                <img
                  src={activeImageUrl}
                  alt={product.name}
                  className="w-full h-96 object-contain"
                  onLoadStart={() => setImageLoading(true)}
                  onLoad={() => {
                    setImageLoading(false);
                    console.log(`✅ Main image loaded successfully for ${product.name}`);
                  }}
                  onError={(e) => {
                    setImageLoading(false);
                    console.error(`❌ Failed to load main image for ${product.name}`);
                    const target = e.target as HTMLImageElement;
                    target.onerror = null; // Prevent infinite loops
                    target.src = 'https://via.placeholder.com/400x400?text=No+Image';
                  }}
                />
              </>
            ) : (
              <div className="w-full h-96 flex items-center justify-center bg-gray-200">
                <span className="text-gray-400">No image available</span>
              </div>
            )}
          </div>

          {/* Thumbnail Images */}
          {product.images.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {product.images.map(image => (
                <button
                  key={image.id}
                  onClick={() => setActiveImage(image.id)}
                  className={`bg-gray-100 rounded border-2 ${
                    activeImage === image.id ? 'border-primary-500' : 'border-transparent'
                  }`}
                >
                  <img
                    src={memoizedImageUrls[image.id] || 'https://via.placeholder.com/100x100?text=No+Image'}
                    alt={image.alt_text || `${product.name} - view ${image.id}`}
                    className="w-full h-20 object-cover object-center"
                    onLoad={() => {
                      console.log(`✅ Thumbnail loaded for ${product.name} - Image ${image.id}`);
                    }}
                    onError={(e) => {
                      console.error(`❌ Failed to load thumbnail for ${product.name} - Image ${image.id}`);
                      const target = e.target as HTMLImageElement;
                      target.onerror = null; // Prevent infinite loops
                      target.src = 'https://via.placeholder.com/100x100?text=No+Image';
                    }}
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>

          {/* Rating */}
          <div className="mt-2 flex items-center">
            {renderStarRating(product.average_rating)}
            <span className="ml-2 text-sm text-gray-500">
              {product.reviews.length} {product.reviews.length === 1 ? 'review' : 'reviews'}
            </span>
          </div>

          {/* Price */}
          <div className="mt-4 flex items-center">
            <p className="text-2xl font-bold text-gray-900">
              {selectedVariant ? formatINR(selectedVariant.price) : formatINR(product.price)}
            </p>
            {product.compare_price && product.price < product.compare_price && (
              <>
                <p className="ml-3 text-lg text-gray-500 line-through">{formatINR(product.compare_price)}</p>
                <div className="ml-3 bg-red-100 text-red-800 text-sm font-medium px-2 py-0.5 rounded">
                  Save {product.discount_percentage}%
                </div>
              </>
            )}
          </div>

          {/* Share Product */}
          <div className="mt-6">
            <ShareProduct
              productName={product.name}
              productUrl={`/product/${product.slug}`}
              productPrice={selectedVariant ? formatINR(selectedVariant.price) : formatINR(product.price)}
              productImage={activeImageUrl}
              className="inline-block"
            />
          </div>

          {/* Description */}
          <div className="mt-6 text-gray-700">
            <p>{product.description}</p>
          </div>

          {/* Availability */}
          <div className="mt-6">
            <p className="text-sm text-gray-700">
              Availability:
              {product.stock > 0 ? (
                <span className="text-green-600 font-medium ml-1">In Stock ({product.stock} available)</span>
              ) : (
                <span className="text-red-600 font-medium ml-1">Out of Stock</span>
              )}
            </p>
          </div>

          {/* Add to Cart */}
          <div className="mt-6">
            {product.stock > 0 ? (
              <form className="flex flex-col space-y-4">
                {/* Color Selection */}
                {availableColors && availableColors.length > 0 && (
                  <div className="mb-4">
                    <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-1">
                      Color
                    </label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {availableColors.map(color => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => {
                            const e = { target: { value: color } } as React.ChangeEvent<HTMLSelectElement>;
                            handleColorChange(e);
                          }}
                          className={`px-3 py-1 border rounded-md ${
                            selectedColor === color
                              ? 'border-primary-500 bg-primary-50 text-primary-700'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Size Selection */}
                {selectedColor && availableSizesByColor[selectedColor] && availableSizesByColor[selectedColor].length > 0 && (
                  <div className="mb-4">
                    <label htmlFor="size" className="block text-sm font-medium text-gray-700 mb-1">
                      Size
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {availableSizesByColor[selectedColor].map(size => (
                        <button
                          key={size}
                          type="button"
                          onClick={() => {
                            const e = { target: { value: size } } as React.ChangeEvent<HTMLSelectElement>;
                            handleSizeChange(e);
                          }}
                          className={`px-3 py-1 border rounded-md ${
                            selectedSize === size
                              ? 'border-primary-500 bg-primary-50 text-primary-700'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quantity Selection */}
                <div className="flex items-center">
                  <label htmlFor="quantity" className="mr-4 text-sm font-medium text-gray-700">
                    Quantity:
                  </label>
                  <select
                    id="quantity"
                    name="quantity"
                    value={quantity}
                    onChange={handleQuantityChange}
                    className="max-w-xs rounded-md border-gray-300 py-1.5 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500"
                  >
                    {Array.from({ length: Math.min(10, product.stock) }, (_, i) => i + 1).map(num => (
                      <option key={num} value={num}>
                        {num}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Selected Variant Info */}
                {selectedVariant && (
                  <div className="text-sm text-gray-600 mt-2 p-3 bg-gray-50 rounded-md">
                    <p className="font-medium text-gray-700">Selected Options:</p>
                    <div className="mt-1 grid grid-cols-2 gap-2">
                      <div>
                        <span className="font-medium">Color:</span> {selectedVariant.options?.color || extractColorFromTitle(selectedVariant.title)}
                      </div>
                      <div>
                        <span className="font-medium">Size:</span> {selectedVariant.options?.size || extractSizeFromTitle(selectedVariant.title)}
                      </div>
                      <div className="col-span-2">
                        <span className="font-medium">Price:</span> {formatINR(selectedVariant.price)}
                      </div>
                      {selectedVariant.is_available === false && (
                        <div className="col-span-2 text-red-600">
                          This variant is currently out of stock
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <button
                  type="button"
                  onClick={handleAddToCart}
                  disabled={
                    addingToCart ||
                    (product.variants && product.variants.length > 0 && !selectedVariant) ||
                    (selectedVariant && selectedVariant.is_available === false) ||
                    false
                  }
                  className="flex items-center justify-center w-full bg-primary-600 border border-transparent rounded-md py-3 px-8 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:bg-primary-400 disabled:cursor-not-allowed"
                  title={selectedVariant && selectedVariant.is_available === false ? "This variant is out of stock" : ""}
                >
                  {addingToCart ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Adding...
                    </>
                  ) : selectedVariant && selectedVariant.is_available === false ? (
                    'Out of Stock'
                  ) : (
                    'Add to Cart'
                  )}
                </button>

                {addToCartSuccess && (
                  <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md text-sm">
                    Product added to cart successfully!
                  </div>
                )}
              </form>
            ) : (
              <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md">
                This product is currently out of stock. Please check back later.
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Product Reviews */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-8">Customer Reviews</h2>

        {/* Review List */}
        {product.reviews.length === 0 ? (
          <div className="bg-gray-50 rounded-lg p-6 text-center">
            <p className="text-gray-500">No reviews yet. Be the first to review this product!</p>
          </div>
        ) : (
          <div className="space-y-8">
            {product.reviews.map(review => (
              <div key={review.id} className="border-b border-gray-200 pb-8">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-gray-500 text-white">
                      <span className="text-xl font-medium">
                        {review.user.username.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-medium text-gray-900">
                      {review.user.username}
                    </h4>
                    <div className="mt-1 flex items-center">
                      {renderStarRating(review.rating)}
                      <span className="ml-2 text-sm text-gray-500">
                        {new Date(review.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="mt-2 text-gray-700">
                      <p>{review.comment}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add Review Form */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Write a Review</h3>

          {isAuthenticated ? (
            <form onSubmit={handleReviewSubmit}>
              {reviewError && (
                <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                  {reviewError}
                </div>
              )}

              <div className="mb-4">
                <label htmlFor="rating" className="block text-sm font-medium text-gray-700 mb-1">
                  Rating
                </label>
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map(star => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setReviewRating(star)}
                      className="p-1 focus:outline-none focus:ring-0"
                    >
                      <svg
                        className={`h-8 w-8 ${
                          star <= reviewRating ? 'text-yellow-400' : 'text-gray-300'
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 15.934l-6.18 3.254.183-7.32L.003 7.936l7.243-1.053L10 .264l2.754 6.619 7.243 1.053-4 3.932.183 7.32z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  ))}
                </div>
              </div>

              <div className="mb-4">
                <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-1">
                  Your Review
                </label>
                <textarea
                  id="comment"
                  name="comment"
                  rows={4}
                  required
                  value={reviewText}
                  onChange={e => setReviewText(e.target.value)}
                  className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  placeholder="Share your experience with this product..."
                />
              </div>

              <button
                type="submit"
                disabled={submittingReview}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:bg-primary-400"
              >
                {submittingReview ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Submitting...
                  </>
                ) : (
                  'Submit Review'
                )}
              </button>
            </form>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600 mb-4">Please sign in to leave a review.</p>
              <Link
                to="/login"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                Sign In
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;