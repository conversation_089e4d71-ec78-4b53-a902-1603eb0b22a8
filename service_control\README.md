# Service Control

A Django app that allows admin users to enable or disable Celery and Redis services through a backend dashboard.

## Features

- Toggle Celery and Redis services on/off
- Admin interface with visual indicators
- API endpoints for programmatic control
- Utility functions for checking service status
- Decorator for conditional Celery task execution

## Installation

1. Add `service_control` to your `INSTALLED_APPS` in `settings.py`:

```python
INSTALLED_APPS = [
    # ...
    'service_control',
    # ...
]
```

2. Include the service_control URLs in your project's `urls.py`:

```python
urlpatterns = [
    # ...
    path('service-control/', include('service_control.urls')),
    # ...
]
```

3. Run migrations:

```bash
python manage.py migrate service_control
```

## Usage

### Admin Interface

The app provides an admin interface for managing service statuses. You can access it at:

- `/admin/service_control/servicecontrol/` - List view
- `/admin/service_control/servicecontrol/dashboard/` - Dashboard view

### API Endpoints

The app provides the following API endpoints:

- `GET /service-control/status/` - Get current status of services
- `POST /service-control/toggle-celery/` - Toggle Celery enabled/disabled
- `POST /service-control/toggle-redis/` - Toggle Redis enabled/disabled

All API endpoints require admin or staff authentication.

### Dashboard

A user-friendly dashboard is available at:

- `/service-control/dashboard/`

This dashboard provides toggle buttons and status indicators for each service.

### Utility Functions

The app provides utility functions for checking service status:

```python
from service_control.utils import is_celery_enabled, is_redis_enabled

# Check if Celery is enabled
if is_celery_enabled():
    # Do something with Celery
    
# Check if Redis is enabled
if is_redis_enabled():
    # Do something with Redis
```

### Safe Task Decorator

The app provides a decorator for conditional Celery task execution:

```python
from celery import shared_task
from service_control.utils import safe_task

@safe_task
@shared_task
def my_task():
    # This task will only be executed if Celery is enabled
    pass
```

## Example Integration with Celery

Here's an example of how to use the service control with Celery tasks:

```python
from celery import shared_task
from service_control.utils import safe_task, is_celery_enabled

# Regular Celery task (will always be sent to Celery)
@shared_task
def regular_task():
    return "Regular task executed"

# Controlled Celery task (will only be sent to Celery if enabled)
@safe_task
@shared_task
def controlled_task():
    return "Controlled task executed"

# Example usage in a view or other function
def process_something():
    # This will always be sent to Celery
    regular_task.delay()
    
    # This will only be sent to Celery if enabled
    controlled_task.delay()
    
    # Alternative approach: check manually
    if is_celery_enabled():
        regular_task.delay()
    else:
        # Execute synchronously or handle differently
        regular_task()
```

## Security

All API endpoints and the dashboard are protected with Django's authentication system. Only admin and staff users can access them.

## License

This app is licensed under the MIT License.
