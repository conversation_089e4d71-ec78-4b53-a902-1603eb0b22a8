import React, { useState, useEffect } from 'react';
import { testImageUrl, debugImageUrl, getImageUrl } from '../../utils/imageUtils';

interface ImageValidatorProps {
  imageUrl: string;
  productName?: string;
  onValidationComplete?: (isValid: boolean) => void;
  showDebugInfo?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  loadTime: number;
  error?: string;
  debugInfo: any;
}

const ImageValidator: React.FC<ImageValidatorProps> = ({
  imageUrl,
  productName = 'Unknown Product',
  onValidationComplete,
  showDebugInfo = false
}) => {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    if (imageUrl) {
      validateImage();
    }
  }, [imageUrl]);

  const validateImage = async () => {
    setIsValidating(true);
    const startTime = Date.now();

    try {
      const debugInfo = debugImageUrl(imageUrl);
      const isValid = await testImageUrl(imageUrl, 15000);
      const loadTime = Date.now() - startTime;

      const result: ValidationResult = {
        isValid,
        loadTime,
        debugInfo
      };

      setValidationResult(result);
      onValidationComplete?.(isValid);

      // Log detailed results
      console.group(`🖼️ Image Validation: ${productName}`);
      console.log('Original URL:', imageUrl);
      console.log('Processed URL:', getImageUrl(imageUrl));
      console.log('Is Valid:', isValid);
      console.log('Load Time:', `${loadTime}ms`);
      console.log('Debug Info:', debugInfo);
      console.groupEnd();

    } catch (error) {
      const loadTime = Date.now() - startTime;
      const result: ValidationResult = {
        isValid: false,
        loadTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        debugInfo: debugImageUrl(imageUrl)
      };

      setValidationResult(result);
      onValidationComplete?.(false);

      console.error(`❌ Image validation failed for ${productName}:`, error);
    } finally {
      setIsValidating(false);
    }
  };

  if (!showDebugInfo) {
    return null;
  }

  return (
    <div className="image-validator p-4 bg-gray-100 rounded-lg mt-4">
      <h4 className="font-semibold text-gray-800 mb-2">
        🔍 Image Validation: {productName}
      </h4>
      
      {isValidating && (
        <div className="flex items-center text-blue-600">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
          Validating image...
        </div>
      )}

      {validationResult && (
        <div className="space-y-2">
          <div className={`flex items-center ${validationResult.isValid ? 'text-green-600' : 'text-red-600'}`}>
            <span className="mr-2">
              {validationResult.isValid ? '✅' : '❌'}
            </span>
            <span className="font-medium">
              {validationResult.isValid ? 'Image Valid' : 'Image Invalid'}
            </span>
            <span className="ml-2 text-gray-500 text-sm">
              ({validationResult.loadTime}ms)
            </span>
          </div>

          {validationResult.error && (
            <div className="text-red-600 text-sm">
              Error: {validationResult.error}
            </div>
          )}

          <div className="text-sm text-gray-600">
            <div><strong>Original URL:</strong> {imageUrl.substring(0, 60)}...</div>
            <div><strong>Processed URL:</strong> {getImageUrl(imageUrl).substring(0, 60)}...</div>
            <div><strong>Is External:</strong> {validationResult.debugInfo.isExternal ? 'Yes' : 'No'}</div>
            <div><strong>Is Printify:</strong> {validationResult.debugInfo.isPrintify ? 'Yes' : 'No'}</div>
          </div>

          <button
            onClick={validateImage}
            className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
            disabled={isValidating}
          >
            Re-validate
          </button>
        </div>
      )}
    </div>
  );
};

export default ImageValidator;
