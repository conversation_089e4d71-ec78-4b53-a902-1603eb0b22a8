import React from 'react';
import { useWallet } from '../../hooks/useWallet';
import { formatINR } from '../../utils/currencyFormatter';
import TokenPurchaseButton from './TokenPurchaseButton';

interface WalletBalanceProps {
  showDetails?: boolean;
  className?: string;
  showBuyButton?: boolean;
  lowBalanceThreshold?: number;
}

const WalletBalance: React.FC<WalletBalanceProps> = ({
  showDetails = false,
  className = '',
  showBuyButton = false,
  lowBalanceThreshold = 10
}) => {
  const { wallet, loading, error, refreshWallet } = useWallet();

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-24"></div>
      </div>
    );
  }

  if (error) {
    console.error('Wallet error:', error);
    return (
      <div className={`text-red-500 text-sm ${className}`}>
        Error loading wallet: {error}
      </div>
    );
  }

  if (!wallet) {
    return (
      <div className={`text-gray-500 text-sm ${className}`}>
        No wallet data
      </div>
    );
  }

  const handleTokensAdded = (tokensAdded: number) => {
    refreshWallet();
  };

  const isLowBalance = wallet.balance <= lowBalanceThreshold;

  return (
    <div className={className}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <span className="text-yellow-500">🪙</span>
            <span className="font-semibold text-lg">{wallet.balance}</span>
            <span className="text-sm text-gray-600">tokens</span>
          </div>

          {showDetails && (
            <div className="text-sm text-gray-500">
              (≈ {formatINR(wallet.balance_in_inr)})
            </div>
          )}
        </div>

        {showBuyButton && isLowBalance && (
          <TokenPurchaseButton
            currentBalance={wallet.balance}
            onTokensAdded={handleTokensAdded}
            variant="outline"
            size="sm"
            showIcon={false}
          >
            Buy More
          </TokenPurchaseButton>
        )}
      </div>

      {showDetails && (
        <div className="mt-2 text-xs text-gray-500 space-y-1">
          <div>Total Earned: {wallet.total_earned} tokens</div>
          <div>Total Spent: {wallet.total_spent} tokens</div>
        </div>
      )}

      {showBuyButton && !showDetails && (
        <div className="mt-3">
          <TokenPurchaseButton
            currentBalance={wallet.balance}
            onTokensAdded={handleTokensAdded}
            variant="primary"
            size="md"
            showIcon={true}
          >
            Buy Tokens
          </TokenPurchaseButton>
        </div>
      )}
    </div>
  );
};

export default WalletBalance;
