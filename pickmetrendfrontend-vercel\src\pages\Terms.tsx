import React from 'react';

const Terms: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Terms and Conditions</h1>
      
      <div className="prose prose-lg">
        <p className="italic text-sm mb-4">Last Updated: May 2023</p>
        
        <p>
          By accessing and using the PickMeTrend website, you agree to be bound by the following terms and conditions:
        </p>
        
        <ul className="list-disc pl-6 mb-6 mt-4">
          <li>All products and prices are subject to change without notice.</li>
          <li>We reserve the right to cancel orders at our discretion.</li>
          <li>Users are responsible for providing accurate shipping information.</li>
          <li>We are not liable for delays caused by customs or shipping carriers.</li>
          <li>Use of our website and services is at your own risk.</li>
        </ul>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">1. Agreement to Terms</h2>
        <p>
          By accessing or using our service, you agree to be bound by these Terms. If you disagree with any part of the terms,
          you may not access the service.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">2. Products and Purchases</h2>
        <p>
          All products are subject to availability. We reserve the right to discontinue any product at any time.
          Prices for all products are subject to change without notice. We reserve the right to refuse any order
          placed through our site.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">3. Shipping and Delivery</h2>
        <p>
          Shipping times are estimates and commence from the date of shipping, rather than the date of order.
          We are not responsible for delays outside our control, such as customs delays or postal service issues.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">4. Intellectual Property</h2>
        <p>
          The service and its original content, features, and functionality are and will remain the exclusive property of
          PickMeTrend and its licensors. The service is protected by copyright, trademark, and other laws of both the
          United States and foreign countries.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">5. User Accounts</h2>
        <p>
          When you create an account with us, you must provide accurate, complete, and current information at all times.
          Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account
          on our service.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">6. Limitation of Liability</h2>
        <p>
          In no event shall PickMeTrend, nor its directors, employees, partners, agents, suppliers, or affiliates,
          be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation,
          loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or
          inability to access or use the service.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">7. Governing Law</h2>
        <p>
          These Terms shall be governed and construed in accordance with the laws of the United States,
          without regard to its conflict of law provisions.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">8. Changes to Terms</h2>
        <p>
          We reserve the right, at our sole discretion, to modify or replace these Terms at any time.
          It is your responsibility to check our Terms periodically for changes.
        </p>
        
        <h2 className="text-xl font-semibold mt-6 mb-4">9. Contact Us</h2>
        <p>
          For any questions about these Terms, please contact us at{' '}
          <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-800">
            <EMAIL>
          </a>
        </p>
      </div>
    </div>
  );
};

export default Terms; 