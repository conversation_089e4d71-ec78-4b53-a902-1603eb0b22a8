# Generated by Django 5.0.2 on 2025-06-12 04:17

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gaming', '0002_add_game_session'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SpinWheelReward',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Display name for the reward', max_length=100)),
                ('reward_type', models.CharField(choices=[('tokens', 'Tokens'), ('scratch_card', 'Scratch Card'), ('discount', 'Discount'), ('bonus_spin', 'Bonus Spin')], max_length=20)),
                ('value', models.PositiveIntegerField(help_text='Token amount, discount percentage, etc.')),
                ('probability', models.FloatField(help_text='Probability of getting this reward (0.0 to 1.0)')),
                ('is_active', models.<PERSON>olean<PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('extra_data', models.JSONField(blank=True, default=dict, help_text='Additional reward configuration')),
            ],
            options={
                'ordering': ['-probability'],
            },
        ),
        migrations.CreateModel(
            name='SpinWheelSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('cooldown_hours', models.PositiveIntegerField(default=24, help_text='Hours between spins')),
                ('wheel_segments', models.PositiveIntegerField(default=8, help_text='Number of wheel segments')),
                ('animation_duration', models.PositiveIntegerField(default=3000, help_text='Spin animation duration in ms')),
                ('min_token_reward', models.PositiveIntegerField(default=1)),
                ('max_token_reward', models.PositiveIntegerField(default=50)),
                ('scratch_card_probability', models.FloatField(default=0.2, help_text='Probability of getting scratch card')),
                ('is_active', models.BooleanField(default=True)),
                ('maintenance_mode', models.BooleanField(default=False)),
                ('maintenance_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Spin Wheel Settings',
                'verbose_name_plural': 'Spin Wheel Settings',
            },
        ),
        migrations.CreateModel(
            name='SpinWheelHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('spin_timestamp', models.DateTimeField(auto_now_add=True)),
                ('reward_claimed', models.BooleanField(default=False)),
                ('reward_claimed_at', models.DateTimeField(blank=True, null=True)),
                ('scratch_card_revealed', models.BooleanField(default=False)),
                ('scratch_card_data', models.JSONField(blank=True, default=dict)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spin_history', to=settings.AUTH_USER_MODEL)),
                ('reward', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gaming.spinwheelreward')),
            ],
            options={
                'ordering': ['-spin_timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ScratchCard',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('card_type', models.CharField(choices=[('token_reveal', 'Token Reveal'), ('discount_reveal', 'Discount Reveal'), ('mystery_box', 'Mystery Box')], default='token_reveal', max_length=20)),
                ('hidden_reward', models.JSONField(help_text='The actual reward hidden under the scratch')),
                ('revealed', models.BooleanField(default=False)),
                ('revealed_at', models.DateTimeField(blank=True, null=True)),
                ('card_design', models.CharField(default='default', max_length=50)),
                ('scratch_areas', models.JSONField(default=list, help_text='Areas that need to be scratched')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('spin_history', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='scratch_card', to='gaming.spinwheelhistory')),
            ],
        ),
        migrations.AddIndex(
            model_name='spinwheelhistory',
            index=models.Index(fields=['user', 'spin_timestamp'], name='gaming_spin_user_id_4c02e9_idx'),
        ),
    ]
