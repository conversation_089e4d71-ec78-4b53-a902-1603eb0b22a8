from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.conf import settings
from django.utils import timezone
from decimal import Decimal
from .models import Wallet, WalletTransaction, TokenRequest
from .serializers import (
    WalletSerializer,
    WalletTransactionSerializer,
    TokenRequestSerializer,
    TokenRequestAdminSerializer
)


class WalletDetailView(generics.RetrieveAPIView):
    """
    Get user's wallet details with token economy features
    """
    serializer_class = WalletSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        wallet, created = Wallet.objects.get_or_create(user=self.request.user)
        return wallet

    def retrieve(self, request, *args, **kwargs):
        """Enhanced wallet details with token economy status"""
        wallet = self.get_object()
        serializer = self.get_serializer(wallet)

        # Add token economy status
        data = serializer.data
        data.update({
            'can_play_games': wallet.can_play_games(),
            'can_use_token_discounts': wallet.can_use_token_discounts(),
            'is_zero_balance': wallet.balance == 0,
            'pending_refill_requests': TokenRequest.objects.filter(
                user=request.user,
                status='pending'
            ).count()
        })

        return Response(data)


class WalletTransactionListView(generics.ListAPIView):
    """
    List user's wallet transactions
    """
    serializer_class = WalletTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        wallet, created = Wallet.objects.get_or_create(user=self.request.user)
        return wallet.transactions.all()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def calculate_token_redemption(request):
    """
    Calculate how many tokens can be redeemed for a given order amount
    """
    try:
        order_amount = Decimal(str(request.data.get('order_amount', 0)))
        
        if order_amount <= 0:
            return Response(
                {'error': 'Invalid order amount'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        wallet, created = Wallet.objects.get_or_create(user=request.user)
        
        # Get configuration
        token_rate = Decimal(str(settings.WALLET_SETTINGS.get('TOKEN_TO_INR_RATE', 0.1)))
        max_percentage = settings.WALLET_SETTINGS.get('MAX_REDEMPTION_PERCENTAGE', 50)
        min_tokens = settings.WALLET_SETTINGS.get('MIN_REDEMPTION_TOKENS', 100)
        
        # Calculate maximum redeemable amount
        max_redeemable_inr = order_amount * Decimal(str(max_percentage / 100))
        max_redeemable_tokens = int(max_redeemable_inr / token_rate)
        
        # Check user's available tokens
        available_tokens = wallet.balance
        
        # Calculate actual redeemable tokens
        if available_tokens < min_tokens:
            redeemable_tokens = 0
            redeemable_inr = Decimal('0')
        else:
            redeemable_tokens = min(available_tokens, max_redeemable_tokens)
            redeemable_inr = Decimal(str(redeemable_tokens)) * token_rate
        
        return Response({
            'available_tokens': available_tokens,
            'redeemable_tokens': redeemable_tokens,
            'redeemable_inr': float(redeemable_inr),
            'remaining_amount': float(order_amount - redeemable_inr),
            'max_redemption_percentage': max_percentage,
            'min_redemption_tokens': min_tokens,
            'token_rate': float(token_rate)
        })

    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def redeem_tokens(request):
    """
    Redeem tokens for an order (called during checkout)
    """
    try:
        tokens_to_redeem = int(request.data.get('tokens_to_redeem', 0))
        order_id = request.data.get('order_id')
        
        if tokens_to_redeem <= 0:
            return Response(
                {'error': 'Invalid token amount'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        wallet, created = Wallet.objects.get_or_create(user=request.user)
        
        # Check if user has enough tokens
        if wallet.balance < tokens_to_redeem:
            return Response(
                {'error': 'Insufficient token balance'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check minimum redemption
        min_tokens = settings.WALLET_SETTINGS.get('MIN_REDEMPTION_TOKENS', 100)
        if tokens_to_redeem < min_tokens:
            return Response(
                {'error': f'Minimum redemption is {min_tokens} tokens'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculate INR value
        token_rate = Decimal(str(settings.WALLET_SETTINGS.get('TOKEN_TO_INR_RATE', 0.1)))
        inr_value = Decimal(str(tokens_to_redeem)) * token_rate
        
        # Spend tokens
        wallet.spend_tokens(
            amount=tokens_to_redeem,
            transaction_type='purchase_redemption',
            description=f'Redeemed for order {order_id}' if order_id else 'Purchase redemption'
        )
        
        return Response({
            'success': True,
            'tokens_redeemed': tokens_to_redeem,
            'inr_value': float(inr_value),
            'remaining_balance': wallet.balance
        })

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )


# Token Request Views
class TokenRequestListCreateView(generics.ListCreateAPIView):
    """
    List user's token requests and create new ones
    """
    serializer_class = TokenRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return TokenRequest.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class TokenRequestDetailView(generics.RetrieveAPIView):
    """
    Get details of a specific token request
    """
    serializer_class = TokenRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return TokenRequest.objects.filter(user=self.request.user)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def check_refill_eligibility(request):
    """
    Check if user can request token refill
    """
    try:
        wallet, created = Wallet.objects.get_or_create(user=request.user)
        temp_request = TokenRequest(user=request.user)
        can_request, message = temp_request.can_request_refill()

        return Response({
            'can_request': can_request,
            'message': message,
            'current_balance': wallet.balance,
            'pending_requests': TokenRequest.objects.filter(
                user=request.user,
                status='pending'
            ).count()
        })

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )


# Admin Views for Token Requests
class TokenRequestAdminListView(generics.ListAPIView):
    """
    Admin view to list all token requests
    """
    serializer_class = TokenRequestAdminSerializer
    permission_classes = [permissions.IsAdminUser]
    queryset = TokenRequest.objects.all()

    def get_queryset(self):
        queryset = super().get_queryset()
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        return queryset


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def approve_token_request(request, request_id):
    """
    Admin endpoint to approve token refill request
    """
    try:
        token_request = TokenRequest.objects.get(id=request_id)
        tokens_to_grant = int(request.data.get('tokens_to_grant', 50))
        admin_notes = request.data.get('admin_notes', '')

        if tokens_to_grant <= 0:
            return Response(
                {'error': 'Invalid token amount'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Approve the request
        token_request.approve(
            admin_user=request.user,
            tokens_to_grant=tokens_to_grant,
            admin_notes=admin_notes
        )

        serializer = TokenRequestAdminSerializer(token_request)
        return Response({
            'success': True,
            'message': f'Token request approved. {tokens_to_grant} tokens granted.',
            'request': serializer.data
        })

    except TokenRequest.DoesNotExist:
        return Response(
            {'error': 'Token request not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def reject_token_request(request, request_id):
    """
    Admin endpoint to reject token refill request
    """
    try:
        token_request = TokenRequest.objects.get(id=request_id)
        admin_notes = request.data.get('admin_notes', '')

        # Reject the request
        token_request.reject(
            admin_user=request.user,
            admin_notes=admin_notes
        )

        serializer = TokenRequestAdminSerializer(token_request)
        return Response({
            'success': True,
            'message': 'Token request rejected.',
            'request': serializer.data
        })

    except TokenRequest.DoesNotExist:
        return Response(
            {'error': 'Token request not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )
