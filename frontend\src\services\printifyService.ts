import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';

/**
 * Service for interacting with the Printify API
 */
const printifyService = {
  /**
   * Get a list of Printify shops
   */
  getShops: async () => {
    const response = await axios.get(`${API_URL}/api/printify/printify/shops/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      }
    });
    return response.data;
  },

  /**
   * Get a list of Printify blueprints (catalog products)
   */
  getBlueprints: async () => {
    const response = await axios.get(`${API_URL}/api/printify/printify/blueprints/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      }
    });
    return response.data;
  },

  /**
   * Get details for a specific blueprint
   */
  getBlueprintDetail: async (blueprintId: string) => {
    const response = await axios.get(`${API_URL}/api/printify/printify/${blueprintId}/blueprint_detail/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      }
    });
    return response.data;
  },

  /**
   * Get print providers for a specific blueprint
   */
  getBlueprintProviders: async (blueprintId: string) => {
    const response = await axios.get(`${API_URL}/api/printify/printify/${blueprintId}/blueprint_providers/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      }
    });
    return response.data;
  },

  /**
   * Get variants for a specific blueprint and print provider
   */
  getBlueprintVariants: async (blueprintId: string, providerId: string) => {
    const response = await axios.get(`${API_URL}/api/printify/printify/blueprint_variants/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      },
      params: {
        blueprint_id: blueprintId,
        provider_id: providerId
      }
    });
    return response.data;
  },

  /**
   * Get shipping information for a specific blueprint and print provider
   */
  getBlueprintShipping: async (blueprintId: string, providerId: string) => {
    const response = await axios.get(`${API_URL}/api/printify/printify/blueprint_shipping/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      },
      params: {
        blueprint_id: blueprintId,
        provider_id: providerId
      }
    });
    return response.data;
  },

  /**
   * Get products for a specific shop
   */
  getProducts: async (shopId: string) => {
    const response = await axios.get(`${API_URL}/api/printify/printify/products/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      },
      params: {
        shop_id: shopId
      }
    });
    return response.data;
  },

  /**
   * Create a new product
   */
  createProduct: async (shopId: string, productData: any) => {
    const response = await axios.post(`${API_URL}/api/printify/printify/create_product/`, {
      shop_id: shopId,
      product_data: productData
    }, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  },

  /**
   * Publish a product
   */
  publishProduct: async (shopId: string, productId: string, publishData: any) => {
    const response = await axios.post(`${API_URL}/api/printify/printify/publish_product/`, {
      shop_id: shopId,
      product_id: productId,
      publish_data: publishData
    }, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  },

  /**
   * Get orders for a specific shop
   */
  getOrders: async (shopId: string, params: any = {}) => {
    const response = await axios.get(`${API_URL}/api/printify/printify/orders/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      },
      params: {
        shop_id: shopId,
        ...params
      }
    });
    return response.data;
  },

  /**
   * Create a new order
   */
  createOrder: async (shopId: string, orderData: any) => {
    const response = await axios.post(`${API_URL}/api/printify/printify/create_order/`, {
      shop_id: shopId,
      order_data: orderData
    }, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  },

  /**
   * Calculate shipping costs
   */
  calculateShipping: async (shopId: string, shippingData: any) => {
    const response = await axios.post(`${API_URL}/api/printify/printify/calculate_shipping/`, {
      shop_id: shopId,
      shipping_data: shippingData
    }, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  },

  /**
   * Send an order to production
   */
  sendToProduction: async (shopId: string, orderId: string) => {
    const response = await axios.post(`${API_URL}/api/printify/printify/send_to_production/`, {
      shop_id: shopId,
      order_id: orderId
    }, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  },

  /**
   * Get a list of uploaded images
   */
  getUploads: async () => {
    const response = await axios.get(`${API_URL}/api/printify/printify/uploads/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      }
    });
    return response.data;
  },

  /**
   * Upload an image from a URL
   */
  uploadImageUrl: async (fileName: string, url: string) => {
    const response = await axios.post(`${API_URL}/api/printify/printify/upload_image/`, {
      file_name: fileName,
      url: url
    }, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  },

  /**
   * Upload an image using base64 encoding
   */
  uploadImageBase64: async (fileName: string, contents: string) => {
    const response = await axios.post(`${API_URL}/api/printify/printify/upload_image/`, {
      file_name: fileName,
      contents: contents
    }, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }
};

export default printifyService;
