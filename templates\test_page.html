{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-4">Site Settings Test</h1>
        
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-2">Logo</h2>
            {% if site_settings and site_settings.site_logo %}
                <div class="p-4 bg-gray-100 rounded">
                    <img src="{{ site_settings.site_logo.url }}" alt="Site Logo" class="max-h-24">
                </div>
                <p class="mt-2 text-sm text-gray-600">Logo URL: {{ site_settings.site_logo.url }}</p>
            {% else %}
                <p class="text-red-500">No logo has been uploaded yet.</p>
            {% endif %}
        </div>
        
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-2">Favicon</h2>
            {% if site_settings and site_settings.favicon %}
                <div class="p-4 bg-gray-100 rounded">
                    <img src="{{ site_settings.favicon.url }}" alt="Favicon" class="max-h-16">
                </div>
                <p class="mt-2 text-sm text-gray-600">Favicon URL: {{ site_settings.favicon.url }}</p>
            {% else %}
                <p class="text-red-500">No favicon has been uploaded yet.</p>
            {% endif %}
        </div>
        
        <div class="mt-8">
            <h2 class="text-xl font-semibold mb-2">Instructions</h2>
            <ol class="list-decimal pl-5 space-y-2">
                <li>Go to the <a href="/admin/site_settings/sitesettings/" class="text-blue-600 hover:underline">admin panel</a> to upload a logo and favicon.</li>
                <li>After uploading, refresh this page to see the changes.</li>
                <li>Check that the logo appears in the header and the favicon appears in the browser tab.</li>
            </ol>
        </div>
    </div>
</div>
{% endblock %}
