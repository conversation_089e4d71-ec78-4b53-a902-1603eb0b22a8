from rest_framework import serializers
from .models import OrderTracking
from django.utils import timezone

class OrderTrackingSerializer(serializers.ModelSerializer):
    """
    Serializer for OrderTracking model.
    
    Provides serialization for order tracking data with additional computed fields
    for better user experience.
    """
    status_display = serializers.SerializerMethodField()
    days_since_shipped = serializers.SerializerMethodField()
    days_until_delivery = serializers.SerializerMethodField()
    is_delivered = serializers.SerializerMethodField()
    is_shipped = serializers.SerializerMethodField()
    
    class Meta:
        model = OrderTracking
        fields = [
            'id', 'order_id', 'printify_order_id', 'status', 'status_display',
            'tracking_number', 'carrier', 'carrier_link', 'estimated_delivery',
            'days_since_shipped', 'days_until_delivery', 'is_delivered', 'is_shipped',
            'last_update', 'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'last_update']
    
    def get_status_display(self, obj):
        """Get the display name for the status."""
        return obj.get_status_display()
    
    def get_days_since_shipped(self, obj):
        """Get the number of days since the order was shipped."""
        return obj.days_since_shipped()
    
    def get_days_until_delivery(self, obj):
        """Get the number of days until the estimated delivery date."""
        return obj.days_until_delivery()
    
    def get_is_delivered(self, obj):
        """Check if the order has been delivered."""
        return obj.is_delivered()
    
    def get_is_shipped(self, obj):
        """Check if the order has been shipped."""
        return obj.is_shipped()
