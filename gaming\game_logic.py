"""
Game logic for different game types
"""
import random
from typing import Dict, <PERSON>, Tu<PERSON>, Optional


class RockPaperScissorsGame:
    """
    Rock Paper Scissors game logic
    """
    CHOICES = ['rock', 'paper', 'scissors']
    
    @staticmethod
    def is_valid_move(move: str) -> bool:
        return move.lower() in RockPaperScissorsGame.CHOICES
    
    @staticmethod
    def determine_winner(move1: str, move2: str) -> str:
        """
        Determine winner between two moves
        Returns: 'player1', 'player2', or 'draw'
        """
        move1, move2 = move1.lower(), move2.lower()
        
        if move1 == move2:
            return 'draw'
        
        winning_combinations = {
            'rock': 'scissors',
            'paper': 'rock',
            'scissors': 'paper'
        }
        
        if winning_combinations[move1] == move2:
            return 'player1'
        else:
            return 'player2'
    
    @staticmethod
    def get_initial_state() -> Dict[str, Any]:
        return {
            'round': 1,
            'max_rounds': 3,
            'player1_score': 0,
            'player2_score': 0,
            'player1_move': None,
            'player2_move': None,
            'round_results': []
        }
    
    @staticmethod
    def process_move(game_state: Dict[str, Any], player: str, move: str) -> Dict[str, Any]:
        """
        Process a player's move
        """
        if not RockPaperScissorsGame.is_valid_move(move):
            raise ValueError(f"Invalid move: {move}")
        
        game_state = game_state.copy()
        
        if player == 'player1':
            game_state['player1_move'] = move.lower()
        elif player == 'player2':
            game_state['player2_move'] = move.lower()
        
        # Check if both players have moved
        if game_state['player1_move'] and game_state['player2_move']:
            # Determine round winner
            winner = RockPaperScissorsGame.determine_winner(
                game_state['player1_move'], 
                game_state['player2_move']
            )
            
            # Update scores
            if winner == 'player1':
                game_state['player1_score'] += 1
            elif winner == 'player2':
                game_state['player2_score'] += 1
            
            # Record round result
            game_state['round_results'].append({
                'round': game_state['round'],
                'player1_move': game_state['player1_move'],
                'player2_move': game_state['player2_move'],
                'winner': winner
            })
            
            # Reset moves for next round
            game_state['player1_move'] = None
            game_state['player2_move'] = None
            game_state['round'] += 1
        
        return game_state
    
    @staticmethod
    def is_game_over(game_state: Dict[str, Any]) -> bool:
        """
        Check if game is over
        """
        return (game_state['round'] > game_state['max_rounds'] or 
                game_state['player1_score'] > game_state['max_rounds'] // 2 or
                game_state['player2_score'] > game_state['max_rounds'] // 2)
    
    @staticmethod
    def get_game_result(game_state: Dict[str, Any]) -> str:
        """
        Get final game result
        Returns: 'player1_win', 'player2_win', or 'draw'
        """
        if game_state['player1_score'] > game_state['player2_score']:
            return 'player1_win'
        elif game_state['player2_score'] > game_state['player1_score']:
            return 'player2_win'
        else:
            return 'draw'


class NumberGuessingGame:
    """
    Number guessing battle game
    """
    @staticmethod
    def get_initial_state() -> Dict[str, Any]:
        return {
            'round': 1,
            'max_rounds': 5,
            'target_number': random.randint(1, 100),
            'player1_score': 0,
            'player2_score': 0,
            'player1_guess': None,
            'player2_guess': None,
            'round_results': []
        }
    
    @staticmethod
    def is_valid_move(move: int) -> bool:
        return isinstance(move, int) and 1 <= move <= 100
    
    @staticmethod
    def process_move(game_state: Dict[str, Any], player: str, guess: int) -> Dict[str, Any]:
        """
        Process a player's guess
        """
        if not NumberGuessingGame.is_valid_move(guess):
            raise ValueError(f"Invalid guess: {guess}")
        
        game_state = game_state.copy()
        
        if player == 'player1':
            game_state['player1_guess'] = guess
        elif player == 'player2':
            game_state['player2_guess'] = guess
        
        # Check if both players have guessed
        if game_state['player1_guess'] is not None and game_state['player2_guess'] is not None:
            target = game_state['target_number']
            
            # Calculate distances
            dist1 = abs(target - game_state['player1_guess'])
            dist2 = abs(target - game_state['player2_guess'])
            
            # Determine winner
            if dist1 < dist2:
                winner = 'player1'
                game_state['player1_score'] += 1
            elif dist2 < dist1:
                winner = 'player2'
                game_state['player2_score'] += 1
            else:
                winner = 'draw'
            
            # Record round result
            game_state['round_results'].append({
                'round': game_state['round'],
                'target_number': target,
                'player1_guess': game_state['player1_guess'],
                'player2_guess': game_state['player2_guess'],
                'player1_distance': dist1,
                'player2_distance': dist2,
                'winner': winner
            })
            
            # Reset for next round
            game_state['player1_guess'] = None
            game_state['player2_guess'] = None
            game_state['target_number'] = random.randint(1, 100)
            game_state['round'] += 1
        
        return game_state
    
    @staticmethod
    def is_game_over(game_state: Dict[str, Any]) -> bool:
        return game_state['round'] > game_state['max_rounds']
    
    @staticmethod
    def get_game_result(game_state: Dict[str, Any]) -> str:
        if game_state['player1_score'] > game_state['player2_score']:
            return 'player1_win'
        elif game_state['player2_score'] > game_state['player1_score']:
            return 'player2_win'
        else:
            return 'draw'


class ColorMatchGame:
    """
    Color Match Game - Players must repeat color sequences
    """

    @staticmethod
    def get_initial_state() -> Dict[str, Any]:
        """Initialize a new color match game"""
        return {
            'round': 1,
            'max_rounds': 5,
            'sequence': [],
            'player1_sequence': [],
            'player2_sequence': [],
            'current_color': None,
            'player1_score': 0,
            'player2_score': 0,
            'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
            'status': 'waiting_for_sequence'
        }

    @staticmethod
    def is_valid_move(move: str) -> bool:
        """Check if the move is valid"""
        valid_colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange']
        return isinstance(move, str) and move.lower() in valid_colors

    @staticmethod
    def process_move(game_state: Dict[str, Any], player: str, move: str) -> Dict[str, Any]:
        """Process a player's color sequence input"""
        if not ColorMatchGame.is_valid_move(move):
            raise ValueError(f"Invalid color: {move}")

        game_state = game_state.copy()
        move = move.lower()

        # If we're waiting for sequence, generate it
        if game_state['status'] == 'waiting_for_sequence':
            import random
            # Generate sequence for current round (length = round number)
            sequence_length = game_state['round']
            game_state['sequence'] = [random.choice(game_state['colors']) for _ in range(sequence_length)]
            game_state['status'] = 'showing_sequence'
            game_state['player1_sequence'] = []
            game_state['player2_sequence'] = []
            return game_state

        # Record player's sequence input
        if player == 'player1':
            game_state['player1_sequence'].append(move)
        elif player == 'player2':
            game_state['player2_sequence'].append(move)

        # Check if both players have completed their sequences
        expected_length = len(game_state['sequence'])
        p1_complete = len(game_state['player1_sequence']) >= expected_length
        p2_complete = len(game_state['player2_sequence']) >= expected_length

        if p1_complete and p2_complete:
            # Score the round
            p1_correct = game_state['player1_sequence'] == game_state['sequence']
            p2_correct = game_state['player2_sequence'] == game_state['sequence']

            if p1_correct:
                game_state['player1_score'] += 1
            if p2_correct:
                game_state['player2_score'] += 1

            # Move to next round or end game
            if game_state['round'] >= game_state['max_rounds']:
                game_state['status'] = 'completed'
            else:
                game_state['round'] += 1
                game_state['status'] = 'waiting_for_sequence'

        return game_state

    @staticmethod
    def is_game_over(game_state: Dict[str, Any]) -> bool:
        """Check if the game is over"""
        return game_state.get('status') == 'completed'

    @staticmethod
    def get_game_result(game_state: Dict[str, Any]) -> str:
        """Determine the winner"""
        if not ColorMatchGame.is_game_over(game_state):
            return 'ongoing'

        p1_score = game_state.get('player1_score', 0)
        p2_score = game_state.get('player2_score', 0)

        if p1_score > p2_score:
            return 'player1_win'
        elif p2_score > p1_score:
            return 'player2_win'
        else:
            return 'draw'


class TicTacToeGame:
    """
    Tic Tac Toe Game - Classic 3x3 grid strategy game
    """

    @staticmethod
    def get_initial_state() -> Dict[str, Any]:
        """Initialize a new tic tac toe game"""
        return {
            'board': [['', '', ''], ['', '', ''], ['', '', '']],
            'current_player': 'player1',
            'player1_symbol': 'X',
            'player2_symbol': 'O',
            'status': 'in_progress',
            'winner': None,
            'winning_line': None
        }

    @staticmethod
    def is_valid_move(move: tuple, game_state: Dict[str, Any]) -> bool:
        """Check if the move is valid (row, col)"""
        if not isinstance(move, (tuple, list)) or len(move) != 2:
            return False

        row, col = move
        if not (0 <= row <= 2 and 0 <= col <= 2):
            return False

        return game_state['board'][row][col] == ''

    @staticmethod
    def process_move(game_state: Dict[str, Any], player: str, move: tuple) -> Dict[str, Any]:
        """Process a player's move"""
        if not TicTacToeGame.is_valid_move(move, game_state):
            raise ValueError(f"Invalid move: {move}")

        game_state = game_state.copy()
        game_state['board'] = [row[:] for row in game_state['board']]  # Deep copy board

        row, col = move
        symbol = game_state['player1_symbol'] if player == 'player1' else game_state['player2_symbol']

        # Place the symbol
        game_state['board'][row][col] = symbol

        # Check for winner
        winner, winning_line = TicTacToeGame._check_winner(game_state['board'])

        if winner:
            game_state['status'] = 'completed'
            game_state['winner'] = 'player1' if winner == game_state['player1_symbol'] else 'player2'
            game_state['winning_line'] = winning_line
        elif TicTacToeGame._is_board_full(game_state['board']):
            game_state['status'] = 'completed'
            game_state['winner'] = 'draw'
        else:
            # Switch players
            game_state['current_player'] = 'player2' if player == 'player1' else 'player1'

        return game_state

    @staticmethod
    def _check_winner(board) -> tuple:
        """Check if there's a winner and return (winner_symbol, winning_line)"""
        # Check rows
        for i in range(3):
            if board[i][0] == board[i][1] == board[i][2] != '':
                return board[i][0], f'row_{i}'

        # Check columns
        for j in range(3):
            if board[0][j] == board[1][j] == board[2][j] != '':
                return board[0][j], f'col_{j}'

        # Check diagonals
        if board[0][0] == board[1][1] == board[2][2] != '':
            return board[0][0], 'diag_main'

        if board[0][2] == board[1][1] == board[2][0] != '':
            return board[0][2], 'diag_anti'

        return None, None

    @staticmethod
    def _is_board_full(board) -> bool:
        """Check if the board is full"""
        for row in board:
            for cell in row:
                if cell == '':
                    return False
        return True

    @staticmethod
    def is_game_over(game_state: Dict[str, Any]) -> bool:
        """Check if the game is over"""
        return game_state.get('status') == 'completed'

    @staticmethod
    def get_game_result(game_state: Dict[str, Any]) -> str:
        """Determine the winner"""
        if not TicTacToeGame.is_game_over(game_state):
            return 'ongoing'

        winner = game_state.get('winner')
        if winner == 'player1':
            return 'player1_win'
        elif winner == 'player2':
            return 'player2_win'
        else:
            return 'draw'


class MemoryCardGame:
    """
    Memory Card Match Game - Players find matching pairs
    """

    @staticmethod
    def get_initial_state() -> Dict[str, Any]:
        """Initialize a new memory card game"""
        import random

        # Create pairs of cards (8 pairs = 16 cards)
        card_types = ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼']
        cards = card_types + card_types  # Create pairs
        random.shuffle(cards)

        return {
            'cards': cards,
            'revealed': [False] * len(cards),
            'matched': [False] * len(cards),
            'player1_score': 0,
            'player2_score': 0,
            'current_player': 'player1',
            'selected_cards': [],
            'round': 1,
            'max_rounds': 8,  # 8 pairs to find
            'status': 'in_progress'
        }

    @staticmethod
    def is_valid_move(move: int) -> bool:
        """Check if the move is valid (card index)"""
        return isinstance(move, int) and 0 <= move <= 15

    @staticmethod
    def process_move(game_state: Dict[str, Any], player: str, move: int) -> Dict[str, Any]:
        """Process a player's card selection"""
        if not MemoryCardGame.is_valid_move(move):
            raise ValueError(f"Invalid card index: {move}")

        game_state = game_state.copy()

        # Check if card is already revealed or matched
        if game_state['revealed'][move] or game_state['matched'][move]:
            return game_state

        # Reveal the card
        game_state['revealed'][move] = True
        game_state['selected_cards'].append(move)

        # If two cards are selected, check for match
        if len(game_state['selected_cards']) == 2:
            card1_idx, card2_idx = game_state['selected_cards']
            card1 = game_state['cards'][card1_idx]
            card2 = game_state['cards'][card2_idx]

            if card1 == card2:
                # Match found!
                game_state['matched'][card1_idx] = True
                game_state['matched'][card2_idx] = True

                # Award point to current player
                if player == 'player1':
                    game_state['player1_score'] += 1
                else:
                    game_state['player2_score'] += 1

                # Check if game is complete
                if all(game_state['matched']):
                    game_state['status'] = 'completed'
            else:
                # No match, hide cards after a delay (handled by frontend)
                game_state['revealed'][card1_idx] = False
                game_state['revealed'][card2_idx] = False

                # Switch players
                game_state['current_player'] = 'player2' if player == 'player1' else 'player1'

            # Reset selected cards
            game_state['selected_cards'] = []

        return game_state

    @staticmethod
    def is_game_over(game_state: Dict[str, Any]) -> bool:
        """Check if the game is over"""
        return game_state.get('status') == 'completed'

    @staticmethod
    def get_game_result(game_state: Dict[str, Any]) -> str:
        """Determine the winner"""
        if not MemoryCardGame.is_game_over(game_state):
            return 'ongoing'

        p1_score = game_state.get('player1_score', 0)
        p2_score = game_state.get('player2_score', 0)

        if p1_score > p2_score:
            return 'player1_win'
        elif p2_score > p1_score:
            return 'player2_win'
        else:
            return 'draw'


class GameEngine:
    """
    Main game engine that handles different game types
    """
    GAME_CLASSES = {
        'rock_paper_scissors': RockPaperScissorsGame,
        'number_guessing': NumberGuessingGame,
        'tic_tac_toe': TicTacToeGame,
        'color_match': ColorMatchGame,
        'memory_card': MemoryCardGame,
    }
    
    @staticmethod
    def get_game_class(game_type: str):
        # Normalize game type name
        normalized_type = game_type.lower().replace(' ', '_')
        return GameEngine.GAME_CLASSES.get(normalized_type)

    @staticmethod
    def create_initial_state(game_type: str) -> Dict[str, Any]:
        print(f"Creating initial state for game type: {game_type}")
        game_class = GameEngine.get_game_class(game_type)
        if not game_class:
            print(f"Unknown game type: {game_type}, available types: {list(GameEngine.GAME_CLASSES.keys())}")
            # Default to rock_paper_scissors if unknown
            game_class = GameEngine.GAME_CLASSES['rock_paper_scissors']

        initial_state = game_class.get_initial_state()
        initial_state['game_type'] = game_type
        return initial_state
    
    @staticmethod
    def process_move(game_type: str, game_state: Dict[str, Any], player: str, move: Any) -> Dict[str, Any]:
        game_class = GameEngine.get_game_class(game_type)
        if not game_class:
            print(f"Unknown game type in process_move: {game_type}")
            game_class = GameEngine.GAME_CLASSES['rock_paper_scissors']
        return game_class.process_move(game_state, player, move)

    @staticmethod
    def is_game_over(game_type: str, game_state: Dict[str, Any]) -> bool:
        game_class = GameEngine.get_game_class(game_type)
        if not game_class:
            print(f"Unknown game type in is_game_over: {game_type}")
            game_class = GameEngine.GAME_CLASSES['rock_paper_scissors']
        return game_class.is_game_over(game_state)

    @staticmethod
    def get_game_result(game_type: str, game_state: Dict[str, Any]) -> str:
        game_class = GameEngine.get_game_class(game_type)
        if not game_class:
            print(f"Unknown game type in get_game_result: {game_type}")
            game_class = GameEngine.GAME_CLASSES['rock_paper_scissors']
        return game_class.get_game_result(game_state)
