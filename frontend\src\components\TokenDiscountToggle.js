import React, { useState, useEffect } from 'react';
import './TokenDiscountToggle.css';

const TokenDiscountToggle = ({ 
    cartId, 
    onTokenDiscountChange, 
    disabled = false 
}) => {
    const [isEnabled, setIsEnabled] = useState(false);
    const [discountInfo, setDiscountInfo] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (cartId) {
            fetchTokenDiscountInfo();
        }
    }, [cartId]);

    const fetchTokenDiscountInfo = async () => {
        try {
            setLoading(true);
            setError(null);

            const token = localStorage.getItem('access_token');
            const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
            const response = await fetch(`${baseUrl}/api/orders/cart/${cartId}/token-discount-info/`, {
                headers: {
                    'Authorization': `JWT ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch token discount info');
            }

            const data = await response.json();
            setDiscountInfo(data);

            // Auto-enable if tokens are available and cart is eligible
            if (data.eligible && data.user_wallet_balance > 0) {
                setIsEnabled(true);
                handleTokenDiscountChange(true, data);
            }
        } catch (err) {
            console.error('Error fetching token discount info:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const handleToggle = () => {
        if (disabled) return;
        
        const newState = !isEnabled;
        setIsEnabled(newState);
        handleTokenDiscountChange(newState, discountInfo);
    };

    const handleTokenDiscountChange = (enabled, info) => {
        if (onTokenDiscountChange) {
            const discountData = enabled && info?.eligible ? {
                enabled: true,
                tokensToUse: info.max_tokens_usable,
                discountAmount: info.max_inr_discount,
                finalAmount: info.final_amount
            } : {
                enabled: false,
                tokensToUse: 0,
                discountAmount: 0,
                finalAmount: info?.cart_total || 0
            };

            onTokenDiscountChange(discountData);
        }
    };

    if (loading) {
        return (
            <div className="token-discount-toggle token-discount-toggle--loading">
                <div className="token-discount-toggle__spinner"></div>
                <span>Checking token eligibility...</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="token-discount-toggle token-discount-toggle--error">
                <span className="token-discount-toggle__error-icon">⚠️</span>
                <span>Unable to load token discount info</span>
            </div>
        );
    }

    if (!discountInfo?.eligible) {
        return (
            <div className="token-discount-toggle token-discount-toggle--unavailable">
                <span className="token-discount-toggle__icon">🪙</span>
                <span>No token-eligible items in cart</span>
            </div>
        );
    }

    if (discountInfo.user_wallet_balance === 0) {
        return (
            <div className="token-discount-toggle token-discount-toggle--no-tokens">
                <span className="token-discount-toggle__icon">🪙</span>
                <span>No tokens available</span>
                <a href="/games" className="token-discount-toggle__earn-link">
                    Earn tokens by playing games
                </a>
            </div>
        );
    }

    const wallet_balance = discountInfo.user_wallet_balance;

    return (
        <div className={`token-discount-toggle ${disabled ? 'token-discount-toggle--disabled' : ''}`}>
            <div className="token-discount-toggle__header">
                <label className="token-discount-toggle__label">
                    <input
                        type="checkbox"
                        checked={isEnabled}
                        onChange={handleToggle}
                        disabled={disabled}
                        className="token-discount-toggle__checkbox"
                    />
                    <span className="token-discount-toggle__checkmark"></span>
                    <span className="token-discount-toggle__text">
                        Use Wallet Tokens
                    </span>
                </label>
                
                <div className="token-discount-toggle__balance">
                    <span className="token-discount-toggle__icon">🪙</span>
                    <span>{wallet_balance} tokens available</span>
                </div>
            </div>

            {isEnabled && (
                <div className="token-discount-toggle__details">
                    <div className="token-discount-toggle__breakdown">
                        <div className="token-discount-toggle__row">
                            <span>Tokens to use:</span>
                            <span className="token-discount-toggle__value">
                                {discountInfo.max_tokens_usable} tokens
                            </span>
                        </div>
                        <div className="token-discount-toggle__row">
                            <span>Discount amount:</span>
                            <span className="token-discount-toggle__value token-discount-toggle__value--discount">
                                -₹{discountInfo.max_inr_discount.toFixed(2)}
                            </span>
                        </div>
                        <div className="token-discount-toggle__row token-discount-toggle__row--total">
                            <span>Final amount:</span>
                            <span className="token-discount-toggle__value token-discount-toggle__value--final">
                                ₹{discountInfo.final_amount.toFixed(2)}
                            </span>
                        </div>
                    </div>

                    <div className="token-discount-toggle__eligible-items">
                        <h4>Token-eligible items:</h4>
                        <ul>
                            {discountInfo.eligible_items.map((item, index) => (
                                <li key={index}>
                                    {item.product_name} (×{item.quantity}) -
                                    Max ₹{item.max_inr_discount.toFixed(2)} discount
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
            )}
        </div>
    );
};

export default TokenDiscountToggle;
