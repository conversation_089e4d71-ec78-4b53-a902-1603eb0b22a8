#!/usr/bin/env python
"""
Standalone script to fix Char<PERSON>ield length issues in the database.
This script can be run independently of Django management commands.

Usage:
    python fix_database_field_lengths.py [--dry-run]
"""

import os
import sys
import argparse
import psycopg2
from urllib.parse import urlparse


def get_database_config():
    """Get database configuration from environment variables or defaults"""
    # Try to get DATABASE_URL first (for production environments like Render, Railway)
    database_url = os.environ.get('DATABASE_URL')
    
    if database_url:
        # Parse DATABASE_URL
        parsed = urlparse(database_url)
        return {
            'host': parsed.hostname,
            'port': parsed.port or 5432,
            'database': parsed.path[1:],  # Remove leading slash
            'user': parsed.username,
            'password': parsed.password,
        }
    else:
        # Use individual environment variables or defaults
        return {
            'host': os.environ.get('DB_HOST', 'localhost'),
            'port': int(os.environ.get('DB_PORT', '5432')),
            'database': os.environ.get('DB_NAME', 'pickmetrend'),
            'user': os.environ.get('DB_USER', 'postgres'),
            'password': os.environ.get('DB_PASSWORD', ''),
        }


def main():
    parser = argparse.ArgumentParser(description='Fix CharField length issues in the database')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show SQL commands without executing them')
    args = parser.parse_args()
    
    # SQL commands to increase field lengths
    sql_commands = [
        # Products app - Product model
        "ALTER TABLE products_product ALTER COLUMN printify_id TYPE VARCHAR(255);",
        
        # Products app - ProductImage model  
        "ALTER TABLE products_productimage ALTER COLUMN alt_text TYPE VARCHAR(255);",
        
        # Products app - ProductVariant model
        "ALTER TABLE products_productvariant ALTER COLUMN variant_id TYPE VARCHAR(255);",
        "ALTER TABLE products_productvariant ALTER COLUMN color TYPE VARCHAR(255);",
        "ALTER TABLE products_productvariant ALTER COLUMN size TYPE VARCHAR(255);",
        
        # Orders app - CartItem model
        "ALTER TABLE orders_cartitem ALTER COLUMN variant_id TYPE VARCHAR(255);",
        
        # Orders app - OrderItem model
        "ALTER TABLE orders_orderitem ALTER COLUMN variant_id TYPE VARCHAR(255);",
        "ALTER TABLE orders_orderitem ALTER COLUMN printify_order_id TYPE VARCHAR(255);",
    ]
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - SQL commands that would be executed:")
        for sql in sql_commands:
            print(f"  {sql}")
        return
    
    # Get database configuration
    try:
        db_config = get_database_config()
        print(f"🔗 Connecting to database: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # Connect to database
        conn = psycopg2.connect(**db_config)
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("✅ Database connection successful!")
        
        # Execute SQL commands
        for sql in sql_commands:
            try:
                print(f"⚡ Executing: {sql}")
                cursor.execute(sql)
                print(f"✅ Successfully executed: {sql}")
            except Exception as e:
                error_msg = str(e).lower()
                if 'does not exist' in error_msg:
                    print(f"⚠️  Column doesn't exist (might be new): {sql}")
                elif 'already exists' in error_msg or 'no change' in error_msg:
                    print(f"⚠️  Column already correct type: {sql}")
                else:
                    print(f"❌ Error executing {sql}: {e}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Database schema updates completed!")
        print("✅ The CharField length issues should now be resolved.")
        print("🔄 You can now try using the Django admin interface again.")
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure PostgreSQL is running")
        print("2. Check your database credentials in environment variables:")
        print("   - DATABASE_URL (for production)")
        print("   - Or: DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD")
        print("3. Ensure the database exists and you have permission to modify it")
        print("4. For production environments, you may need to run this through your deployment platform")
        sys.exit(1)


if __name__ == '__main__':
    main()
