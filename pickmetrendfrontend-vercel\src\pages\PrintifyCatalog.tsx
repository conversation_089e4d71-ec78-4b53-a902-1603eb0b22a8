import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import printifyService from '../services/printifyService';
import { useAuth } from '../contexts/AuthContext';

interface Shop {
  id: number;
  title: string;
  sales_channel: string;
}

interface Blueprint {
  id: number;
  title: string;
  description: string;
  brand: string;
  model: string;
  images: string[];
}

interface PrintProvider {
  id: number;
  title: string;
}

interface Variant {
  id: number;
  title: string;
  options: {
    [key: string]: string;
  };
}

const PrintifyCatalog: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [shops, setShops] = useState<Shop[]>([]);
  const [selectedShop, setSelectedShop] = useState<Shop | null>(null);
  const [blueprints, setBlueprints] = useState<Blueprint[]>([]);
  const [selectedBlueprint, setSelectedBlueprint] = useState<Blueprint | null>(null);
  const [providers, setProviders] = useState<PrintProvider[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<PrintProvider | null>(null);
  const [variants, setVariants] = useState<Variant[]>([]);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchShops = async () => {
      try {
        setLoading(true);
        const shopsData = await printifyService.getShops();
        setShops(shopsData);

        // If shops are available, select the first one and fetch its blueprints
        if (shopsData && shopsData.length > 0) {
          setSelectedShop(shopsData[0]);

          // Fetch blueprints
          const blueprintsData = await printifyService.getBlueprints();
          setBlueprints(blueprintsData);
        }

        setLoading(false);
      } catch (err: any) {
        setError(err.response?.data?.detail || 'Failed to fetch Printify data');
        setLoading(false);
      }
    };

    fetchShops();
  }, [isAuthenticated, navigate]);

  const handleBlueprintSelect = async (blueprint: Blueprint) => {
    setSelectedBlueprint(blueprint);
    setSelectedProvider(null);
    setVariants([]);

    try {
      setLoading(true);
      const data = await printifyService.getBlueprintProviders(blueprint.id.toString());
      setProviders(data);
      setLoading(false);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch print providers');
      setLoading(false);
    }
  };

  const handleProviderSelect = async (provider: PrintProvider) => {
    if (!selectedBlueprint) return;

    setSelectedProvider(provider);

    try {
      setLoading(true);
      const data = await printifyService.getBlueprintVariants(
        selectedBlueprint.id.toString(),
        provider.id.toString()
      );

      // Check if data is an array
      if (Array.isArray(data)) {
        setVariants(data);
      } else if (data && typeof data === 'object') {
        // If data is an object, try to extract variants array
        if (Array.isArray(data.variants)) {
          setVariants(data.variants);
        } else if (Array.isArray(data.data)) {
          setVariants(data.data);
        } else {
          // If no array found, create an empty array
          console.error('Variants data is not in expected format:', data);
          setVariants([]);
        }
      } else {
        // If data is neither an array nor an object, create an empty array
        console.error('Unexpected variants data format:', data);
        setVariants([]);
      }

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching variants:', err);
      setError(err.response?.data?.detail || 'Failed to fetch variants');
      setVariants([]);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Printify Catalog</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {/* Shops Section */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <h2 className="text-xl font-semibold mb-4">Your Printify Shops</h2>
        <div className="flex flex-wrap gap-4">
          {shops.map((shop) => (
            <div
              key={shop.id}
              className={`p-4 rounded-lg border cursor-pointer ${
                selectedShop?.id === shop.id
                  ? 'bg-blue-100 border-blue-300'
                  : 'hover:bg-gray-100 border-gray-200'
              }`}
              onClick={() => setSelectedShop(shop)}
            >
              <h3 className="font-medium text-lg">{shop.title}</h3>
              <p className="text-sm text-gray-600">Shop ID: {shop.id}</p>
              <p className="text-sm text-gray-600">Channel: {shop.sales_channel}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Blueprints List */}
        <div className="bg-white rounded-lg shadow-md p-4">
          <h2 className="text-xl font-semibold mb-4">Products</h2>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {blueprints.map((blueprint) => (
              <div
                key={blueprint.id}
                className={`p-3 rounded cursor-pointer ${
                  selectedBlueprint?.id === blueprint.id
                    ? 'bg-blue-100 border border-blue-300'
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => handleBlueprintSelect(blueprint)}
              >
                <div className="flex items-center">
                  {blueprint.images && blueprint.images.length > 0 && (
                    <img
                      src={blueprint.images[0]}
                      alt={blueprint.title}
                      className="w-16 h-16 object-cover mr-3"
                    />
                  )}
                  <div>
                    <h3 className="font-medium">{blueprint.title}</h3>
                    <p className="text-sm text-gray-600">{blueprint.brand} - {blueprint.model}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Print Providers */}
        <div className="bg-white rounded-lg shadow-md p-4">
          <h2 className="text-xl font-semibold mb-4">Print Providers</h2>
          {selectedBlueprint ? (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {providers.map((provider) => (
                <div
                  key={provider.id}
                  className={`p-3 rounded cursor-pointer ${
                    selectedProvider?.id === provider.id
                      ? 'bg-blue-100 border border-blue-300'
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => handleProviderSelect(provider)}
                >
                  <h3 className="font-medium">{provider.title}</h3>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">Select a product to see available print providers</p>
          )}
        </div>

        {/* Variants */}
        <div className="bg-white rounded-lg shadow-md p-4">
          <h2 className="text-xl font-semibold mb-4">Variants</h2>
          {selectedProvider ? (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {Array.isArray(variants) && variants.length > 0 ? (
                variants.map((variant) => (
                  <div
                    key={variant.id}
                    className="p-3 rounded hover:bg-gray-100"
                  >
                    <h3 className="font-medium">{variant.title}</h3>
                    <div className="mt-2">
                      {variant.options && Object.entries(variant.options).map(([key, value]) => (
                        <span
                          key={key}
                          className="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2"
                        >
                          {key}: {value}
                        </span>
                      ))}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500">No variants available for this product and print provider</p>
              )}
            </div>
          ) : (
            <p className="text-gray-500">Select a print provider to see available variants</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PrintifyCatalog;
