import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { api, returnRequestAPI, ReturnRequest } from '../services/api';
import { formatINR } from '../utils/currencyFormatter';
import ReturnRequestForm from '../components/returns/ReturnRequestForm';
import ReturnStatus from '../components/returns/ReturnStatus';
import DebugOrders from '../components/DebugOrders';

interface Order {
  id: string;
  order_number?: string;
  status: string;
  total: number;
  created_at: string;
  items_count?: number;
  can_return?: boolean;
  has_return_request?: boolean;
  return_request_status?: string;
}

const Orders = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showReturnForm, setShowReturnForm] = useState<string | null>(null);
  const [returnRequests, setReturnRequests] = useState<ReturnRequest[]>([]);
  const [activeTab, setActiveTab] = useState<'orders' | 'returns'>('orders');

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch orders using the configured API service
        const ordersResponse = await api.get(`/api/orders/?page=${currentPage}`);
        setOrders(ordersResponse.data.results || []);
        setTotalPages(Math.ceil(ordersResponse.data.count / 10) || 1);

        // Fetch return requests
        try {
          const returnsResponse = await returnRequestAPI.getMyReturns();
          setReturnRequests(returnsResponse.data);
        } catch (returnError) {
          console.error('Error fetching return requests:', returnError);
          // Don't fail the whole page if returns fail
        }

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.response?.data?.detail || 'Failed to load data');
        setOrders([]);
        setLoading(false);
      }
    };

    fetchData();
  }, [currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleReturnSuccess = () => {
    setShowReturnForm(null);
    // Refresh data
    window.location.reload();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'shipped':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return <div className="container mx-auto p-4">Loading orders...</div>;
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-red-500 mb-6">{error}</div>
        <DebugOrders />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">My Orders & Returns</h1>

      {/* Tabs */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('orders')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'orders'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Orders ({orders.length})
          </button>
          <button
            onClick={() => setActiveTab('returns')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'returns'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Returns ({returnRequests.length})
          </button>
        </nav>
      </div>

      {/* Orders Tab */}
      {activeTab === 'orders' && (
        <>
          {orders.length > 0 ? (
            <div>
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Order #
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {orders.map((order) => (
                        <tr key={order.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {order.order_number || order.id.slice(0, 8)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {new Date(order.created_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(order.status)}`}>
                              {order.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {formatINR(order.total)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap space-x-2">
                            <Link to={`/order/${order.id}`} className="text-blue-600 hover:underline">
                              View Details
                            </Link>
                            {order.can_return && !order.has_return_request && (
                              <button
                                onClick={() => setShowReturnForm(order.id)}
                                className="text-orange-600 hover:underline"
                              >
                                Request Return
                              </button>
                            )}
                            {order.has_return_request && (
                              <span className="text-sm text-gray-500">
                                Return: {order.return_request_status}
                              </span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <nav className="flex items-center">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-md mr-2 ${
                    currentPage === 1
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Previous
                </button>

                {Array.from({ length: totalPages }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => handlePageChange(index + 1)}
                    className={`px-3 py-1 rounded-md mx-1 ${
                      currentPage === index + 1
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {index + 1}
                  </button>
                ))}

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-md ml-2 ${
                    currentPage === totalPages
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Next
                </button>
              </nav>
            </div>
          )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-lg mb-4">You haven't placed any orders yet.</p>
              <Link
                to="/shop"
                className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Start Shopping
              </Link>
            </div>
          )}
        </>
      )}

      {/* Returns Tab */}
      {activeTab === 'returns' && (
        <div>
          {returnRequests.length > 0 ? (
            <div className="space-y-6">
              {returnRequests.map((returnRequest) => (
                <ReturnStatus key={returnRequest.id} returnRequest={returnRequest} />
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-lg mb-4">You haven't submitted any return requests yet.</p>
              <p className="text-gray-600">
                Return requests can be made for delivered orders from the Orders tab.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Return Request Form Modal */}
      {showReturnForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-md w-full">
            <ReturnRequestForm
              orderId={showReturnForm}
              onSuccess={handleReturnSuccess}
              onCancel={() => setShowReturnForm(null)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Orders;