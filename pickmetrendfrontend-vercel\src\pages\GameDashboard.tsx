import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import GameLobby from '../components/gaming/GameLobby';
import BattleArena from '../components/gaming/BattleArena';
import WalletBalance from '../components/wallet/WalletBalance';
import TicTacToe from '../components/games/TicTacToe';
import SpinWheel from '../components/gaming/SpinWheel';

const GameDashboard: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [currentView, setCurrentView] = useState<'lobby' | 'battle' | 'tic-tac-toe' | 'spin-wheel'>('lobby');
  const [currentBattleId, setCurrentBattleId] = useState<string | null>(null);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
        <div className="relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-72 h-72 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-10"></div>
          <div className="absolute bottom-0 left-0 -mb-32 -ml-32 w-64 h-64 bg-gradient-to-tr from-indigo-400 to-cyan-500 rounded-full opacity-10"></div>

          <div className="relative container mx-auto px-4 py-24 text-center">
            <div className="max-w-2xl mx-auto">
              <div className="mb-8">
                <span className="text-6xl mb-4 block">🎮</span>
                <h1 className="text-4xl font-bold text-gray-900 mb-4">Gaming Dashboard</h1>
                <p className="text-xl text-gray-600 mb-8">Please log in to access the gaming features and start your adventure!</p>
              </div>

              <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                <div className="mb-6">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-2">Ready to Play?</h2>
                  <p className="text-gray-600">Join thousands of players in exciting battles and earn rewards!</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                  <div className="text-center p-4">
                    <div className="text-3xl mb-2">🎯</div>
                    <div className="font-semibold text-gray-800">Battle Arena</div>
                    <div className="text-sm text-gray-600">Compete with others</div>
                  </div>
                  <div className="text-center p-4">
                    <div className="text-3xl mb-2">🪙</div>
                    <div className="font-semibold text-gray-800">Earn Tokens</div>
                    <div className="text-sm text-gray-600">Win rewards</div>
                  </div>
                  <div className="text-center p-4">
                    <div className="text-3xl mb-2">🏆</div>
                    <div className="font-semibold text-gray-800">Tournaments</div>
                    <div className="text-sm text-gray-600">Climb leaderboards</div>
                  </div>
                </div>

                <a
                  href="/login"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🚀</span>
                  Login to Start Playing
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleBattleCreated = (battleId: string) => {
    setCurrentBattleId(battleId);
    setCurrentView('battle');
  };

  const handleBattleComplete = () => {
    setCurrentView('lobby');
    setCurrentBattleId(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Hero Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700">
        {/* Background decorative elements */}
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-64 h-64 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>

        <div className="relative container mx-auto px-4 py-12">
          <div className="flex flex-col lg:flex-row justify-between items-center">
            <div className="text-white mb-6 lg:mb-0">
              <div className="flex items-center mb-4">
                <span className="text-5xl mr-4">🎮</span>
                <div>
                  <h1 className="text-4xl font-bold mb-2">Gaming Dashboard</h1>
                  <p className="text-xl text-blue-100">Welcome back, {user?.username}! Ready to dominate?</p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
              {isAuthenticated && (
                <div className="bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-4 border border-white border-opacity-30">
                  <WalletBalance showDetails={true} className="text-white" />
                </div>
              )}

              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                {currentView !== 'lobby' && (
                  <button
                    onClick={() => setCurrentView('lobby')}
                    className="px-6 py-3 bg-white bg-opacity-20 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-opacity-30 transition-all duration-200 border border-white border-opacity-30"
                  >
                    <span className="mr-2">⬅️</span>
                    Back to Lobby
                  </button>
                )}

                {currentView === 'lobby' && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentView('tic-tac-toe')}
                      className="px-6 py-3 bg-white bg-opacity-20 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-opacity-30 transition-all duration-200 border border-white border-opacity-30"
                    >
                      <span className="mr-2">⭕</span>
                      Tic Tac Toe
                    </button>
                    <button
                      onClick={() => setCurrentView('spin-wheel')}
                      className="px-6 py-3 bg-white bg-opacity-20 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-opacity-30 transition-all duration-200 border border-white border-opacity-30"
                    >
                      <span className="mr-2">🎡</span>
                      Spin Wheel
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {currentView === 'lobby' && (
          <GameLobby
            onBattleCreated={handleBattleCreated}
            onGameSelected={(gameType) => {
              if (gameType === 'tic-tac-toe') {
                setCurrentView('tic-tac-toe');
              }
            }}
          />
        )}

        {currentView === 'battle' && currentBattleId && (
          <BattleArena
            battleId={currentBattleId}
            onBattleComplete={handleBattleComplete}
          />
        )}

        {currentView === 'tic-tac-toe' && (
          <div className="max-w-2xl mx-auto">
            <TicTacToe />
          </div>
        )}

        {currentView === 'spin-wheel' && (
          <div className="max-w-2xl mx-auto">
            <SpinWheel
              onRewardWon={(reward) => {
                console.log('Reward won:', reward);
                // You can add additional reward handling here
              }}
              onBalanceUpdate={(newBalance) => {
                console.log('Balance updated:', newBalance);
                // You can trigger wallet balance refresh here
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default GameDashboard;
