import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';

const Login: React.FC = () => {
  const [usernameOrEmail, setUsernameOrEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState('');
  const { login, error, isAuthenticated, loading, user } = useAuth();
  const { fetchCart: refetchCart } = useCart();
  const navigate = useNavigate();
  const location = useLocation();

  // Get returnUrl from query parameters
  const searchParams = new URLSearchParams(location.search);
  const returnUrl = searchParams.get('returnUrl') || '/dashboard';

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      // Make sure to refetch cart data to ensure it's up to date
      refetchCart();
      navigate(returnUrl);
    }
  }, [isAuthenticated, user, navigate, refetchCart, returnUrl]);

  // Update form error when auth context error changes
  useEffect(() => {
    setFormError(error || '');
  }, [error]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setFormError('');

    // Basic form validation
    if (!usernameOrEmail || !password) {
      setFormError('Please enter both username/email and password');
      return;
    }

    console.log('Login form submitted with credential:', usernameOrEmail);
    console.log('API URL:', process.env.REACT_APP_API_URL);

    try {
      // Use the login function from AuthContext which now handles both email and username
      await login(usernameOrEmail, password);

      // After successful login, the AuthContext and the useEffect above will handle the redirect
      console.log('Login successful, redirecting to:', returnUrl);
    } catch (err: any) {
      console.error('Login error in component:', err);
      // The error is already set in AuthContext, useEffect will update formError

      // Add a link to the login test page for debugging
      setFormError((prevError) => {
        return `${prevError || err.message || 'Login failed'}.
        For debugging, try the <a href="/login-test" class="text-blue-600 underline">Login Test Page</a>.`;
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 -mt-4 -mr-4 w-72 h-72 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-10"></div>
      <div className="absolute bottom-0 left-0 -mb-32 -ml-32 w-64 h-64 bg-gradient-to-tr from-indigo-400 to-cyan-500 rounded-full opacity-10"></div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
          <div className="text-center mb-8">
            <div className="mb-4">
              <span className="text-5xl">🔐</span>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Welcome Back!</h2>
            <p className="text-gray-600">
              Sign in to access your account and continue your PickMeTrend journey
            </p>
            <p className="mt-4 text-sm text-gray-600">
              Don't have an account?{' '}
              <Link to="/register" className="font-medium text-purple-600 hover:text-purple-500">
                Create one here
              </Link>
            </p>
          </div>

          {returnUrl && returnUrl !== '/dashboard' && (
            <div className="mb-6 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-xl text-sm">
              <div className="flex items-center">
                <span className="mr-2">ℹ️</span>
                Please sign in to add items to your cart and continue shopping.
              </div>
            </div>
          )}

          {formError && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-xl">
              <div className="flex items-center">
                <span className="mr-2">⚠️</span>
                <div dangerouslySetInnerHTML={{ __html: formError }} />
              </div>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <label htmlFor="username-or-email" className="block text-sm font-medium text-gray-700 mb-2">
                  Username or Email
                </label>
                <input
                  id="username-or-email"
                  name="usernameOrEmail"
                  type="text"
                  autoComplete="username email"
                  required
                  value={usernameOrEmail}
                  onChange={(e) => setUsernameOrEmail(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  placeholder="Enter your username or email"
                />
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link to="/forgot-password" className="font-medium text-purple-600 hover:text-purple-500">
                  Forgot password?
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center items-center py-3 px-4 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </>
                ) : (
                  <>
                    <span className="mr-2">🚀</span>
                    Sign In
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;