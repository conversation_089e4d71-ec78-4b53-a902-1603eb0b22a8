from rest_framework import serializers
from .models import Wallet, WalletTransaction, TokenRequest


class WalletTransactionSerializer(serializers.ModelSerializer):
    transaction_type_display = serializers.CharField(source='get_transaction_type_display', read_only=True)
    amount_display = serializers.SerializerMethodField()

    class Meta:
        model = WalletTransaction
        fields = [
            'id', 'transaction_type', 'transaction_type_display', 
            'amount', 'amount_display', 'description', 'balance_after', 
            'created_at', 'game_id', 'order_id'
        ]

    def get_amount_display(self, obj):
        sign = "+" if obj.amount > 0 else ""
        return f"{sign}{obj.amount}"


class WalletSerializer(serializers.ModelSerializer):
    balance_in_inr = serializers.ReadOnlyField()
    recent_transactions = serializers.SerializerMethodField()

    class Meta:
        model = Wallet
        fields = [
            'id', 'balance', 'balance_in_inr', 'total_earned', 
            'total_spent', 'created_at', 'updated_at', 'recent_transactions'
        ]

    def get_recent_transactions(self, obj):
        recent = obj.transactions.all()[:5]
        return WalletTransactionSerializer(recent, many=True).data


class TokenRequestSerializer(serializers.ModelSerializer):
    """Serializer for creating token refill requests"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    processed_by_username = serializers.CharField(source='processed_by.username', read_only=True)
    can_request = serializers.SerializerMethodField()

    class Meta:
        model = TokenRequest
        fields = [
            'id', 'user', 'user_username', 'message', 'status', 'status_display',
            'requested_at', 'processed_at', 'processed_by', 'processed_by_username',
            'tokens_granted', 'admin_notes', 'can_request'
        ]
        read_only_fields = [
            'id', 'user', 'status', 'requested_at', 'processed_at',
            'processed_by', 'tokens_granted', 'admin_notes'
        ]

    def get_can_request(self, obj):
        """Check if user can request token refill"""
        if hasattr(obj, 'user') and hasattr(obj.user, 'wallet'):
            can_request, message = obj.can_request_refill()
            return {'can_request': can_request, 'message': message}
        return {'can_request': False, 'message': 'Wallet not found'}

    def validate(self, data):
        """Validate token request creation"""
        user = self.context['request'].user

        # Check if user has wallet
        if not hasattr(user, 'wallet'):
            raise serializers.ValidationError("User wallet not found")

        # Check if user can request refill
        temp_request = TokenRequest(user=user)
        can_request, message = temp_request.can_request_refill()

        if not can_request:
            raise serializers.ValidationError(message)

        return data


class TokenRequestAdminSerializer(serializers.ModelSerializer):
    """Serializer for admin to process token requests"""
    user_username = serializers.CharField(source='user.username', read_only=True)
    user_balance = serializers.SerializerMethodField()

    class Meta:
        model = TokenRequest
        fields = [
            'id', 'user', 'user_username', 'user_balance', 'message', 'status',
            'requested_at', 'processed_at', 'processed_by', 'tokens_granted', 'admin_notes'
        ]
        read_only_fields = ['id', 'user', 'requested_at']

    def get_user_balance(self, obj):
        """Get user's current token balance"""
        if hasattr(obj.user, 'wallet'):
            return obj.user.wallet.balance
        return 0
