# Generated manually to fix ProductImage schema conflicts

import products.fields
import products.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0011_product_cleaned'),
    ]

    operations = [
        # Update the image field to use our simplified URLOrFileField
        migrations.AlterField(
            model_name='productimage',
            name='image',
            field=products.fields.URLOrFileField(blank=True, null=True, upload_to='products/', validators=[products.models.validate_image_file]),
        ),
    ]
