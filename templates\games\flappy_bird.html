{% extends 'base.html' %}
{% load static %}

{% block title %}Flappy Bird - PickMeTrend Gaming{% endblock %}

{% block extra_css %}
<style>
    .game-page {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .game-header {
        text-align: center;
        color: white;
        margin-bottom: 30px;
    }
    
    .game-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .game-header p {
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    .game-wrapper {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        gap: 30px;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }
    
    .game-frame {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        overflow: hidden;
        position: relative;
    }
    
    .game-sidebar {
        background: rgba(255,255,255,0.95);
        border-radius: 15px;
        padding: 25px;
        width: 300px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .sidebar-section {
        margin-bottom: 25px;
    }
    
    .sidebar-section h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 1.2rem;
        border-bottom: 2px solid #4CAF50;
        padding-bottom: 5px;
    }
    
    .user-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        padding: 15px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        display: block;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .token-balance {
        background: linear-gradient(135deg, #FFD700, #FFA500);
        color: #333;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(255,215,0,0.3);
    }
    
    .token-balance .balance {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }
    
    .game-rules {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #4CAF50;
    }
    
    .game-rules h4 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .game-rules ul {
        list-style: none;
        padding: 0;
    }
    
    .game-rules li {
        padding: 5px 0;
        color: #666;
    }
    
    .game-rules li:before {
        content: "🎮 ";
        margin-right: 8px;
    }
    
    .back-button {
        position: fixed;
        top: 20px;
        left: 20px;
        background: rgba(255,255,255,0.9);
        color: #333;
        padding: 10px 20px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: bold;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        z-index: 1000;
    }
    
    .back-button:hover {
        background: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        text-decoration: none;
        color: #333;
    }
    
    @media (max-width: 768px) {
        .game-wrapper {
            flex-direction: column;
            align-items: center;
        }
        
        .game-sidebar {
            width: 100%;
            max-width: 400px;
            order: 2;
        }
        
        .game-frame {
            order: 1;
        }
        
        .game-header h1 {
            font-size: 2rem;
        }
        
        .user-stats {
            grid-template-columns: 1fr;
        }
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100;
    }
    
    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #4CAF50;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="game-page">
    <a href="{% url 'gaming:game_hub' %}" class="back-button">← Back to Games</a>
    
    <div class="game-header">
        <h1>🐦 Flappy Bird</h1>
        <p>Test your reflexes and earn tokens!</p>
    </div>
    
    <div class="game-wrapper">
        <div class="game-frame">
            <iframe 
                src="{% static 'games/flappy-bird.html' %}" 
                width="400" 
                height="600" 
                frameborder="0"
                id="gameFrame">
            </iframe>
            <div class="loading-overlay" id="gameLoading">
                <div class="spinner"></div>
            </div>
        </div>
        
        <div class="game-sidebar">
            <div class="sidebar-section">
                <div class="token-balance">
                    <span class="balance" id="userTokens">{{ user.wallet.balance|default:"0" }}</span>
                    <small>Available Tokens</small>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h3>Your Stats</h3>
                <div class="user-stats" id="userStats">
                    <div class="stat-card">
                        <span class="stat-value" id="totalGames">--</span>
                        <span class="stat-label">Games Played</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="highScore">--</span>
                        <span class="stat-label">High Score</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="bestPipes">--</span>
                        <span class="stat-label">Best Pipes</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="winRate">--%</span>
                        <span class="stat-label">Win Rate</span>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-section">
                <div class="game-rules">
                    <h4>🎯 Game Rules</h4>
                    <ul>
                        <li>Costs 2 tokens to play</li>
                        <li>Tap/click to make bird flap</li>
                        <li>Avoid pipes and ground</li>
                        <li>Pass 10+ pipes: +5 tokens</li>
                        <li>Less than 5 pipes: -1 extra token</li>
                        <li>Early exit: lose all tokens</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading overlay when game loads
    const gameFrame = document.getElementById('gameFrame');
    const loadingOverlay = document.getElementById('gameLoading');
    
    gameFrame.addEventListener('load', function() {
        setTimeout(() => {
            loadingOverlay.style.display = 'none';
        }, 1000);
    });
    
    // Load user stats
    loadFlappyBirdStats();
    
    // Listen for token balance updates from the game
    window.addEventListener('message', function(event) {
        if (event.data.type === 'tokenUpdate') {
            document.getElementById('userTokens').textContent = event.data.balance;
        }
    });
});

async function loadFlappyBirdStats() {
    try {
        const response = await fetch('/api/gaming/flappy-bird/stats/', {
            headers: {
                'X-CSRFToken': getCSRFToken()
            },
            credentials: 'same-origin'
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                updateStatsDisplay(data.stats);
            }
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

function updateStatsDisplay(stats) {
    document.getElementById('totalGames').textContent = stats.total_games || 0;
    document.getElementById('highScore').textContent = stats.high_score || 0;
    document.getElementById('bestPipes').textContent = stats.best_pipes_passed || 0;
    document.getElementById('winRate').textContent = stats.win_rate || 0;
    document.getElementById('userTokens').textContent = stats.current_balance || 0;
}

function getCSRFToken() {
    // Get CSRF token from Django
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfToken) return csrfToken.value;

    // Try to get from cookies
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') return value;
    }

    return '';
}
</script>
{% endblock %}
