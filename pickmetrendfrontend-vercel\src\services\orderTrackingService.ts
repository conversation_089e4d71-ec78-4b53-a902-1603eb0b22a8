import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';

/**
 * Service for interacting with the order tracking API
 */
const orderTrackingService = {
  /**
   * Get tracking information for a specific order
   * @param orderId The ID of the order
   */
  getOrderTracking: async (orderId: string) => {
    const response = await axios.get(`${API_URL}/api/order_tracking/tracking/by_order/`, {
      headers: {
        'Authorization': `JWT ${localStorage.getItem('access_token')}`
      },
      params: {
        order_id: orderId
      }
    });
    return response.data;
  },

  /**
   * Refresh tracking information for a specific order
   * @param orderId The ID of the order
   */
  refreshOrderTracking: async (orderId: string) => {
    const response = await axios.post(
      `${API_URL}/api/order_tracking/tracking/refresh/`,
      { order_id: orderId },
      {
        headers: {
          'Authorization': `JWT ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }
};

export default orderTrackingService;
