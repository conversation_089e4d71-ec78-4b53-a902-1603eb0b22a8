#!/usr/bin/env python
"""
Test script to verify profile update functionality
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from accounts.models import UserProfile
from accounts.serializers import UserSerializer
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken


def test_profile_update():
    """Test profile update functionality"""
    
    # Create or get a test user
    try:
        user = User.objects.get(username='testuser')
        print(f"Using existing test user: {user.username}")
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        print(f"Created test user: {user.username}")
    
    # Ensure user has a profile
    if not hasattr(user, 'profile'):
        UserProfile.objects.create(user=user)
        user.refresh_from_db()
        print("Created user profile")
    
    # Test serializer directly
    print("\n=== Testing UserSerializer directly ===")
    
    test_data = {
        'first_name': 'Updated',
        'last_name': 'Name',
        'email': '<EMAIL>',
        'profile': {
            'user_type': 'customer',
            'phone_number': '+1234567890',
            'address': '123 Test Street',
            'city': 'Test City',
            'state': 'Test State',
            'country': 'Test Country',
            'zip_code': '12345'
        }
    }
    
    serializer = UserSerializer(user, data=test_data, partial=True)
    if serializer.is_valid():
        updated_user = serializer.save()
        print("✅ Serializer update successful")
        print(f"Updated user: {updated_user.first_name} {updated_user.last_name}")
        print(f"Profile phone: {updated_user.profile.phone_number}")
        print(f"Profile address: {updated_user.profile.address}")
    else:
        print("❌ Serializer validation failed")
        print(f"Errors: {serializer.errors}")
    
    # Test API endpoint
    print("\n=== Testing API endpoint ===")
    
    client = APIClient()
    
    # Generate JWT token
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    # Set authorization header
    client.credentials(HTTP_AUTHORIZATION=f'JWT {access_token}')
    
    # Test GET request first
    response = client.get('/api/auth/profile/')
    print(f"GET /api/auth/profile/ - Status: {response.status_code}")
    if response.status_code == 200:
        print(f"Current profile data: {json.dumps(response.data, indent=2)}")
    else:
        print(f"GET failed: {response.data}")
    
    # Test PATCH request
    patch_data = {
        'first_name': 'API Updated',
        'last_name': 'User',
        'email': '<EMAIL>',
        'profile': {
            'user_type': 'customer',
            'phone_number': '+9876543210',
            'address': '456 API Street',
            'city': 'API City',
            'state': 'API State',
            'country': 'API Country',
            'zip_code': '54321'
        }
    }
    
    response = client.patch('/api/auth/profile/', patch_data, format='json')
    print(f"\nPATCH /api/auth/profile/ - Status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API update successful")
        print(f"Updated profile data: {json.dumps(response.data, indent=2)}")
    else:
        print("❌ API update failed")
        print(f"Error response: {response.data}")
        print(f"Response content: {response.content}")


if __name__ == '__main__':
    test_profile_update()
