# Customer Communication Automation System

This Django app provides a complete customer communication automation system for PickMeTrend, including feedback emails, support tickets, and chatbot integration.

## Features

### Customer Feedback Automation
- Automatic feedback request emails sent 7 days after order delivery
- Feedback form for customers to rate their experience
- Admin dashboard to view and analyze customer feedback

### Support Ticket System
- Support form for customers to submit queries
- Email notifications for new support tickets
- Admin interface for managing and responding to tickets
- Auto-reply emails to customers

### Email Templates
- Customizable email templates for different communication types
- HTML and plain text versions
- Dynamic content using template variables

### Chatbot Integration
- Integration with Tidio or Dialogflow chatbots
- Pre-configured with common FAQs
- Customizable appearance and behavior

## Installation

### 1. Install Required Packages

```bash
pip install celery redis django-celery-beat django-celery-results
```

### 2. Install and Configure Redis

#### For Windows:
1. Download and install Redis for Windows from https://github.com/tporadowski/redis/releases
2. Start Redis server:
```
redis-server
```

#### For Linux/Mac:
```bash
sudo apt-get install redis-server  # Ubuntu/Debian
brew install redis                 # Mac with Homebrew
```

### 3. Configure Celery

Add the following to your settings.py:

```python
# Celery Configuration
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'django-db'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60  # 30 minutes

# Django Celery Beat Configuration
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

# Customer Communication Settings
CUSTOMER_FEEDBACK_DAYS = 7  # Days after order to send feedback email
SUPPORT_EMAIL = '<EMAIL>'  # Email for support tickets
```

### 4. Configure Email Settings

For development:
```python
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

For production (example with Gmail):
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'  # Use app password for Gmail
DEFAULT_FROM_EMAIL = 'PickMeTrend <<EMAIL>>'
```

## Running Celery

### Start Celery Worker
```bash
# From the project root directory
celery -A dropshipping_backend worker -l info
```

### Start Celery Beat (for scheduled tasks)
```bash
# From the project root directory
celery -A dropshipping_backend beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
```

## Chatbot Integration

### Tidio
1. Sign up for a free account at https://www.tidio.com/
2. Get your Tidio key from the dashboard
3. Replace `YOUR_TIDIO_KEY` in the chatbot_integration.html template
4. Include the template in your base.html:
```html
{% include 'customer_communication/chatbot_integration.html' %}
```

### Dialogflow
1. Create a Google Cloud account and set up a Dialogflow agent
2. Get your agent ID from the Dialogflow console
3. Replace `YOUR_DIALOGFLOW_AGENT_ID` in the chatbot_integration.html template
4. Uncomment the Dialogflow section and comment out the Tidio section

## Usage

### Creating Email Templates
1. Go to the Django admin panel
2. Navigate to "Email Templates"
3. Create templates for different communication types:
   - Order Confirmation
   - Feedback Request
   - Support Ticket Confirmation
   - Support Ticket Response

### Managing Support Tickets
1. Go to the Django admin panel
2. Navigate to "Support Tickets"
3. View, filter, and respond to tickets
4. Use the inline response form to reply to customers

### Viewing Customer Feedback
1. Go to the Django admin panel
2. Navigate to "Customer Feedback"
3. View ratings and comments from customers

## API Endpoints

- `POST /customer/api/support-form/` - Submit a support ticket
- `GET /customer/api/tickets/` - List support tickets (authenticated users)
- `POST /customer/api/tickets/{id}/respond/` - Respond to a ticket
- `POST /customer/api/feedback/` - Submit feedback for an order

## Frontend Pages

- `/customer/support/` - Support form page
- `/customer/feedback/{order_id}/` - Feedback form page
