# 🚀 Railway + Vercel Deployment Guide - Token Purchase Feature

## 🎯 **DEPLOYMENT ARCHITECTURE**

- **Backend**: Railway (https://web-production-e8443.up.railway.app)
- **Frontend**: Vercel (https://pickmetrendfrontend-vercel.vercel.app)
- **Database**: PostgreSQL on Railway
- **Payments**: Razorpay Live Integration

---

## 🔧 **RAILWAY BACKEND DEPLOYMENT**

### **Step 1: Environment Variables**

In your Railway project dashboard, set these environment variables:

```bash
# Razorpay Live Keys
RAZORPAY_KEY_ID=***********************
RAZORPAY_KEY_SECRET=y0CXtCz5Cdfet2PcH3tXetia

# Database (Auto-configured by Railway)
DATABASE_URL=postgresql://postgres:password@host:port/database

# Django Settings
SECRET_KEY=your-production-secret-key
DEBUG=False
ALLOWED_HOSTS=web-production-e8443.up.railway.app,localhost,127.0.0.1

# CORS Settings (for Vercel frontend)
CORS_ALLOWED_ORIGINS=https://pickmetrendfrontend-vercel.vercel.app,https://your-custom-domain.com
CORS_ALLOW_CREDENTIALS=True

# Email Configuration (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
DEFAULT_FROM_EMAIL=<EMAIL>

# Media Storage (Cloudinary)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

### **Step 2: Deploy Backend**

1. **Push to Repository**
   ```bash
   cd /path/to/backend
   git add .
   git commit -m "feat: Add token purchase system for production"
   git push origin main
   ```

2. **Railway Auto-Deploy**
   - Railway will automatically detect changes
   - Monitor deployment in Railway dashboard
   - Check logs for any errors

3. **Run Migrations**
   ```bash
   # In Railway dashboard, go to your service
   # Open the "Deploy" tab and run:
   python manage.py migrate
   python manage.py create_token_packs
   ```

### **Step 3: Verify Backend**

```bash
# Test API endpoints
curl https://web-production-e8443.up.railway.app/api/wallet/token-packs/
# Should return 401 (authentication required)

curl https://web-production-e8443.up.railway.app/admin/
# Should return Django admin login page
```

---

## 🌐 **VERCEL FRONTEND DEPLOYMENT**

### **Step 1: Environment Variables**

In your Vercel project dashboard (Settings → Environment Variables):

```bash
# API Configuration
REACT_APP_API_URL=https://web-production-e8443.up.railway.app
REACT_APP_ENVIRONMENT=production

# Razorpay Configuration
REACT_APP_RAZORPAY_KEY_ID=***********************
REACT_APP_PAYMENT_TEST_MODE=false

# CORS Configuration
REACT_APP_ENABLE_CORS=true
```

**Important**: Set these for all environments (Production, Preview, Development)

### **Step 2: Deploy Frontend**

1. **Push to Repository**
   ```bash
   cd /path/to/frontend
   git add .
   git commit -m "feat: Add token purchase system for production"
   git push origin master  # Vercel uses master branch
   ```

2. **Vercel Auto-Deploy**
   - Vercel will automatically build and deploy
   - Monitor deployment in Vercel dashboard
   - Check build logs for any errors

### **Step 3: Verify Frontend**

1. **Visit Deployed Site**
   - Go to your Vercel URL
   - Navigate to `/buy-tokens`
   - Should show "Live Mode: Real Razorpay payments enabled"

2. **Test Token Purchase Flow**
   - Login with test account
   - Select token pack
   - Verify Razorpay dialog opens

---

## 🔗 **CORS CONFIGURATION**

### **Backend CORS Settings**

In your Django settings, ensure CORS is properly configured:

```python
# settings.py
CORS_ALLOWED_ORIGINS = [
    "https://pickmetrendfrontend-vercel.vercel.app",
    "https://your-custom-domain.com",  # If you have a custom domain
]

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]
```

### **Frontend API Configuration**

The frontend is configured to call your Railway backend:

```javascript
// API calls will go to:
https://web-production-e8443.up.railway.app/api/wallet/token-packs/
https://web-production-e8443.up.railway.app/api/wallet/create-token-order/
https://web-production-e8443.up.railway.app/api/wallet/verify-token-payment/
```

---

## 🧪 **TESTING PRODUCTION DEPLOYMENT**

### **Step 1: Basic Functionality**

1. **Frontend Access**
   ```
   https://pickmetrendfrontend-vercel.vercel.app/buy-tokens
   ```

2. **Backend API**
   ```
   https://web-production-e8443.up.railway.app/api/wallet/token-packs/
   ```

### **Step 2: End-to-End Testing**

1. **Login Flow**
   - Go to production frontend
   - Login with test account
   - Navigate to wallet

2. **Token Purchase Flow**
   - Click "Buy Tokens"
   - Select token pack
   - Complete Razorpay payment
   - Verify tokens added

### **Step 3: Payment Testing**

Use Razorpay test cards in live mode:

```
Success Card: 4111 1111 1111 1111
Failure Card: 4000 0000 0000 0002
Expiry: Any future date
CVV: Any 3 digits
```

---

## 📊 **MONITORING & LOGS**

### **Railway Backend Monitoring**

1. **Access Logs**
   - Railway Dashboard → Your Service → Logs
   - Monitor for errors in token purchase endpoints

2. **Database Monitoring**
   - Check TokenPurchase records
   - Monitor wallet balance updates

### **Vercel Frontend Monitoring**

1. **Build Logs**
   - Vercel Dashboard → Deployments
   - Check for build errors

2. **Runtime Logs**
   - Vercel Dashboard → Functions (if using)
   - Monitor for runtime errors

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

1. **CORS Errors**
   ```
   Error: Access to fetch at 'railway-url' from origin 'vercel-url' has been blocked by CORS policy
   ```
   **Solution**: Update CORS_ALLOWED_ORIGINS in Django settings

2. **Environment Variables Not Loading**
   ```
   Error: REACT_APP_API_URL is undefined
   ```
   **Solution**: Check Vercel environment variables are set for all environments

3. **Razorpay Key Mismatch**
   ```
   Error: Invalid key_id
   ```
   **Solution**: Verify RAZORPAY_KEY_ID matches in both Railway and Vercel

4. **Database Connection Issues**
   ```
   Error: could not connect to server
   ```
   **Solution**: Check Railway database URL and connection

### **Quick Fixes**

1. **Redeploy Backend**
   ```bash
   # Force redeploy on Railway
   git commit --allow-empty -m "Force redeploy"
   git push origin main
   ```

2. **Redeploy Frontend**
   ```bash
   # Force redeploy on Vercel
   git commit --allow-empty -m "Force redeploy"
   git push origin master
   ```

---

## 🎉 **DEPLOYMENT COMPLETE!**

Once deployed successfully, your token purchase system will be live with:

### **✅ Production URLs**
- **Frontend**: https://pickmetrendfrontend-vercel.vercel.app
- **Backend**: https://web-production-e8443.up.railway.app
- **Token Purchase**: https://pickmetrendfrontend-vercel.vercel.app/buy-tokens

### **✅ Live Features**
- Real Razorpay payments
- Token pack selection
- Wallet integration
- Transaction history
- Admin management

### **✅ Security**
- HTTPS encryption
- CORS protection
- Authentication required
- Payment verification

**Your token purchase system is now live and ready for real users!** 🚀💰
