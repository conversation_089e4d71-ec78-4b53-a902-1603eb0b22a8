import React, { useState, useEffect } from 'react';
import TokenPurchaseButton from './TokenPurchaseButton';

interface LowBalanceAlertProps {
  balance: number;
  onTokensAdded: (tokensAdded: number) => void;
  threshold?: number;
  autoShow?: boolean;
  className?: string;
}

const LowBalanceAlert: React.FC<LowBalanceAlertProps> = ({
  balance,
  onTokensAdded,
  threshold = 10,
  autoShow = true,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    if (autoShow && balance <= threshold && !isDismissed) {
      setIsVisible(true);
    } else if (balance > threshold) {
      setIsVisible(false);
      setIsDismissed(false);
    }
  }, [balance, threshold, autoShow, isDismissed]);

  const handleDismiss = () => {
    setIsDismissed(true);
    setIsVisible(false);
  };

  const handleTokensAdded = (tokensAdded: number) => {
    onTokensAdded(tokensAdded);
    setIsVisible(false);
    setIsDismissed(false);
  };

  if (!isVisible || balance > threshold) {
    return null;
  }

  return (
    <div className={`bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-xl p-6 shadow-lg ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-2xl">⚠️</span>
            </div>
          </div>
          <div className="ml-4 flex-1">
            <h3 className="text-lg font-semibold text-orange-800 mb-2">
              Low Token Balance
            </h3>
            <p className="text-orange-700 mb-4">
              You have only <span className="font-bold">{balance} tokens</span> remaining. 
              Buy more tokens to continue playing games and earning rewards!
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <TokenPurchaseButton
                currentBalance={balance}
                onTokensAdded={handleTokensAdded}
                variant="primary"
                size="md"
                showIcon={true}
              >
                🪙 Buy Tokens Now
              </TokenPurchaseButton>
              
              <button
                onClick={handleDismiss}
                className="px-4 py-2 text-orange-700 hover:text-orange-800 font-medium transition-colors"
              >
                Remind me later
              </button>
            </div>
          </div>
        </div>
        
        <button
          onClick={handleDismiss}
          className="flex-shrink-0 text-orange-400 hover:text-orange-600 transition-colors ml-4"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      {/* Quick benefits reminder */}
      <div className="mt-4 pt-4 border-t border-orange-200">
        <h4 className="text-sm font-semibold text-orange-800 mb-2">💡 Why buy tokens?</h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-xs text-orange-700">
          <div className="flex items-center">
            <span className="mr-1">🎮</span>
            <span>Play more games</span>
          </div>
          <div className="flex items-center">
            <span className="mr-1">🛒</span>
            <span>Get shopping discounts</span>
          </div>
          <div className="flex items-center">
            <span className="mr-1">🏆</span>
            <span>Enter tournaments</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LowBalanceAlert;
