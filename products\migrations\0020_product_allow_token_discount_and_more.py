# Generated by Django 5.0.2 on 2025-06-03 13:00

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0019_remove_product_allow_token_discount_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='allow_token_discount',
            field=models.BooleanField(default=False, help_text='Allow customers to use tokens for partial payment on this product'),
        ),
        migrations.AddField(
            model_name='product',
            name='token_discount_max_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Maximum INR amount that can be paid with tokens (optional cap)', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='token_discount_percentage',
            field=models.PositiveIntegerField(default=20, help_text='Maximum percentage of product price that can be paid with tokens (1-100%)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)]),
        ),
    ]
