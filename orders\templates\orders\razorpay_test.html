<!DOCTYPE html>
<html>
<head>
    <title>Razorpay Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        button {
            background-color: #3399cc;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            display: none;
        }
        .debug-info {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Razorpay Test</h1>
        <p>This page tests your Razorpay integration. Click the button below to create a test order and open the Razorpay payment form.</p>

        <button id="rzp-button">Pay with Razorpay</button>

        <div id="result" class="result">
            <h3>Payment Result</h3>
            <pre id="payment-details"></pre>
        </div>

        <div class="debug-info">
            <h3>Debug Information</h3>
            <p><strong>Razorpay Key ID:</strong> {{ razorpay_key_id }}</p>
        </div>
    </div>

    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        console.log('Razorpay Key ID:', '{{ razorpay_key_id }}');

        document.getElementById('rzp-button').addEventListener('click', function() {
            showResult('Creating test order...');

            // Create a test order
            fetch('/razorpay/test/create_test_order/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({
                    amount: 50000,  // 500 INR in paise
                    currency: 'INR',
                    receipt: 'test_receipt_' + Date.now()
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Order data:', data);

                if (data.error) {
                    showResult('Error: ' + data.error);
                    return;
                }

                // Configure Razorpay
                var options = {
                    key: '{{ razorpay_key_id }}',
                    amount: data.amount,
                    currency: data.currency,
                    name: 'PickMeTrend',
                    description: 'Test Payment',
                    order_id: data.id,
                    handler: function (response) {
                        console.log('Payment response:', response);

                        // Handle successful payment
                        showResult('Payment successful!\n\nPayment ID: ' + response.razorpay_payment_id +
                                  '\nOrder ID: ' + response.razorpay_order_id +
                                  '\nSignature: ' + response.razorpay_signature);

                        // Verify payment with backend
                        verifyPayment(response);
                    },
                    prefill: {
                        name: 'Test User',
                        email: '<EMAIL>',
                        contact: '9353014895'  // Using the contact number from your memories
                    },
                    theme: {
                        color: '#3399cc'
                    },
                    modal: {
                        ondismiss: function() {
                            console.log('Payment modal dismissed');
                            showResult('Payment cancelled by user');
                        }
                    }
                };

                console.log('Razorpay options:', options);

                try {
                    var rzp = new Razorpay(options);
                    rzp.on('payment.failed', function (response) {
                        console.error('Payment failed:', response.error);
                        showResult('Payment failed: ' + response.error.description);
                    });
                    rzp.open();
                } catch (error) {
                    console.error('Error opening Razorpay:', error);
                    showResult('Error opening Razorpay: ' + error.message);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                showResult('Error: ' + error.message);
            });
        });

        function verifyPayment(response) {
            showResult('Verifying payment...');
            console.log('Verifying payment:', response);

            fetch('/razorpay/test/verify_test_payment/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({
                    razorpay_payment_id: response.razorpay_payment_id,
                    razorpay_order_id: response.razorpay_order_id,
                    razorpay_signature: response.razorpay_signature
                })
            })
            .then(response => {
                console.log('Verification response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Verification data:', data);

                if (data.verified) {
                    showResult('Payment verified successfully!\n\n' + JSON.stringify(data, null, 2));
                } else {
                    showResult('Payment verification failed!\n\n' + JSON.stringify(data, null, 2));
                }
            })
            .catch(error => {
                console.error('Verification error:', error);
                showResult('Verification error: ' + error.message);
            });
        }

        function showResult(message) {
            document.getElementById('payment-details').textContent = message;
            document.getElementById('result').style.display = 'block';
        }
    </script>
</body>
</html>
