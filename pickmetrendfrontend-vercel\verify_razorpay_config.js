#!/usr/bin/env node
/**
 * Verify Razorpay configuration in frontend
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 VERIFYING FRONTEND RAZORPAY CONFIGURATION');
console.log('=' .repeat(60));

// Check environment files
const envFiles = [
  '.env',
  '.env.production',
  '.env.local'
];

console.log('\n📁 CHECKING ENVIRONMENT FILES:');
console.log('=' .repeat(40));

envFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} exists`);
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    lines.forEach(line => {
      if (line.includes('RAZORPAY_KEY_ID')) {
        const keyId = line.split('=')[1];
        if (keyId) {
          if (keyId.startsWith('rzp_live_')) {
            console.log(`   🔑 ${file}: LIVE key (${keyId})`);
          } else if (keyId.startsWith('rzp_test_')) {
            console.log(`   🧪 ${file}: TEST key (${keyId})`);
          } else {
            console.log(`   ❓ ${file}: Unknown key format (${keyId})`);
          }
        }
      }
    });
  } else {
    console.log(`❌ ${file} not found`);
  }
});

// Check render.yaml
console.log('\n🚀 CHECKING RENDER.YAML:');
console.log('=' .repeat(40));

const renderYamlPath = path.join(__dirname, 'render.yaml');
if (fs.existsSync(renderYamlPath)) {
  console.log('✅ render.yaml exists');
  
  const content = fs.readFileSync(renderYamlPath, 'utf8');
  const lines = content.split('\n');
  
  let foundRazorpayKey = false;
  let foundApiUrl = false;
  
  lines.forEach((line, index) => {
    if (line.includes('REACT_APP_RAZORPAY_KEY_ID')) {
      foundRazorpayKey = true;
      const nextLine = lines[index + 1];
      if (nextLine && nextLine.includes('value:')) {
        const keyId = nextLine.split('value:')[1].trim();
        if (keyId.startsWith('rzp_live_')) {
          console.log(`   🔑 render.yaml: LIVE key (${keyId})`);
        } else if (keyId.startsWith('rzp_test_')) {
          console.log(`   🧪 render.yaml: TEST key (${keyId})`);
        } else {
          console.log(`   ❓ render.yaml: Unknown key format (${keyId})`);
        }
      }
    }
    
    if (line.includes('REACT_APP_API_URL')) {
      foundApiUrl = true;
      const nextLine = lines[index + 1];
      if (nextLine && nextLine.includes('value:')) {
        const apiUrl = nextLine.split('value:')[1].trim();
        console.log(`   🌐 render.yaml: API URL (${apiUrl})`);
      }
    }
  });
  
  if (!foundRazorpayKey) {
    console.log('   ❌ REACT_APP_RAZORPAY_KEY_ID not found in render.yaml');
  }
  
  if (!foundApiUrl) {
    console.log('   ❌ REACT_APP_API_URL not found in render.yaml');
  }
} else {
  console.log('❌ render.yaml not found');
}

// Check React components
console.log('\n⚛️ CHECKING REACT COMPONENTS:');
console.log('=' .repeat(40));

const componentsToCheck = [
  'src/components/RazorpayCheckout.tsx',
  'src/pages/RazorpayTest.tsx'
];

componentsToCheck.forEach(componentPath => {
  const fullPath = path.join(__dirname, componentPath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${componentPath} exists`);
    
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Check for hardcoded keys
    const hardcodedTestKey = content.includes('rzp_test_');
    const hardcodedLiveKey = content.includes('rzp_live_');
    const usesEnvVar = content.includes('process.env.REACT_APP_RAZORPAY_KEY_ID');
    const usesBackendKey = content.includes('orderData.razorpay.key_id') || content.includes('response.data.key_id');
    
    if (hardcodedTestKey) {
      console.log(`   ⚠️ ${componentPath}: Contains hardcoded TEST key`);
    }
    
    if (hardcodedLiveKey) {
      console.log(`   ⚠️ ${componentPath}: Contains hardcoded LIVE key`);
    }
    
    if (usesEnvVar) {
      console.log(`   ✅ ${componentPath}: Uses environment variable`);
    }
    
    if (usesBackendKey) {
      console.log(`   ✅ ${componentPath}: Uses backend-provided key`);
    }
    
    if (!usesEnvVar && !usesBackendKey && !hardcodedTestKey && !hardcodedLiveKey) {
      console.log(`   ❓ ${componentPath}: No Razorpay key usage found`);
    }
  } else {
    console.log(`❌ ${componentPath} not found`);
  }
});

// Check package.json for build scripts
console.log('\n📦 CHECKING PACKAGE.JSON:');
console.log('=' .repeat(40));

const packageJsonPath = path.join(__dirname, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  console.log('✅ package.json exists');
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  if (packageJson.scripts && packageJson.scripts.build) {
    console.log(`   ✅ Build script: ${packageJson.scripts.build}`);
  } else {
    console.log('   ❌ No build script found');
  }
  
  if (packageJson.dependencies && packageJson.dependencies.react) {
    console.log(`   ✅ React version: ${packageJson.dependencies.react}`);
  }
} else {
  console.log('❌ package.json not found');
}

// Summary and recommendations
console.log('\n📊 CONFIGURATION SUMMARY:');
console.log('=' .repeat(40));

console.log('\n🎯 RECOMMENDATIONS:');
console.log('1. For DEVELOPMENT: Use .env with TEST keys');
console.log('2. For PRODUCTION: Use .env.production with LIVE keys');
console.log('3. For RENDER DEPLOYMENT: Use render.yaml with LIVE keys');
console.log('4. Components should use backend-provided keys when possible');

console.log('\n🚀 DEPLOYMENT CHECKLIST:');
console.log('✅ .env.production has live keys');
console.log('✅ render.yaml has live keys');
console.log('✅ Components use dynamic key detection');
console.log('✅ No hardcoded keys in components');

console.log('\n🔧 NEXT STEPS:');
console.log('1. Commit and push changes to GitHub');
console.log('2. Deploy to Render');
console.log('3. Verify environment variables in Render dashboard');
console.log('4. Test payment flow with live keys');

console.log('\n' + '=' .repeat(60));
console.log('🎉 VERIFICATION COMPLETE');
console.log('=' .repeat(60));
