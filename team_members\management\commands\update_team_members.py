from django.core.management.base import BaseCommand
from team_members.models import TeamMember


class Command(BaseCommand):
    help = 'Update team members for PickMeTrend'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear all existing team members before adding new ones',
        )

    def handle(self, *args, **options):
        if options['clear']:
            # Clear existing team members
            deleted_count = TeamMember.objects.count()
            TeamMember.objects.all().delete()
            self.stdout.write(
                self.style.WARNING(f'Cleared {deleted_count} existing team members')
            )

        # Define your team members here
        # Update this list with your actual team member information
        team_members = [
            {
                'name': 'Dr. <PERSON>',
                'role': 'Founder & CEO',
                'order': 1,
                'linkedin_url': '',  # Add LinkedIn URL if available
            },
            {
                'name': '<PERSON><PERSON><PERSON>',
                'role': 'Chief Product Officer',
                'order': 2,
                'linkedin_url': '',  # Add LinkedIn URL if available
            },
        ]

        # Add or update team members
        for member_data in team_members:
            member, created = TeamMember.objects.update_or_create(
                name=member_data['name'],
                defaults={
                    'role': member_data['role'],
                    'order': member_data['order'],
                    'linkedin_url': member_data.get('linkedin_url', ''),
                }
            )
            
            action = 'Created' if created else 'Updated'
            self.stdout.write(
                self.style.SUCCESS(
                    f'{action} team member: {member.name} - {member.role}'
                )
            )

        # Display final team members
        self.stdout.write('\n' + self.style.SUCCESS('Current team members:'))
        for member in TeamMember.objects.all().order_by('order'):
            self.stdout.write(f'  {member.order}. {member.name} - {member.role}')
            if member.linkedin_url:
                self.stdout.write(f'     LinkedIn: {member.linkedin_url}')

        self.stdout.write(
            '\n' + self.style.SUCCESS('Team members updated successfully!')
        )
        self.stdout.write(
            self.style.WARNING(
                'Note: You can add photos through the Django admin interface at /admin/team_members/teammember/'
            )
        )
