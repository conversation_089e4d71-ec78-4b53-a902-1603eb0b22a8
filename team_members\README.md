# Team Members App

This Django app allows you to manage team members through the Django admin panel and display them on your website.

## Features

- Upload and manage team member profiles
- Display team members on a dedicated page
- Add LinkedIn profile links
- Order team members by priority
- Context processor to make team members available in all templates

## Installation

1. Add `'team_members'` to your `INSTALLED_APPS` in `settings.py`:

```python
INSTALLED_APPS = [
    # ...
    'team_members',
    # ...
]
```

2. Add the context processor to your `TEMPLATES` setting:

```python
TEMPLATES = [
    {
        # ...
        'OPTIONS': {
            'context_processors': [
                # ...
                'team_members.context_processors.team_members',
            ],
        },
    },
]
```

3. Make sure your `MEDIA_URL` and `MEDIA_ROOT` are configured:

```python
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
```

4. Configure your URLs to serve media files in development:

```python
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Your URL patterns...
    path('team/', include('team_members.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

5. Run migrations:

```bash
python manage.py migrate team_members
```

## Usage

### Admin Interface

1. Go to the Django admin panel (`/admin/`)
2. Navigate to "Team Members"
3. Add team members with their name, role, photo, and LinkedIn URL
4. Set the display order to control the order in which they appear

### In Templates

The team members are automatically available in all templates through the context processor:

```html
{% if team_members %}
    <div class="team-grid">
        {% for member in team_members %}
            <div class="team-member">
                {% if member.photo %}
                    <img src="{{ member.photo.url }}" alt="{{ member.name }}">
                {% endif %}
                <h3>{{ member.name }}</h3>
                <p>{{ member.role }}</p>
                {% if member.linkedin_url %}
                    <a href="{{ member.linkedin_url }}" target="_blank">LinkedIn</a>
                {% endif %}
            </div>
        {% endfor %}
    </div>
{% endif %}
```

### Team Page

A dedicated team page is available at `/team/` which displays all team members.

## Image Requirements

- Maximum file size: 2MB
- Maximum dimensions: 1200x1200 pixels
- Photos are stored in `team/photos/` directory within your media root
- Photos are automatically renamed based on the team member's name

## Notes

- Team members are ordered by the `order` field (lower numbers first) and then by name
- LinkedIn URLs are automatically prefixed with `https://` if not already present
