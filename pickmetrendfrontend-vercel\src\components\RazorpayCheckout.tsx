import React, { useEffect, useState } from 'react';

declare global {
  interface Window {
    Razorpay: any;
  }
}

interface RazorpayCheckoutProps {
  orderData: {
    id: string;
    razorpay: {
      order_id: string;
      amount: number;
      currency: string;
      key_id: string;
    };
    full_name: string;
    email: string;
    phone: string;
  };
  onSuccess: (paymentData: any) => void;
  onError: (error: any) => void;
}

const RazorpayCheckout: React.FC<RazorpayCheckoutProps> = ({
  orderData,
  onSuccess,
  onError
}) => {
  const [scriptLoaded, setScriptLoaded] = useState<boolean>(false);
  const [scriptError, setScriptError] = useState<boolean>(false);

  // Load Razorpay script if not already loaded
  useEffect(() => {
    // Check if Razorpay is already loaded
    if (window.Razorpay) {
      console.log('Razorpay is already loaded');
      setScriptLoaded(true);
      return;
    }

    console.log('Loading Razorpay script...');

    // Create script element
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;

    // Set up event listeners
    script.onload = () => {
      console.log('Razorpay script loaded successfully');
      setScriptLoaded(true);
      setScriptError(false);
    };

    script.onerror = () => {
      console.error('Failed to load Razorpay script');
      setScriptError(true);
      setScriptLoaded(false);
      onError(new Error('Failed to load Razorpay SDK'));
    };

    // Add script to document
    document.body.appendChild(script);

    // Clean up
    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, [onError]);

  // Initialize Razorpay when script is loaded and order data is available
  useEffect(() => {
    if (scriptLoaded && orderData && !scriptError) {
      console.log('Razorpay script loaded and order data available');
      initializeRazorpay();
    }
  }, [scriptLoaded, orderData, scriptError]);

  const initializeRazorpay = () => {
    console.log('Initializing Razorpay with order data:', orderData);

    // Check if Razorpay is available
    if (!window.Razorpay) {
      console.error('Razorpay is not available');
      onError(new Error('Razorpay SDK is not available. Please refresh the page and try again.'));
      return;
    }

    if (!orderData?.razorpay) {
      console.error('Razorpay order data is missing:', orderData);
      onError(new Error('Razorpay order data is missing'));
      return;
    }

    // Validate required fields
    if (!orderData.razorpay.key_id) {
      console.error('Razorpay key_id is missing');
      onError(new Error('Razorpay key_id is missing'));
      return;
    }

    if (!orderData.razorpay.order_id) {
      console.error('Razorpay order_id is missing');
      onError(new Error('Razorpay order_id is missing'));
      return;
    }

    // Ensure amount is a number and convert to paise if needed
    let amount = orderData.razorpay.amount;
    if (typeof amount === 'string') {
      amount = parseFloat(amount);
    }

    // If amount is less than 100, it's likely in rupees and needs to be converted to paise
    if (amount < 100) {
      amount = amount * 100;
      console.log('Amount converted to paise:', amount);
    }

    const options = {
      key: orderData.razorpay.key_id,
      amount: amount,
      currency: orderData.razorpay.currency || 'INR',
      name: 'PickMeTrend',
      description: 'Custom T-Shirt Order',
      order_id: orderData.razorpay.order_id,
      prefill: {
        name: orderData.full_name || '',
        email: orderData.email || '',
        contact: orderData.phone || ''
      },
      notes: {
        order_id: orderData.id
      },
      theme: {
        color: '#3399cc'
      },
      handler: function (response: any) {
        // Handle successful payment
        console.log('Payment successful:', response);
        onSuccess({
          razorpay_payment_id: response.razorpay_payment_id,
          razorpay_order_id: response.razorpay_order_id,
          razorpay_signature: response.razorpay_signature
        });
      },
      modal: {
        ondismiss: function () {
          console.log('Payment modal dismissed by user');
          onError(new Error('Payment cancelled by user'));
        }
      }
    };

    console.log('Razorpay options:', options);

    try {
      console.log('Creating Razorpay instance and opening payment form');
      const razorpay = new window.Razorpay(options);

      // Add event listeners for payment failures
      razorpay.on('payment.failed', function (response: any) {
        console.error('Payment failed:', response.error);
        onError(new Error(`Payment failed: ${response.error.description}`));
      });

      // Open the payment form
      razorpay.open();

      console.log('Razorpay payment form opened');
    } catch (error) {
      console.error('Error opening Razorpay:', error);
      onError(error instanceof Error ? error : new Error('Unknown error opening Razorpay'));
    }
  };

  return (
    <div className="razorpay-container">
      {scriptError ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p className="font-bold">Error loading Razorpay</p>
          <p>Please refresh the page and try again. If the problem persists, try a different payment method.</p>
        </div>
      ) : !scriptLoaded ? (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600 mb-2"></div>
          <p>Loading payment gateway...</p>
        </div>
      ) : (
        <>
          <p className="mb-4 text-gray-700">
            Click the button below to complete your payment securely via Razorpay.
          </p>
          {orderData.razorpay.key_id?.startsWith('rzp_test_') ? (
            <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-4">
              <p className="font-bold">Test Mode</p>
              <p>This is a test payment. No actual charges will be made.</p>
            </div>
          ) : (
            <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4">
              <p className="font-bold">Live Mode</p>
              <p>This is a live payment. Real charges will be made to your account.</p>
            </div>
          )}
          <button
            className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-4 rounded w-full flex items-center justify-center"
            onClick={initializeRazorpay}
          >
            <span className="mr-2">Pay with Razorpay</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
          </button>
          {orderData.razorpay.key_id?.startsWith('rzp_test_') && (
            <div className="mt-4 text-sm text-gray-600">
              <p>For testing, use these credentials:</p>
              <ul className="list-disc pl-5 mt-2">
                <li>Card: 4111 1111 1111 1111</li>
                <li>Expiry: Any future date</li>
                <li>CVV: Any 3 digits</li>
                <li>Name: Any name</li>
              </ul>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default RazorpayCheckout;
