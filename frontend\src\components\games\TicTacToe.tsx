import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';
import { ticTacToeService } from '../../services/ticTacToeService';

type Player = 'X' | 'O' | null;
type Board = Player[];
type GameStatus = 'playing' | 'won' | 'draw' | 'lost';
type Difficulty = 'easy' | 'medium' | 'hard';

interface GameStats {
  wins: number;
  losses: number;
  draws: number;
  tokensEarned: number;
  winStreak: number;
  gamesPlayed: number;
}

const TicTacToe: React.FC = () => {
  const { user } = useAuth();
  const { wallet, fetchWallet } = useWallet();
  const [board, setBoard] = useState<Board>(Array(9).fill(null));
  const [isPlayerTurn, setIsPlayerTurn] = useState(true);
  const [gameStatus, setGameStatus] = useState<GameStatus>('playing');
  const [difficulty, setDifficulty] = useState<Difficulty>('medium');
  const [tokensEarned, setTokensEarned] = useState(0);
  const [gameStats, setGameStats] = useState<GameStats>({
    wins: 0,
    losses: 0,
    draws: 0,
    tokensEarned: 0,
    winStreak: 0,
    gamesPlayed: 0
  });
  const [showCelebration, setShowCelebration] = useState(false);
  const [winningLine, setWinningLine] = useState<number[]>([]);
  const [currentGameId, setCurrentGameId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check for winner
  const checkWinner = useCallback((board: Board): { winner: Player; line: number[] } => {
    const lines = [
      [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
      [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
      [0, 4, 8], [2, 4, 6] // diagonals
    ];

    for (const line of lines) {
      const [a, b, c] = line;
      if (board[a] && board[a] === board[b] && board[a] === board[c]) {
        return { winner: board[a], line };
      }
    }
    return { winner: null, line: [] };
  }, []);

  // AI Move Logic
  const getAIMove = useCallback((board: Board, difficulty: Difficulty): number => {
    const availableMoves = board.map((cell, index) => cell === null ? index : null).filter(val => val !== null) as number[];
    
    if (availableMoves.length === 0) return -1;

    // Easy: Random move
    if (difficulty === 'easy') {
      return availableMoves[Math.floor(Math.random() * availableMoves.length)];
    }

    // Medium: 80% optimal, 20% random (increased optimal play)
    if (difficulty === 'medium' && Math.random() < 0.2) {
      return availableMoves[Math.floor(Math.random() * availableMoves.length)];
    }

    // Strategic AI: First try to win, then block player, then use minimax

    // 1. Check if AI can win in next move
    for (const move of availableMoves) {
      const testBoard = [...board];
      testBoard[move] = 'O';
      const { winner } = checkWinner(testBoard);
      if (winner === 'O') {
        console.log(`🎯 AI found winning move at position ${move}`);
        return move;
      }
    }

    // 2. Check if AI needs to block player from winning
    for (const move of availableMoves) {
      const testBoard = [...board];
      testBoard[move] = 'X';
      const { winner } = checkWinner(testBoard);
      if (winner === 'X') {
        console.log(`🛡️ AI blocking player win at position ${move}`);
        return move;
      }
    }

    // 3. Use minimax for strategic play
    const minimax = (board: Board, depth: number, isMaximizing: boolean): number => {
      const { winner } = checkWinner(board);

      if (winner === 'O') return 10 - depth;
      if (winner === 'X') return depth - 10;
      if (board.every(cell => cell !== null)) return 0;

      if (isMaximizing) {
        let bestScore = -Infinity;
        for (let i = 0; i < 9; i++) {
          if (board[i] === null) {
            board[i] = 'O';
            const score = minimax(board, depth + 1, false);
            board[i] = null;
            bestScore = Math.max(score, bestScore);
          }
        }
        return bestScore;
      } else {
        let bestScore = Infinity;
        for (let i = 0; i < 9; i++) {
          if (board[i] === null) {
            board[i] = 'X';
            const score = minimax(board, depth + 1, true);
            board[i] = null;
            bestScore = Math.min(score, bestScore);
          }
        }
        return bestScore;
      }
    };

    let bestScore = -Infinity;
    let bestMove = availableMoves[0];

    for (const move of availableMoves) {
      board[move] = 'O';
      const score = minimax(board, 0, false);
      board[move] = null;

      if (score > bestScore) {
        bestScore = score;
        bestMove = move;
      }
    }

    console.log(`🤖 AI chose strategic move at position ${bestMove}`);
    return bestMove;
  }, [checkWinner]);

  // Complete game with backend API
  const completeGame = async (result: 'win' | 'loss' | 'draw') => {
    if (!currentGameId) return;

    try {
      setLoading(true);
      console.log(`🎮 Completing game with result: ${result}`);
      const response = await ticTacToeService.completeGame(
        currentGameId,
        result
      );

      if (response.success) {
        console.log(`💰 Tokens earned: ${response.tokens_earned}`);
        setTokensEarned(response.tokens_earned);

        // Update local stats
        setGameStats(prev => ({
          ...prev,
          wins: result === 'win' ? prev.wins + 1 : prev.wins,
          losses: result === 'loss' ? prev.losses + 1 : prev.losses,
          draws: result === 'draw' ? prev.draws + 1 : prev.draws,
          winStreak: result === 'win' ? prev.winStreak + 1 : 0,
          tokensEarned: prev.tokensEarned + response.tokens_earned,
          gamesPlayed: prev.gamesPlayed + 1
        }));

        // Refresh wallet to show updated balance
        await fetchWallet();

        setError(null);
      } else {
        setError(response.error || 'Failed to complete game');
      }
    } catch (err) {
      console.error('Error completing game:', err);
      setError('Failed to complete game');
    } finally {
      setLoading(false);
    }
  };

  // Handle player move
  const handleCellClick = (index: number) => {
    if (board[index] || !isPlayerTurn || gameStatus !== 'playing') return;

    const newBoard = [...board];
    newBoard[index] = 'X';
    setBoard(newBoard);
    setIsPlayerTurn(false);

    // Check if player won
    const { winner, line } = checkWinner(newBoard);
    if (winner === 'X') {
      setGameStatus('won');
      setWinningLine(line);
      setShowCelebration(true);
      setTimeout(() => setShowCelebration(false), 3000);

      // Complete game with backend
      completeGame('win');
      return;
    }

    // Check for draw
    if (newBoard.every(cell => cell !== null)) {
      setGameStatus('draw');
      completeGame('draw');
      return;
    }

    // AI turn
    setTimeout(() => {
      const aiMove = getAIMove(newBoard, difficulty);
      if (aiMove !== -1) {
        const aiBoard = [...newBoard];
        aiBoard[aiMove] = 'O';
        setBoard(aiBoard);

        // Check if AI won
        const { winner: aiWinner, line: aiLine } = checkWinner(aiBoard);
        if (aiWinner === 'O') {
          console.log('🤖 AI Won! Setting game status to lost and completing game with loss');
          setGameStatus('lost');
          setWinningLine(aiLine);
          completeGame('loss');
          return; // Important: Exit early to prevent draw check
        }

        // Only check for draw if AI didn't win
        if (aiBoard.every(cell => cell !== null)) {
          setGameStatus('draw');
          completeGame('draw');
          return; // Exit early to prevent setting player turn
        }
      }

      // Only set player turn if game is still ongoing
      setIsPlayerTurn(true);
    }, 500);
  };

  // Start new game
  const startNewGame = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await ticTacToeService.startGame();

      if (response.success) {
        setCurrentGameId(response.game_id);
        setBoard(Array(9).fill(null));
        setIsPlayerTurn(true);
        setGameStatus('playing');
        setTokensEarned(0);
        setWinningLine([]);
        setShowCelebration(false);
        setError(null);
      } else {
        setError(response.error || 'Failed to start game');
        setCurrentGameId(null);
      }
    } catch (err) {
      console.error('Error starting game:', err);
      setError('Failed to start game');
      setCurrentGameId(null);
    } finally {
      setLoading(false);
    }
  };

  // Reset game (alias for startNewGame)
  const resetGame = startNewGame;

  // Load stats from backend and start first game
  useEffect(() => {
    const loadStatsAndStartGame = async () => {
      try {
        // Load stats from backend
        const statsResponse = await ticTacToeService.getStats();
        if (statsResponse.success) {
          setGameStats({
            wins: statsResponse.stats.wins,
            losses: statsResponse.stats.losses,
            draws: statsResponse.stats.draws,
            tokensEarned: statsResponse.stats.total_tokens_earned,
            winStreak: 0, // Win streak is calculated locally
            gamesPlayed: statsResponse.stats.total_games
          });
        }

        // Start first game
        await startNewGame();
      } catch (err) {
        console.error('Error loading stats and starting game:', err);
        setError('Failed to initialize game');
      }
    };

    if (user) {
      loadStatsAndStartGame();
    }
  }, [user]); // Only run when user changes

  const getStatusMessage = () => {
    switch (gameStatus) {
      case 'won':
        return `🎉 You Won! +${tokensEarned} tokens (Net: +${tokensEarned - 2} tokens)`;
      case 'lost':
        return `😔 AI Won! Only participation fee lost (Net: -2 tokens)`;
      case 'draw':
        return `🤝 It's a Draw! +${tokensEarned} tokens (Net: 0 tokens - break even)`;
      default:
        return isPlayerTurn ? "Your turn (X)" : "AI thinking...";
    }
  };

  const getCellContent = (index: number) => {
    const value = board[index];
    if (!value) return '';
    
    const isWinningCell = winningLine.includes(index);
    const baseClasses = "text-4xl font-bold transition-all duration-300";
    
    if (value === 'X') {
      return (
        <span className={`${baseClasses} ${isWinningCell ? 'text-green-500 animate-pulse' : 'text-purple-600'}`}>
          ✕
        </span>
      );
    } else {
      return (
        <span className={`${baseClasses} ${isWinningCell ? 'text-red-500 animate-pulse' : 'text-blue-600'}`}>
          ⭕
        </span>
      );
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
      {/* Celebration Effect */}
      {showCelebration && (
        <div className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center">
          <div className="text-6xl animate-bounce">🎉🎊🏆🎊🎉</div>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <span className="text-3xl mr-4">⭕</span>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Tic Tac Toe</h2>
            <p className="text-gray-600">Beat the AI to earn tokens!</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-purple-600">+{tokensEarned} 🪙</div>
          <div className="text-sm text-gray-500">This Game</div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center text-red-700">
            <span className="mr-2">⚠️</span>
            <span className="font-medium">{error}</span>
          </div>
        </div>
      )}

      {/* Game Controls */}
      <div className="mb-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="px-3 py-2 bg-yellow-50 border border-yellow-200 rounded-lg">
              <span className="text-sm font-medium text-yellow-700">🟡 Medium Difficulty</span>
            </div>
          </div>

          <button
            onClick={resetGame}
            disabled={loading}
            className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                Starting...
              </div>
            ) : (
              <>🔄 New Game</>
            )}
          </button>
        </div>
      </div>

      {/* Status Message */}
      <div className="text-center mb-6">
        <div className={`text-lg font-semibold p-3 rounded-lg ${
          gameStatus === 'won' ? 'bg-green-100 text-green-800' :
          gameStatus === 'lost' ? 'bg-red-100 text-red-800' :
          gameStatus === 'draw' ? 'bg-yellow-100 text-yellow-800' :
          'bg-blue-100 text-blue-800'
        }`}>
          {getStatusMessage()}
        </div>
      </div>

      {/* Game Board */}
      <div className="mb-8">
        <div className="grid grid-cols-3 gap-2 max-w-xs mx-auto">
          {board.map((cell, index) => (
            <button
              key={index}
              onClick={() => handleCellClick(index)}
              disabled={loading || !isPlayerTurn || gameStatus !== 'playing' || cell !== null}
              className={`
                aspect-square bg-gradient-to-br from-gray-50 to-gray-100
                border-2 border-gray-300 rounded-xl
                flex items-center justify-center
                hover:from-purple-50 hover:to-blue-50 hover:border-purple-300
                transition-all duration-200 transform hover:scale-105
                disabled:cursor-not-allowed disabled:hover:scale-100 disabled:opacity-50
                ${winningLine.includes(index) ? 'bg-gradient-to-br from-green-100 to-green-200 border-green-400' : ''}
                ${cell ? 'cursor-not-allowed' : 'cursor-pointer'}
              `}
            >
              {getCellContent(index)}
            </button>
          ))}
        </div>
      </div>

      {/* Game Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-green-50 to-emerald-100 p-4 rounded-xl text-center">
          <div className="text-2xl font-bold text-green-600">{gameStats.wins}</div>
          <div className="text-sm text-green-700 font-medium">Wins</div>
        </div>

        <div className="bg-gradient-to-br from-red-50 to-rose-100 p-4 rounded-xl text-center">
          <div className="text-2xl font-bold text-red-600">{gameStats.losses}</div>
          <div className="text-sm text-red-700 font-medium">Losses</div>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-amber-100 p-4 rounded-xl text-center">
          <div className="text-2xl font-bold text-yellow-600">{gameStats.draws}</div>
          <div className="text-sm text-yellow-700 font-medium">Draws</div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-blue-100 p-4 rounded-xl text-center">
          <div className="text-2xl font-bold text-purple-600">{gameStats.tokensEarned}</div>
          <div className="text-sm text-purple-700 font-medium">Total Tokens</div>
        </div>
      </div>

      {/* Win Streak & Additional Stats */}
      {gameStats.winStreak > 0 && (
        <div className="mt-4 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-100 to-red-100 rounded-full">
            <span className="text-orange-600 font-bold mr-2">🔥</span>
            <span className="text-orange-800 font-semibold">
              {gameStats.winStreak} Win Streak!
            </span>
          </div>
        </div>
      )}

      {/* Game Rules */}
      <div className="mt-6 p-4 bg-gray-50 rounded-xl">
        <h3 className="font-semibold text-gray-900 mb-2">🏆 Token Rules:</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <div className="flex items-center">
            <span className="text-blue-600 mr-2">🎮</span>
            <span>Entry Fee: 2 tokens (deducted at start)</span>
          </div>
          <div className="flex items-center">
            <span className="text-green-600 mr-2">🏆</span>
            <span>Win: +5 tokens (Net: +3 tokens)</span>
          </div>
          <div className="flex items-center">
            <span className="text-yellow-600 mr-2">🤝</span>
            <span>Draw: +2 tokens (Net: 0 tokens - break even)</span>
          </div>
          <div className="flex items-center">
            <span className="text-red-600 mr-2">💔</span>
            <span>Loss: No additional penalty (Net: -2 tokens)</span>
          </div>
          <div className="mt-2 p-2 bg-blue-50 rounded text-blue-800 text-xs">
            ℹ️ <strong>Medium Mode:</strong> AI plays strategically (80% optimal) - tries to win and block you!
          </div>
        </div>
      </div>
    </div>
  );
};

export default TicTacToe;
