# PickMeTrend Backend

A comprehensive Django backend for the PickMeTrend dropshipping platform with integrated payment processing, order management, and gaming features.

## Features

- **User Authentication**: JWT-based authentication with Djoser
- **Product Management**: Complete product catalog with Printify integration
- **Order Processing**: Order management with Razorpay payment gateway
- **Gaming System**: Interactive games for user engagement
- **Wallet System**: Digital wallet for users
- **Order Tracking**: Real-time order tracking system
- **Customer Communication**: Email templates and communication tools
- **Admin Dashboard**: Comprehensive admin interface
- **API**: RESTful API for frontend integration

## Quick Start

### Prerequisites

- Python 3.11+
- pip
- Git

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd pickmetrendofficial-render
   ```

2. **Create and activate virtual environment**
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate

   # macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   # Copy the example environment file
   cp env.example .env.local
   
   # Edit .env.local with your settings
   # At minimum, set a SECRET_KEY
   ```

5. **Run the application**
   ```bash
   # Option 1: Use the automated script
   python run_local.py
   
   # Option 2: Manual setup
   python manage.py migrate
   python manage.py collectstatic --noinput
   python manage.py runserver
   ```

6. **Access the application**
   - Main site: http://localhost:8000
   - Admin interface: http://localhost:8000/admin/
   - API root: http://localhost:8000/api/

### Environment Variables

Copy `env.example` to `.env.local` and configure the following variables:

```bash
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for local development)
USE_SQLITE=True

# Cloudinary (for media storage)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Razorpay (payment gateway)
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret

# SendGrid (for emails)
SENDGRID_API_KEY=your-sendgrid-api-key
```

## Railway Deployment

### Prerequisites

1. **Railway Account**: Sign up at [railway.app](https://railway.app)
2. **Railway CLI** (optional): Install for local development
   ```bash
   npm install -g @railway/cli
   ```

### Deployment Steps

1. **Connect your repository**
   - Go to [railway.app](https://railway.app)
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose your repository

2. **Add PostgreSQL Database**
   - In your Railway project, click "New"
   - Select "Database" → "PostgreSQL"
   - Railway will automatically set the `DATABASE_URL` environment variable

3. **Configure Environment Variables**
   - Go to your service settings
   - Add the following environment variables:
   ```bash
   SECRET_KEY=your-production-secret-key
   DEBUG=False
   ALLOWED_HOSTS=your-app-name.railway.app
   USE_CLOUDINARY=True
   CLOUDINARY_CLOUD_NAME=your-cloud-name
   CLOUDINARY_API_KEY=your-api-key
   CLOUDINARY_API_SECRET=your-api-secret
   RAZORPAY_KEY_ID=your-razorpay-key-id
   RAZORPAY_KEY_SECRET=your-razorpay-key-secret
   SENDGRID_API_KEY=your-sendgrid-api-key
   ```

4. **Deploy**
   - Railway will automatically detect the Django project
   - It will use the `Procfile` and `railway.json` for configuration
   - The deployment will run migrations and collect static files automatically

5. **Post-deployment setup**
   - Access your deployed app
   - Go to `/admin/` to create a superuser
   - Configure your domain settings

### Railway Configuration Files

The project includes these Railway-specific files:

- **`railway.json`**: Railway deployment configuration
- **`Procfile`**: Process definition for Railway
- **`runtime.txt`**: Python version specification

## API Documentation

### Authentication

The API uses JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Main Endpoints

- **Products**: `/api/products/`
- **Orders**: `/api/orders/`
- **Authentication**: `/api/auth/`
- **Users**: `/api/accounts/`
- **Gaming**: `/api/gaming/`
- **Wallet**: `/api/wallet/`

### Health Check

- **Health Check**: `/health/` - Returns application status

## Project Structure

```
pickmetrendofficial-render/
├── accounts/                 # User authentication
├── products/                 # Product management
├── orders/                   # Order processing
├── gaming/                   # Gaming system
├── wallet/                   # Digital wallet
├── printify/                 # Printify integration
├── razorpay_settings/        # Payment gateway
├── order_tracking/           # Order tracking
├── customer_communication/   # Email templates
├── service_control/          # Admin dashboard
├── dropshipping_backend/     # Main Django project
├── templates/                # HTML templates
├── media/                    # Media files
├── staticfiles/              # Static files
├── requirements.txt          # Python dependencies
├── railway.json             # Railway configuration
├── Procfile                 # Railway process file
├── runtime.txt              # Python version
├── env.example              # Environment variables example
└── run_local.py             # Local development script
```

## Development

### Running Tests

```bash
python manage.py test
```

### Creating Migrations

```bash
python manage.py makemigrations
python manage.py migrate
```

### Creating Superuser

```bash
python manage.py createsuperuser
```

### Static Files

```bash
python manage.py collectstatic --noinput
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure PostgreSQL is running (for production)
   - Check database credentials in environment variables

2. **Static Files Not Loading**
   - Run `python manage.py collectstatic --noinput`
   - Check `STATIC_ROOT` and `STATIC_URL` settings

3. **Media Files Not Accessible**
   - Ensure Cloudinary is configured (for production)
   - Check `MEDIA_ROOT` and `MEDIA_URL` settings

4. **Import Errors**
   - Ensure virtual environment is activated
   - Install all requirements: `pip install -r requirements.txt`

### Railway Specific

1. **Build Failures**
   - Check Railway logs for specific error messages
   - Ensure all required files are committed to Git

2. **Environment Variables**
   - Verify all required environment variables are set in Railway dashboard
   - Check that `DATABASE_URL` is automatically set by Railway

3. **Health Check Failures**
   - Ensure the `/health/` endpoint is accessible
   - Check application logs for startup errors

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Railway deployment logs
3. Check Django application logs
4. Create an issue in the repository

## License

This project is proprietary software for PickMeTrend. 