#!/usr/bin/env python3
"""
Simple script to run the Django development server locally.
This script will:
1. Check if virtual environment is activated
2. Install requirements if needed
3. Run migrations
4. Start the development server
"""

import os
import sys
import subprocess
import django
from pathlib import Path

def run_command(command, check=True):
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error running command: {command}")
        print(f"Error: {result.stderr}")
        sys.exit(1)
    return result

def main():
    # Check if we're in a virtual environment
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected!")
        print("Please activate your virtual environment first:")
        print("  Windows: venv\\Scripts\\activate")
        print("  macOS/Linux: source venv/bin/activate")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)

    # Check if requirements are installed
    try:
        import django
        print("✅ Django is installed")
    except ImportError:
        print("Installing requirements...")
        run_command("pip install -r requirements.txt")
    
    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
    
    # Run migrations
    print("Running migrations...")
    run_command("python manage.py migrate")
    
    # Collect static files
    print("Collecting static files...")
    run_command("python manage.py collectstatic --noinput")
    
    # Create superuser if it doesn't exist
    try:
        from django.contrib.auth.models import User
        if not User.objects.filter(is_superuser=True).exists():
            print("No superuser found. Creating one...")
            run_command("python manage.py createsuperuser", check=False)
    except Exception as e:
        print(f"Could not check/create superuser: {e}")
    
    # Start the development server
    print("Starting development server...")
    print("Server will be available at: http://localhost:8000")
    print("Admin interface: http://localhost:8000/admin/")
    print("API root: http://localhost:8000/api/")
    print("Press Ctrl+C to stop the server")
    
    # Run the development server
    subprocess.run("python manage.py runserver", shell=True)

if __name__ == "__main__":
    main() 