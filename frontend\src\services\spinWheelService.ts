import { api } from './api';

// Types for Spin Wheel
export interface SpinWheelReward {
  id: string;
  name: string;
  type: 'tokens' | 'scratch_card' | 'discount' | 'bonus_spin';
  value: number;
  probability: number;
  extra_data?: {
    color?: string;
    icon?: string;
  };
}

export interface SpinWheelStatus {
  success: boolean;
  can_spin: boolean;
  last_spin?: string;
  next_spin_time?: string;
  cooldown_hours: number;
  wheel_segments: number;
  animation_duration: number;
  current_balance: number;
  available_rewards: SpinWheelReward[];
  total_spins_today: number;
  error?: string;
}

export interface SpinResult {
  success: boolean;
  spin_id?: string;
  reward?: {
    id: string;
    name: string;
    type: string;
    value: number;
    description: string;
  };
  wheel_position?: number;
  animation_duration?: number;
  result?: {
    success: boolean;
    type: string;
    amount?: number;
    new_balance?: number;
    description: string;
    requires_action?: boolean;
    scratch_card_id?: string;
  };
  next_spin_time?: string;
  error?: string;
}

export interface ScratchCardReveal {
  success: boolean;
  scratch_card_id?: string;
  reward_result?: {
    success: boolean;
    reward_type: string;
    amount: number;
    message: string;
  };
  hidden_reward?: {
    type: string;
    amount: number;
    message: string;
  };
  new_balance?: number;
  revealed_at?: string;
  error?: string;
}

export interface SpinHistoryItem {
  id: string;
  reward_name: string;
  reward_type: string;
  reward_value: number;
  spin_timestamp: string;
  reward_claimed: boolean;
  has_scratch_card: boolean;
  scratch_card_revealed: boolean;
  scratch_card_reward?: {
    type: string;
    amount: number;
    message: string;
  };
}

export interface SpinHistory {
  success: boolean;
  history: SpinHistoryItem[];
  total_spins: number;
  error?: string;
}

class SpinWheelService {
  private baseUrl = '/api/gaming/spin-wheel';

  /**
   * Get spin wheel status and user eligibility
   */
  async getStatus(): Promise<SpinWheelStatus> {
    try {
      const response = await api.get(`${this.baseUrl}/status/`);
      return response.data;
    } catch (error: any) {
      console.error('Error getting spin wheel status:', error);
      return {
        success: false,
        can_spin: false,
        cooldown_hours: 24,
        wheel_segments: 8,
        animation_duration: 3000,
        current_balance: 0,
        available_rewards: [],
        total_spins_today: 0,
        error: error.response?.data?.error || 'Failed to get spin wheel status'
      };
    }
  }

  /**
   * Perform a spin
   */
  async spin(): Promise<SpinResult> {
    try {
      const response = await api.post(`${this.baseUrl}/spin/`);
      return response.data;
    } catch (error: any) {
      console.error('Error spinning wheel:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to spin wheel'
      };
    }
  }

  /**
   * Reveal a scratch card
   */
  async revealScratchCard(spinId: string): Promise<ScratchCardReveal> {
    try {
      const response = await api.post(`${this.baseUrl}/reveal-scratch/`, {
        spin_id: spinId
      });
      return response.data;
    } catch (error: any) {
      console.error('Error revealing scratch card:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to reveal scratch card'
      };
    }
  }

  /**
   * Get spin history
   */
  async getHistory(limit: number = 10): Promise<SpinHistory> {
    try {
      const response = await api.get(`${this.baseUrl}/history/?limit=${limit}`);
      return response.data;
    } catch (error: any) {
      console.error('Error getting spin history:', error);
      return {
        success: false,
        history: [],
        total_spins: 0,
        error: error.response?.data?.error || 'Failed to get spin history'
      };
    }
  }

  /**
   * Calculate time until next spin
   */
  getTimeUntilNextSpin(nextSpinTime: string): {
    hours: number;
    minutes: number;
    seconds: number;
    totalMs: number;
  } {
    const now = new Date();
    const nextSpin = new Date(nextSpinTime);
    const diff = nextSpin.getTime() - now.getTime();

    if (diff <= 0) {
      return { hours: 0, minutes: 0, seconds: 0, totalMs: 0 };
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return { hours, minutes, seconds, totalMs: diff };
  }

  /**
   * Format time remaining as string
   */
  formatTimeRemaining(nextSpinTime: string): string {
    const { hours, minutes, seconds, totalMs } = this.getTimeUntilNextSpin(nextSpinTime);
    
    if (totalMs <= 0) {
      return 'Available now!';
    }

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s remaining`;
    } else {
      return `${seconds}s remaining`;
    }
  }

  /**
   * Get reward color for UI
   */
  getRewardColor(reward: SpinWheelReward): string {
    return reward.extra_data?.color || this.getDefaultColorForType(reward.type);
  }

  /**
   * Get reward icon for UI
   */
  getRewardIcon(reward: SpinWheelReward): string {
    return reward.extra_data?.icon || this.getDefaultIconForType(reward.type);
  }

  /**
   * Get default color for reward type
   */
  private getDefaultColorForType(type: string): string {
    const colors = {
      tokens: '#FFD700',
      scratch_card: '#96CEB4',
      discount: '#FFEAA7',
      bonus_spin: '#DDA0DD'
    };
    return colors[type as keyof typeof colors] || '#F0F0F0';
  }

  /**
   * Get default icon for reward type
   */
  private getDefaultIconForType(type: string): string {
    const icons = {
      tokens: '🪙',
      scratch_card: '🎫',
      discount: '🏷️',
      bonus_spin: '🎡'
    };
    return icons[type as keyof typeof icons] || '🎁';
  }
}

export const spinWheelService = new SpinWheelService();
export default spinWheelService;
