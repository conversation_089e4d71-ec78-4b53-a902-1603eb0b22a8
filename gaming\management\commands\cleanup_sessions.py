from django.core.management.base import BaseCommand
from gaming.models import GameSession
from django.utils import timezone


class Command(BaseCommand):
    help = 'Clean up abandoned game sessions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--timeout',
            type=int,
            default=30,
            help='Session timeout in minutes (default: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )

    def handle(self, *args, **options):
        timeout_minutes = options['timeout']
        dry_run = options['dry_run']
        
        timeout = timezone.now() - timezone.timedelta(minutes=timeout_minutes)
        abandoned_sessions = GameSession.objects.filter(
            status='active',
            last_activity__lt=timeout
        )
        
        count = abandoned_sessions.count()
        
        if dry_run:
            self.stdout.write(f"Found {count} abandoned sessions that would be cleaned up")
            for session in abandoned_sessions:
                self.stdout.write(f"  - Session {session.id} by {session.user.username}")
            return
        
        if count > 0:
            GameSession.cleanup_abandoned_sessions()
            self.stdout.write(
                self.style.SUCCESS(f"Successfully cleaned up {count} abandoned sessions")
            )
        else:
            self.stdout.write("No abandoned sessions found") 