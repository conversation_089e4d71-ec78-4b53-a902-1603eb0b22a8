"""
Management command to fix Cha<PERSON><PERSON>ield length issues by applying database schema changes manually.
This command can be run when migrations fail due to database connection issues.
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.conf import settings


class Command(BaseCommand):
    help = 'Fix CharField length issues by applying database schema changes manually'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show SQL commands without executing them',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # SQL commands to increase field lengths
        sql_commands = [
            # Products app - Product model
            "ALTER TABLE products_product ALTER COLUMN printify_id TYPE VARCHAR(255);",
            
            # Products app - ProductImage model
            "ALTER TABLE products_productimage ALTER COLUMN alt_text TYPE VARCHAR(255);",
            
            # Products app - ProductVariant model
            "ALTER TABLE products_productvariant ALTER COLUMN variant_id TYPE VARCHAR(255);",
            "ALTER TABLE products_productvariant ALTER COLUMN color TYPE VARCHAR(255);",
            "ALTER TABLE products_productvariant ALTER COLUMN size TYPE VARCHAR(255);",
            
            # Orders app - CartItem model
            "ALTER TABLE orders_cartitem ALTER COLUMN variant_id TYPE VARCHAR(255);",
            
            # Orders app - OrderItem model
            "ALTER TABLE orders_orderitem ALTER COLUMN variant_id TYPE VARCHAR(255);",
            "ALTER TABLE orders_orderitem ALTER COLUMN printify_order_id TYPE VARCHAR(255);",
        ]
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - SQL commands that would be executed:')
            )
            for sql in sql_commands:
                self.stdout.write(f"  {sql}")
            return
        
        try:
            with connection.cursor() as cursor:
                for sql in sql_commands:
                    try:
                        self.stdout.write(f"Executing: {sql}")
                        cursor.execute(sql)
                        self.stdout.write(
                            self.style.SUCCESS(f"✓ Successfully executed: {sql}")
                        )
                    except Exception as e:
                        # Check if the error is because the column doesn't exist or is already the right type
                        error_msg = str(e).lower()
                        if 'does not exist' in error_msg:
                            self.stdout.write(
                                self.style.WARNING(f"⚠ Column doesn't exist (might be new): {sql}")
                            )
                        elif 'already exists' in error_msg or 'no change' in error_msg:
                            self.stdout.write(
                                self.style.WARNING(f"⚠ Column already correct type: {sql}")
                            )
                        else:
                            self.stdout.write(
                                self.style.ERROR(f"✗ Error executing {sql}: {e}")
                            )
                            
            self.stdout.write(
                self.style.SUCCESS(
                    '\n✅ Database schema updates completed! '
                    'The CharField length issues should now be resolved.'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Database connection error: {e}')
            )
            self.stdout.write(
                self.style.WARNING(
                    '\n💡 If you\'re in a production environment, you may need to:'
                    '\n1. Run migrations through your deployment platform'
                    '\n2. Apply the SQL commands manually through your database admin panel'
                    '\n3. Contact your database administrator'
                )
            )
