{% extends 'base.html' %}
{% load static %}

{% block title %}Gaming Hub - PickMeTrend{% endblock %}

{% block extra_css %}
<style>
    .gaming-hub {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 40px 0;
    }
    
    .hub-header {
        text-align: center;
        color: white;
        margin-bottom: 50px;
    }
    
    .hub-header h1 {
        font-size: 3rem;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .hub-header p {
        font-size: 1.2rem;
        opacity: 0.9;
    }
    
    .games-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }
    
    .game-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .game-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
    }
    
    .game-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #4CAF50, #45a049);
    }
    
    .game-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        display: block;
    }
    
    .game-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
    }
    
    .game-description {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }
    
    .game-stats {
        display: flex;
        justify-content: space-around;
        margin-bottom: 25px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .stat {
        text-align: center;
    }
    
    .stat-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: #4CAF50;
        display: block;
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: #666;
    }
    
    .play-button {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        border: none;
        padding: 15px 30px;
        font-size: 1.1rem;
        border-radius: 25px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    }
    
    .play-button:hover {
        background: linear-gradient(135deg, #45a049, #4CAF50);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        text-decoration: none;
        color: white;
    }
    
    .token-cost {
        background: #FFD700;
        color: #333;
        padding: 5px 15px;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: bold;
        margin-bottom: 15px;
        display: inline-block;
    }
    
    .user-dashboard {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 40px;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .dashboard-stat {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
    }
    
    .dashboard-stat-value {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }
    
    .dashboard-stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    @media (max-width: 768px) {
        .hub-header h1 {
            font-size: 2rem;
        }
        
        .games-grid {
            grid-template-columns: 1fr;
            padding: 0 15px;
        }
        
        .game-card {
            padding: 20px;
        }
        
        .dashboard-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="gaming-hub">
    <div class="hub-header">
        <h1>🎮 Gaming Hub</h1>
        <p>Play games, earn tokens, and climb the leaderboards!</p>
    </div>
    
    <div class="user-dashboard">
        <h2 style="text-align: center; color: #333; margin-bottom: 20px;">Your Gaming Dashboard</h2>
        <div class="dashboard-grid">
            <div class="dashboard-stat">
                <span class="dashboard-stat-value">{{ user.wallet.balance|default:"0" }}</span>
                <span class="dashboard-stat-label">🪙 Tokens</span>
            </div>
            <div class="dashboard-stat">
                <span class="dashboard-stat-value" id="totalGames">--</span>
                <span class="dashboard-stat-label">🎯 Games Played</span>
            </div>
            <div class="dashboard-stat">
                <span class="dashboard-stat-value" id="totalWins">--</span>
                <span class="dashboard-stat-label">🏆 Total Wins</span>
            </div>
            <div class="dashboard-stat">
                <span class="dashboard-stat-value" id="winRate">--%</span>
                <span class="dashboard-stat-label">📊 Win Rate</span>
            </div>
        </div>
    </div>
    
    <div class="games-grid">
        <!-- Flappy Bird Game -->
        <div class="game-card">
            <span class="game-icon">🐦</span>
            <h3 class="game-title">Flappy Bird</h3>
            <p class="game-description">
                Navigate through pipes and test your reflexes in this classic arcade game. 
                Pass 10+ pipes to earn bonus tokens!
            </p>
            <div class="token-cost">2 🪙 to play</div>
            <div class="game-stats">
                <div class="stat">
                    <span class="stat-value" id="flappyGames">--</span>
                    <span class="stat-label">Games</span>
                </div>
                <div class="stat">
                    <span class="stat-value" id="flappyHighScore">--</span>
                    <span class="stat-label">High Score</span>
                </div>
                <div class="stat">
                    <span class="stat-value" id="flappyBestPipes">--</span>
                    <span class="stat-label">Best Pipes</span>
                </div>
            </div>
            <a href="{% url 'gaming:flappy_bird_game' %}" class="play-button">Play Now</a>
        </div>
        
        <!-- Coming Soon Games -->
        <div class="game-card" style="opacity: 0.7;">
            <span class="game-icon">🎯</span>
            <h3 class="game-title">Tic Tac Toe</h3>
            <p class="game-description">
                Classic strategy game. Challenge the AI or play with friends online.
            </p>
            <div class="token-cost">Coming Soon</div>
            <div class="game-stats">
                <div class="stat">
                    <span class="stat-value">--</span>
                    <span class="stat-label">Games</span>
                </div>
                <div class="stat">
                    <span class="stat-value">--</span>
                    <span class="stat-label">Wins</span>
                </div>
                <div class="stat">
                    <span class="stat-value">--</span>
                    <span class="stat-label">Win Rate</span>
                </div>
            </div>
            <button class="play-button" disabled style="opacity: 0.5; cursor: not-allowed;">Coming Soon</button>
        </div>
        
        <div class="game-card" style="opacity: 0.7;">
            <span class="game-icon">🎲</span>
            <h3 class="game-title">Number Guessing</h3>
            <p class="game-description">
                Test your luck and intuition in this number guessing challenge.
            </p>
            <div class="token-cost">Coming Soon</div>
            <div class="game-stats">
                <div class="stat">
                    <span class="stat-value">--</span>
                    <span class="stat-label">Games</span>
                </div>
                <div class="stat">
                    <span class="stat-value">--</span>
                    <span class="stat-label">Wins</span>
                </div>
                <div class="stat">
                    <span class="stat-value">--</span>
                    <span class="stat-label">Win Rate</span>
                </div>
            </div>
            <button class="play-button" disabled style="opacity: 0.5; cursor: not-allowed;">Coming Soon</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadUserStats();
    loadFlappyBirdStats();
});

async function loadUserStats() {
    try {
        const response = await fetch('/api/gaming/session/stats/', {
            headers: {
                'Authorization': `Bearer ${getAuthToken()}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                document.getElementById('totalGames').textContent = data.stats.total_sessions || 0;
                document.getElementById('totalWins').textContent = data.stats.wins || 0;
                document.getElementById('winRate').textContent = data.stats.win_rate || 0;
            }
        }
    } catch (error) {
        console.error('Error loading user stats:', error);
    }
}

async function loadFlappyBirdStats() {
    try {
        const response = await fetch('/api/gaming/flappy-bird/stats/', {
            headers: {
                'Authorization': `Bearer ${getAuthToken()}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                document.getElementById('flappyGames').textContent = data.stats.total_games || 0;
                document.getElementById('flappyHighScore').textContent = data.stats.high_score || 0;
                document.getElementById('flappyBestPipes').textContent = data.stats.best_pipes_passed || 0;
            }
        }
    } catch (error) {
        console.error('Error loading Flappy Bird stats:', error);
    }
}

function getAuthToken() {
    // Get CSRF token for Django
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
}
</script>
{% endblock %}
