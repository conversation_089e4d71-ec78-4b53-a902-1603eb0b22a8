import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';

// Enable debug mode only in development
const DEBUG = process.env.NODE_ENV === 'development';

interface CartItem {
  id: number;
  product: {
    id: number;
    name: string;
    slug: string;
    price: number;
    image: string;
  };
  quantity: number;
  price: number;
  variant_id?: string;
  variant_details?: {
    id: string;
    title: string;
    color?: string;
    size?: string;
    price: string;
  };
  variant_price?: number;
  total_price: number;
}

interface CartOptions {
  size?: string;
  color?: string;
  [key: string]: any;
}

interface CartContextType {
  cart: CartItem[];
  cartId: string | null;
  loading: boolean;
  error: string | null;
  addToCart: (productId: string | number, quantity: number, variantId?: string, options?: CartOptions) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  updateQuantity: (itemId: number, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  totalItems: number;
  totalPrice: number;
  fetchCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [cartId, setCartId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch cart on initial load
    fetchCart();
  }, []);

  const fetchCart = async () => {
    const token = localStorage.getItem('access_token');
    if (!token) {
      // If not logged in, try to get cart from local storage
      const localCart = localStorage.getItem('cart');
      if (localCart) {
        setCart(JSON.parse(localCart));
      }
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const response = await axios.get(`${baseUrl}/api/orders/cart/`, {
        headers: {
          'Authorization': `JWT ${token}`
        }
      });

      // Check if we have results and items
      if (response.data && response.data.results && response.data.results.length > 0 && response.data.results[0].items) {
        const cartData = response.data.results[0];

        // Set cart ID
        setCartId(cartData.id);

        // Map the items to match our CartItem interface
        const cartItems = cartData.items.map((item: any) => ({
          id: item.id,
          product: {
            id: item.product.id,
            name: item.product.name,
            slug: item.product.slug,
            price: parseFloat(item.product.price),
            image: item.product.main_image ? item.product.main_image.image : null
          },
          quantity: item.quantity,
          price: parseFloat(item.product.price),
          variant_id: item.variant_id,
          variant_details: item.variant_details,
          variant_price: item.variant_price ? parseFloat(item.variant_price) : undefined,
          total_price: parseFloat(item.total_price)
        }));
        setCart(cartItems);
      } else {
        setCart([]);
        setCartId(null);
      }

      setLoading(false);
    } catch (err: any) {
      if (DEBUG) {
        console.error('Error fetching cart:', err);
      }
      setError(err.response?.data?.detail || 'Failed to fetch cart');
      setLoading(false);
    }
  };

  const addToCart = async (productId: string | number, quantity: number, variantId?: string, options?: CartOptions) => {
    const token = localStorage.getItem('access_token');

    try {
      setLoading(true);
      setError(null);

      if (DEBUG) {
        console.log('CartContext: Adding to cart with product ID:', productId, 'quantity:', quantity, 'variant ID:', variantId, 'and options:', options);
        console.log('CartContext: Token exists:', !!token);
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';

      if (DEBUG) {
        console.log('CartContext: API URL:', `${baseUrl}/api/orders/cart/add/`);
      }

      // Prepare request data
      const requestData: any = {
        product_id: productId,
        quantity: quantity
      };

      // Add variant_id if provided
      if (variantId) {
        requestData.variant_id = variantId;
      }

      // Add options if provided
      if (options) {
        requestData.options = options;
      }

      if (DEBUG) {
        console.log('CartContext: Request data:', requestData);
      }

      const response = await axios.post(
        `${baseUrl}/api/orders/cart/add/`,
        requestData,
        {
          headers: {
            'Authorization': token ? `JWT ${token}` : '',
            'Content-Type': 'application/json'
          }
        }
      );

      if (DEBUG) {
        console.log('CartContext: Add to cart response:', response.data);
      }

      // Update cart state
      await fetchCart();

      setLoading(false);
    } catch (err: any) {
      if (DEBUG) {
        console.error('CartContext: Error adding to cart:', err);

        if (err.response) {
          console.error('CartContext: Error response:', {
            status: err.response.status,
            data: err.response.data,
            headers: err.response.headers
          });
        } else if (err.request) {
          console.error('CartContext: No response received:', err.request);
        } else {
          console.error('CartContext: Error message:', err.message);
        }
      }

      setError(err.response?.data?.detail || 'Failed to add item to cart');
      setLoading(false);
      throw err;
    }
  };

  const removeFromCart = async (itemId: number) => {
    const token = localStorage.getItem('access_token');

    try {
      setLoading(true);
      setError(null);

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      await axios.delete(
        `${baseUrl}/api/orders/cart/remove/${itemId}/`,
        {
          headers: {
            'Authorization': token ? `JWT ${token}` : ''
          }
        }
      );

      // Update cart state
      await fetchCart();

      setLoading(false);
    } catch (err: any) {
      if (DEBUG) {
        console.error('Error removing from cart:', err);
      }
      setError(err.response?.data?.detail || 'Failed to remove item from cart');
      setLoading(false);
      throw err;
    }
  };

  const updateQuantity = async (itemId: number, quantity: number) => {
    const token = localStorage.getItem('access_token');

    try {
      setLoading(true);
      setError(null);

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      await axios.put(
        `${baseUrl}/api/orders/cart/update/${itemId}/`,
        {
          quantity: quantity
        },
        {
          headers: {
            'Authorization': token ? `JWT ${token}` : '',
            'Content-Type': 'application/json'
          }
        }
      );

      // Update cart state
      await fetchCart();

      setLoading(false);
    } catch (err: any) {
      if (DEBUG) {
        console.error('Error updating cart:', err);
      }
      setError(err.response?.data?.detail || 'Failed to update cart');
      setLoading(false);
      throw err;
    }
  };

  const clearCart = async () => {
    const token = localStorage.getItem('access_token');

    try {
      setLoading(true);
      setError(null);

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      await axios.delete(
        `${baseUrl}/api/orders/cart/clear/`,
        {
          headers: {
            'Authorization': token ? `JWT ${token}` : ''
          }
        }
      );

      // Update cart state
      setCart([]);
      setCartId(null);

      setLoading(false);
    } catch (err: any) {
      if (DEBUG) {
        console.error('Error clearing cart:', err);
      }
      setError(err.response?.data?.detail || 'Failed to clear cart');
      setLoading(false);
      throw err;
    }
  };

  // Calculate total items and price
  const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
  const totalPrice = cart.reduce((total, item) => {
    // Use variant price if available, otherwise use product price
    const price = item.variant_price || item.product.price;
    return total + (price * item.quantity);
  }, 0);

  const value = {
    cart,
    cartId,
    loading,
    error,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    totalItems,
    totalPrice,
    fetchCart
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

export default CartContext;
