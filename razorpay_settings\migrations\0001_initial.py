# Generated by Django 5.0.2 on 2025-05-08 04:34

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RazorpaySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_live', models.BooleanField(default=False, help_text='Toggle between Test (unchecked) and Live (checked) mode', verbose_name='Live Mode')),
                ('test_key_id', models.Char<PERSON>ield(help_text='Razorpay Test Key ID', max_length=255, verbose_name='Test Key ID')),
                ('test_key_secret', models.Char<PERSON><PERSON>(help_text='Razorpay Test Key Secret', max_length=255, verbose_name='Test Key Secret')),
                ('live_key_id', models.Char<PERSON><PERSON>(help_text='Razorpay Live Key ID', max_length=255, verbose_name='Live Key ID')),
                ('live_key_secret', models.<PERSON><PERSON><PERSON><PERSON>(help_text='Razorpay Live Key Secret', max_length=255, verbose_name='Live Key Secret')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Razorpay Settings',
                'verbose_name_plural': 'Razorpay Settings',
            },
        ),
    ]
