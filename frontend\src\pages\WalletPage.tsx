import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useWallet } from '../hooks/useWallet';
import WalletBalance from '../components/wallet/WalletBalance';
import { formatINR } from '../utils/currencyFormatter';

const WalletPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { wallet, transactions, loading, error } = useWallet();

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
        <div className="relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-72 h-72 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-full opacity-10"></div>
          <div className="absolute bottom-0 left-0 -mb-32 -ml-32 w-64 h-64 bg-gradient-to-tr from-cyan-400 to-blue-500 rounded-full opacity-10"></div>

          <div className="relative container mx-auto px-4 py-24 text-center">
            <div className="max-w-2xl mx-auto">
              <div className="mb-8">
                <span className="text-6xl mb-4 block">💰</span>
                <h1 className="text-4xl font-bold text-gray-900 mb-4">Smart Wallet</h1>
                <p className="text-xl text-gray-600 mb-8">Please log in to access your digital wallet and manage your tokens!</p>
              </div>

              <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                <div className="mb-6">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-2">Ready to Manage Your Tokens?</h2>
                  <p className="text-gray-600">Access your wallet to view balance, transaction history, and earn rewards!</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                  <div className="text-center p-4">
                    <div className="text-3xl mb-2">🪙</div>
                    <div className="font-semibold text-gray-800">Token Balance</div>
                    <div className="text-sm text-gray-600">View your tokens</div>
                  </div>
                  <div className="text-center p-4">
                    <div className="text-3xl mb-2">📊</div>
                    <div className="font-semibold text-gray-800">Transaction History</div>
                    <div className="text-sm text-gray-600">Track earnings</div>
                  </div>
                  <div className="text-center p-4">
                    <div className="text-3xl mb-2">💳</div>
                    <div className="font-semibold text-gray-800">Smart Payments</div>
                    <div className="text-sm text-gray-600">Easy checkout</div>
                  </div>
                </div>

                <Link
                  to="/login"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🚀</span>
                  Login to Access Wallet
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'game_win': return '🏆';
      case 'game_draw': return '🤝';
      case 'game_participation': return '🎮';
      case 'referral_bonus': return '👥';
      case 'spin_wheel': return '🎰';
      case 'purchase_redemption': return '🛒';
      case 'admin_adjustment': return '⚙️';
      case 'bonus': return '🎁';
      default: return '💰';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Hero Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-700">
        {/* Background decorative elements */}
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-64 h-64 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>

        <div className="relative container mx-auto px-4 py-12">
          <div className="text-center text-white">
            <div className="flex items-center justify-center mb-4">
              <span className="text-5xl mr-4">💰</span>
              <div>
                <h1 className="text-4xl font-bold mb-2">My Smart Wallet</h1>
                <p className="text-xl text-emerald-100">Welcome back, {user?.username}! Manage your tokens and rewards</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Wallet Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {/* Current Balance Card */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-700 p-6 text-white shadow-2xl transform hover:scale-105 transition-all duration-300">
            <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
            <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

            <div className="relative z-10">
              <div className="flex items-center mb-4">
                <span className="text-3xl mr-3">🪙</span>
                <h2 className="text-xl font-bold">Current Balance</h2>
              </div>
              <WalletBalance showDetails={true} />
            </div>
          </div>

          {/* Total Earned Card */}
          <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">📈</span>
              <h2 className="text-xl font-semibold text-gray-800">Total Earned</h2>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <span className="font-bold text-3xl text-green-600">{wallet?.total_earned || 0}</span>
              <span className="text-sm text-gray-600">tokens</span>
            </div>
            <div className="text-sm text-gray-500">
              ≈ {formatINR((wallet?.total_earned || 0) * 0.1)}
            </div>
            <div className="mt-4 bg-green-50 rounded-lg p-3">
              <div className="text-xs text-green-700 font-medium">💡 Earn more by playing games!</div>
            </div>
          </div>

          {/* Total Spent Card */}
          <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">💳</span>
              <h2 className="text-xl font-semibold text-gray-800">Total Spent</h2>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <span className="font-bold text-3xl text-purple-600">{wallet?.total_spent || 0}</span>
              <span className="text-sm text-gray-600">tokens</span>
            </div>
            <div className="text-sm text-gray-500">
              ≈ {formatINR((wallet?.total_spent || 0) * 0.1)}
            </div>
            <div className="mt-4 bg-purple-50 rounded-lg p-3">
              <div className="text-xs text-purple-700 font-medium">🛍️ Smart shopping with token discounts!</div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Quick Actions</h2>
            <p className="text-lg text-gray-600">Manage your tokens and explore features</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Link
              to="/game-dashboard"
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 p-8 text-white shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-4">
                  <span className="text-4xl mr-4">🎮</span>
                  <h3 className="text-2xl font-bold">Play Games</h3>
                </div>
                <p className="text-blue-100 mb-4">Compete in battles and tournaments to earn more tokens!</p>
                <div className="flex items-center text-sm">
                  <span className="mr-2">🚀</span>
                  Start Playing Now
                </div>
              </div>
            </Link>

            <Link
              to="/shop"
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-700 p-8 text-white shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              <div className="absolute top-0 left-0 -mt-6 -ml-6 w-28 h-28 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 right-0 -mb-10 -mr-10 w-36 h-36 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-4">
                  <span className="text-4xl mr-4">🛍️</span>
                  <h3 className="text-2xl font-bold">Shop Smart</h3>
                </div>
                <p className="text-emerald-100 mb-4">Use your tokens for exclusive discounts on trendy products!</p>
                <div className="flex items-center text-sm">
                  <span className="mr-2">💎</span>
                  Browse Products
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* Transaction History */}
        <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
          <div className="flex items-center mb-6">
            <span className="text-3xl mr-4">📊</span>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Transaction History</h2>
              <p className="text-gray-600">{transactions.length} transactions</p>
            </div>
          </div>

          {transactions.length === 0 ? (
            <div className="text-center py-12">
              <div className="mb-6">
                <span className="text-6xl mb-4 block">📝</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No transactions yet</h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">Start playing games or shopping to see your transaction history and track your token activity</p>
              <Link
                to="/game-dashboard"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
              >
                <span className="mr-2">🎯</span>
                Play Games to Earn Tokens
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-md mr-4">
                      <span className="text-xl">{getTransactionIcon(transaction.transaction_type)}</span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{transaction.transaction_type_display}</h3>
                      <p className="text-sm text-gray-600">
                        {formatDate(transaction.created_at)}
                      </p>
                      {transaction.description && (
                        <p className="text-xs text-gray-500 mt-1">{transaction.description}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`font-bold text-lg ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-purple-600'
                    }`}>
                      {transaction.amount_display} tokens
                    </span>
                    <p className="text-sm text-gray-500">
                      Balance: {transaction.balance_after}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

      {/* Token Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
        <h3 className="font-semibold text-blue-800 mb-2">💡 How to Earn Tokens</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Win games: 5 tokens per victory</li>
          <li>• Participate in battles and challenges</li>
          <li>• Refer friends: Bonus tokens (coming soon)</li>
          <li>• Special events and promotions</li>
          <li>• Daily login bonuses (coming soon)</li>
        </ul>

        <h3 className="font-semibold text-blue-800 mb-2 mt-4">🛒 How to Use Tokens</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 1 token = ₹0.10 discount on eligible products</li>
          <li>• Use tokens during checkout for instant discounts</li>
          <li>• Discount percentage and caps vary by product</li>
          <li>• Look for 🪙 Token Discount badges on products</li>
          <li>• Combine with other offers for maximum savings</li>
        </ul>
      </div>
      </div>
    </div>
  );
};

export default WalletPage;
