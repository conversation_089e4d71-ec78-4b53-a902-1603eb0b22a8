from django.core.management.base import BaseCommand
from django.conf import settings
from printify.api_client import PrintifyAPIClient
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Debug Printify API connection and configuration'

    def handle(self, *args, **options):
        self.stdout.write('=== Printify Debug Information ===')
        
        # Check configuration
        self.stdout.write(f'PRINTIFY_API_TOKEN: {"✅ Set" if hasattr(settings, "PRINTIFY_API_TOKEN") and settings.PRINTIFY_API_TOKEN else "❌ Not set"}')
        self.stdout.write(f'PRINTIFY_SHOP_ID: {getattr(settings, "PRINTIFY_SHOP_ID", "❌ Not set")}')
        self.stdout.write(f'PRINTIFY_API_BASE_URL: {getattr(settings, "PRINTIFY_API_BASE_URL", "❌ Not set")}')
        
        if hasattr(settings, 'PRINTIFY_API_TOKEN') and settings.PRINTIFY_API_TOKEN:
            token_preview = settings.PRINTIFY_API_TOKEN[:20] + '...' if len(settings.PRINTIFY_API_TOKEN) > 20 else settings.PRINTIFY_API_TOKEN
            self.stdout.write(f'Token preview: {token_preview}')
        
        # Test API client initialization
        try:
            self.stdout.write('\n=== Testing API Client Initialization ===')
            client = PrintifyAPIClient()
            self.stdout.write('✅ API Client initialized successfully')
            
            # Test API connection
            self.stdout.write('\n=== Testing API Connection ===')
            shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)
            
            if shop_id:
                self.stdout.write(f'Testing with shop ID: {shop_id}')
                
                # Test getting shops
                try:
                    shops = client.get_shops()
                    self.stdout.write(f'✅ Successfully retrieved shops: {len(shops) if isinstance(shops, list) else "Unknown count"}')
                    if isinstance(shops, list) and shops:
                        for shop in shops[:3]:  # Show first 3 shops
                            self.stdout.write(f'  - Shop: {shop.get("title", "Unknown")} (ID: {shop.get("id", "Unknown")})')
                except Exception as e:
                    self.stdout.write(f'❌ Failed to get shops: {str(e)}')
                
                # Test getting products
                try:
                    self.stdout.write(f'\n=== Testing Product Retrieval ===')
                    products = client.get_products(shop_id)
                    
                    if isinstance(products, dict) and 'data' in products:
                        products_list = products['data']
                        self.stdout.write(f'✅ Successfully retrieved products: {len(products_list)}')
                    elif isinstance(products, list):
                        products_list = products
                        self.stdout.write(f'✅ Successfully retrieved products: {len(products_list)}')
                    else:
                        self.stdout.write(f'⚠️ Unexpected response format: {type(products)}')
                        self.stdout.write(f'Response: {str(products)[:200]}...')
                        return
                    
                    # Show first few products
                    if products_list:
                        self.stdout.write('\nFirst few products:')
                        for product in products_list[:3]:
                            title = product.get('title', 'Unknown')
                            product_id = product.get('id', 'Unknown')
                            self.stdout.write(f'  - {title} (ID: {product_id})')
                    else:
                        self.stdout.write('⚠️ No products found in your Printify shop')
                        
                except Exception as e:
                    self.stdout.write(f'❌ Failed to get products: {str(e)}')
                    self.stdout.write(f'Error type: {type(e).__name__}')
                    import traceback
                    self.stdout.write(f'Traceback: {traceback.format_exc()}')
            else:
                self.stdout.write('❌ No shop ID configured')
                
        except Exception as e:
            self.stdout.write(f'❌ Failed to initialize API client: {str(e)}')
            import traceback
            self.stdout.write(f'Traceback: {traceback.format_exc()}')
        
        self.stdout.write('\n=== Debug Complete ===')
