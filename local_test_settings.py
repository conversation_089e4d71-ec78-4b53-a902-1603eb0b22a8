"""
Local testing settings for Token Purchase System
================================================

This file provides SQLite database configuration for local testing
without requiring PostgreSQL setup.

Usage:
    python manage.py migrate --settings=local_test_settings
    python manage.py runserver --settings=local_test_settings
"""

from dropshipping_backend.settings import *

# Override database to use SQLite for local testing
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'test_db.sqlite3',
    }
}

# Disable some features that might cause issues in testing
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# CORS settings for local development
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

CORS_ALLOW_CREDENTIALS = True

# Allow all origins for local testing (less secure but easier for development)
CORS_ALLOW_ALL_ORIGINS = True

# Simplify logging for testing
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
        },
        'wallet': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}

print("🧪 Using local test settings with SQLite database")
print(f"📁 Database file: {DATABASES['default']['NAME']}")
