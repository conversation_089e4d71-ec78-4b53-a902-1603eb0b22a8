# Generated by Django 5.0.2 on 2025-06-03 07:03

import datetime
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GameType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('display_name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('rules', models.JSONField(default=dict, help_text='Game rules and configuration')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Battle',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_ai_battle', models.BooleanField(default=False, help_text='True if player2 is AI')),
                ('status', models.CharField(choices=[('waiting', 'Waiting for Opponent'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('timeout', 'Timeout')], default='waiting', max_length=20)),
                ('result', models.CharField(blank=True, choices=[('player1_win', 'Player 1 Wins'), ('player2_win', 'Player 2 Wins'), ('draw', 'Draw'), ('cancelled', 'Cancelled')], max_length=20, null=True)),
                ('game_state', models.JSONField(default=dict, help_text='Current game state')),
                ('moves_history', models.JSONField(default=list, help_text='History of all moves')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('tokens_awarded', models.PositiveIntegerField(default=0)),
                ('winner_tokens', models.PositiveIntegerField(default=0)),
                ('loser_tokens', models.PositiveIntegerField(default=0)),
                ('player1', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='battles_as_player1', to=settings.AUTH_USER_MODEL)),
                ('player2', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='battles_as_player2', to=settings.AUTH_USER_MODEL)),
                ('game_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gaming.gametype')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PlayerStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_battles', models.PositiveIntegerField(default=0)),
                ('battles_won', models.PositiveIntegerField(default=0)),
                ('battles_lost', models.PositiveIntegerField(default=0)),
                ('battles_drawn', models.PositiveIntegerField(default=0)),
                ('total_tokens_earned', models.PositiveIntegerField(default=0)),
                ('current_win_streak', models.PositiveIntegerField(default=0)),
                ('best_win_streak', models.PositiveIntegerField(default=0)),
                ('total_play_time', models.DurationField(default=datetime.timedelta)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='gaming_stats', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='GameMove',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('move_data', models.JSONField(help_text='Move data specific to game type')),
                ('move_number', models.PositiveIntegerField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('battle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='moves', to='gaming.battle')),
                ('player', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['move_number'],
                'unique_together': {('battle', 'move_number')},
            },
        ),
    ]
