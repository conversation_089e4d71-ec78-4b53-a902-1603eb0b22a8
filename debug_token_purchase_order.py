#!/usr/bin/env python
"""
Debug Token Purchase Order Creation
==================================

This script tests the complete token purchase order creation flow.
"""

import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'local_test_settings')
django.setup()

from django.contrib.auth import get_user_model
from wallet.models import TokenPack, TokenPurchase, Wallet
from wallet.token_purchase_views import create_token_purchase_order
from django.test import RequestFactory
import json

User = get_user_model()

def debug_token_purchase_order():
    """Debug the complete token purchase order creation"""
    
    print("🔍 Debugging Token Purchase Order Creation")
    print("=" * 60)
    
    try:
        # Step 1: Check if test user exists
        print("\n1️⃣ Checking test user...")
        try:
            user = User.objects.get(username='testuser')
            print(f"✅ Test user found: {user.username}")
        except User.DoesNotExist:
            print("❌ Test user not found")
            return False
        
        # Step 2: Check if token packs exist
        print("\n2️⃣ Checking token packs...")
        token_packs = TokenPack.objects.filter(is_active=True)
        print(f"   Found {token_packs.count()} active token packs")
        
        if not token_packs.exists():
            print("❌ No active token packs found")
            return False
        
        first_pack = token_packs.first()
        print(f"   Using pack: {first_pack.name} - {first_pack.tokens} tokens for ₹{first_pack.price_inr}")
        
        # Step 3: Check user's wallet
        print("\n3️⃣ Checking user wallet...")
        wallet, created = Wallet.objects.get_or_create(user=user)
        print(f"   Wallet balance: {wallet.balance} tokens")
        print(f"   Wallet created: {created}")
        
        # Step 4: Test the view function directly
        print("\n4️⃣ Testing token purchase order creation...")
        
        factory = RequestFactory()
        request_data = {
            'token_pack_id': str(first_pack.id)
        }
        
        request = factory.post(
            '/api/wallet/create-token-order/',
            data=json.dumps(request_data),
            content_type='application/json'
        )
        request.user = user
        
        print(f"   Request data: {request_data}")
        print(f"   User: {request.user.username}")
        
        # Call the view function
        try:
            response = create_token_purchase_order(request)
            print(f"   Response status: {response.status_code}")
            
            if hasattr(response, 'render'):
                response.render()
            
            response_data = json.loads(response.content.decode('utf-8'))
            print(f"   Response data: {response_data}")
            
            if response.status_code == 201:
                print("✅ Token purchase order created successfully!")
                
                # Check if TokenPurchase was created
                purchase_id = response_data.get('purchase_id')
                if purchase_id:
                    try:
                        purchase = TokenPurchase.objects.get(id=purchase_id)
                        print(f"   Purchase record: {purchase}")
                        print(f"   Razorpay order ID: {purchase.razorpay_order_id}")
                    except TokenPurchase.DoesNotExist:
                        print("❌ Purchase record not found in database")
                
                return True
            else:
                print(f"❌ Order creation failed with status {response.status_code}")
                print(f"   Error: {response_data.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"❌ Exception in view function: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """Test creating TokenPurchase model directly"""
    
    print("\n🧪 Testing TokenPurchase Model Creation")
    print("=" * 50)
    
    try:
        user = User.objects.get(username='testuser')
        token_pack = TokenPack.objects.filter(is_active=True).first()
        
        if not token_pack:
            print("❌ No token pack available")
            return False
        
        print(f"Creating TokenPurchase for user: {user.username}")
        print(f"Token pack: {token_pack.name}")
        
        # Create TokenPurchase directly
        purchase = TokenPurchase.objects.create(
            user=user,
            token_pack=token_pack,
            tokens_purchased=token_pack.tokens,
            amount_paid=token_pack.price_inr,
            payment_status='pending'
        )
        
        print(f"✅ TokenPurchase created: {purchase.id}")
        print(f"   Status: {purchase.payment_status}")
        print(f"   Tokens: {purchase.tokens_purchased}")
        print(f"   Amount: ₹{purchase.amount_paid}")
        
        # Clean up
        purchase.delete()
        print("   Cleaned up test purchase")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 Starting Token Purchase Order Debug Session")
    
    # Test model creation first
    model_success = test_model_creation()
    
    # Test the complete flow
    flow_success = debug_token_purchase_order()
    
    print("\n🎯 Debug Summary:")
    print(f"   Model Creation: {'✅ Working' if model_success else '❌ Failed'}")
    print(f"   Complete Flow: {'✅ Working' if flow_success else '❌ Failed'}")
    
    if model_success and flow_success:
        print("\n🎉 Token purchase order creation is working!")
        print("   The frontend should be able to create orders successfully.")
    elif model_success and not flow_success:
        print("\n🔧 Model works but view has issues.")
        print("   Check the token_purchase_views.py implementation.")
    else:
        print("\n❌ Token purchase system has issues.")
        print("   Check database models and migrations.")
