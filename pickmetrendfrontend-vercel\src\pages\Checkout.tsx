import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import RazorpayCheckout from '../components/RazorpayCheckout';
import TokenDiscountToggle from '../components/TokenDiscountToggle';
import axios from 'axios';
import { formatINR } from '../utils/currencyFormatter';

const Checkout = () => {
  const navigate = useNavigate();
  const { cart, cartId, clearCart } = useCart();
  const { isAuthenticated } = useAuth();
  const [formData, setFormData] = useState({
    // Billing information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'IN', // Default to India

    // Shipping information (optional)
    shippingFirstName: '',
    shippingLastName: '',
    shippingAddress: '',
    shippingCity: '',
    shippingState: '',
    shippingPostalCode: '',
    shippingCountry: 'IN',

    // Use same address for shipping
    sameAsShipping: true,

    paymentMethod: 'razorpay' // Default to Razorpay
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [orderData, setOrderData] = useState<any>(null);
  const [showRazorpay, setShowRazorpay] = useState<boolean>(false);
  const [tokenDiscount, setTokenDiscount] = useState<any>({
    enabled: false,
    tokensToUse: 0,
    discountAmount: 0,
    finalAmount: 0,
    discountBreakdown: [] // Add this to hold eligible_items breakdown
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    // Cast to appropriate type based on the element type
    if (e.target instanceof HTMLInputElement && e.target.type === 'checkbox') {
      const { name, checked } = e.target;

      // Handle checkbox inputs
      setFormData({ ...formData, [name]: checked });

      // If "same as shipping" is checked, copy billing address to shipping address
      if (name === 'sameAsShipping' && checked) {
        setFormData({
          ...formData,
          sameAsShipping: true,
          shippingFirstName: formData.firstName,
          shippingLastName: formData.lastName,
          shippingAddress: formData.address,
          shippingCity: formData.city,
          shippingState: formData.state,
          shippingPostalCode: formData.postalCode,
          shippingCountry: formData.country
        });
      }
    } else {
      // Handle regular inputs (text, select, etc.)
      const { name, value } = e.target;
      setFormData({ ...formData, [name]: value });

      // If "same as shipping" is checked and a billing field changes, update the corresponding shipping field
      if (formData.sameAsShipping) {
        const shippingFieldMap: Record<string, string> = {
          firstName: 'shippingFirstName',
          lastName: 'shippingLastName',
          address: 'shippingAddress',
          city: 'shippingCity',
          state: 'shippingState',
          postalCode: 'shippingPostalCode',
          country: 'shippingCountry'
        };

        const shippingField = shippingFieldMap[name];
        if (shippingField) {
          setFormData(prev => ({ ...prev, [name]: value, [shippingField]: value }));
        } else {
          setFormData(prev => ({ ...prev, [name]: value }));
        }
      } else {
        setFormData(prev => ({ ...prev, [name]: value }));
      }
    }
  };

  const calculateTotal = () => {
    const subtotal = Array.isArray(cart)
      ? cart.reduce((total: number, item: any) => {
          // Use variant price if available, otherwise use product price
          const price = item.variant_price || item.product.price;
          return total + (price * item.quantity);
        }, 0)
      : 0;

    // Apply token discount if enabled
    return tokenDiscount.enabled ? tokenDiscount.finalAmount : subtotal;
  };

  const getSubtotal = () => {
    return Array.isArray(cart)
      ? cart.reduce((total: number, item: any) => {
          const price = item.variant_price || item.product.price;
          return total + (price * item.quantity);
        }, 0)
      : 0;
  };

  const handleTokenDiscountChange = (discountData: any) => {
    // If discountData does not have discountBreakdown, try to get it from TokenDiscountToggle's discountInfo
    if (discountData && typeof discountData === 'object' && !discountData.discountBreakdown && discountData.eligible_items) {
      setTokenDiscount({ ...discountData, discountBreakdown: discountData.eligible_items });
    } else {
      setTokenDiscount(discountData);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Basic form validation
    if (!formData.firstName || !formData.lastName) {
      setError('Please enter your full name');
      setLoading(false);
      return;
    }

    if (!formData.email) {
      setError('Please enter your email address');
      setLoading(false);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      setLoading(false);
      return;
    }

    if (!formData.phone) {
      setError('Please enter your phone number');
      setLoading(false);
      return;
    }

    // Validate phone number (at least 10 digits)
    const phoneDigits = formData.phone.replace(/\D/g, '');
    if (phoneDigits.length < 10) {
      setError('Please enter a valid phone number with at least 10 digits');
      setLoading(false);
      return;
    }

    if (!formData.address) {
      setError('Please enter your address');
      setLoading(false);
      return;
    }

    if (!formData.city) {
      setError('Please enter your city');
      setLoading(false);
      return;
    }

    if (!formData.postalCode) {
      setError('Please enter your postal code');
      setLoading(false);
      return;
    }

    if (!formData.country) {
      setError('Please select your country');
      setLoading(false);
      return;
    }

    // Log the form data for debugging
    console.log('Submitting checkout form with data:', formData);

    // Get the base URL and construct the API URL
    const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
    const apiUrl = `${baseUrl}/api`;
    const createOrderUrl = `${apiUrl}/orders/create_from_cart/`;
    console.log('API URL:', apiUrl);
    console.log('Create Order URL:', createOrderUrl);

    try {
      console.log('Creating order from cart...');

      // Prepare the request data
      const requestData = {
        // Billing information
        full_name: `${formData.firstName} ${formData.lastName}`,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        state: formData.state || 'Default State',
        zipcode: formData.postalCode,
        country: formData.country,
        payment_method: formData.paymentMethod.toLowerCase(),
        notes: '',

        // Shipping information (if different from billing)
        shipping_first_name: formData.sameAsShipping ? formData.firstName : formData.shippingFirstName,
        shipping_last_name: formData.sameAsShipping ? formData.lastName : formData.shippingLastName,
        shipping_address1: formData.sameAsShipping ? formData.address : formData.shippingAddress,
        shipping_city: formData.sameAsShipping ? formData.city : formData.shippingCity,
        shipping_state: formData.sameAsShipping ? (formData.state || 'Default State') : (formData.shippingState || 'Default State'),
        shipping_zip: formData.sameAsShipping ? formData.postalCode : formData.shippingPostalCode,
        shipping_country: formData.sameAsShipping ? formData.country : formData.shippingCountry
      };

      // Log the exact data being sent
      console.log('Request data being sent:', JSON.stringify(requestData));

      // Create order from cart
      const response = await axios.post(
        createOrderUrl,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `JWT ${localStorage.getItem('access_token')}`
          }
        }
      );

      // If payment method is Razorpay, show Razorpay checkout
      if (formData.paymentMethod.toLowerCase() === 'razorpay') {
        // Log the response data
        console.log('Order created for Razorpay payment:', response.data);

        // Check if Razorpay data is present
        if (!response.data.razorpay) {
          console.error('Razorpay data is missing in the response:', response.data);
          setError('Razorpay data is missing. Please try again or choose a different payment method.');
          return;
        }

        // Validate required Razorpay fields
        const { order_id, amount, currency, key_id } = response.data.razorpay;
        if (!order_id || !amount || !currency || !key_id) {
          console.error('Missing required Razorpay fields:', response.data.razorpay);
          setError('Missing required Razorpay information. Please try again.');
          return;
        }

        // Save order data for Razorpay
        setOrderData(response.data);
        setShowRazorpay(true);
        console.log('Order data set for Razorpay payment:', response.data);

        // Don't clear cart yet, wait for payment confirmation
      } else {
        // For COD, clear cart and navigate to order confirmation
        clearCart();
        navigate(`/order-confirmation/${response.data.id}`);
      }
    } catch (err: any) {
      console.error('Checkout error:', err);

      if (err.response) {
        console.error('Response data:', err.response.data);
        console.error('Response status:', err.response.status);
        console.error('Response headers:', err.response.headers);
        console.error('Request URL:', err.config?.url);
        console.error('Request method:', err.config?.method);
        console.error('Request data:', err.config?.data);

        // Handle 404 errors specifically
        if (err.response.status === 404) {
          setError(`API endpoint not found: ${err.config?.url}. Please check the URL configuration.`);
          return;
        }

        // Handle validation errors
        if (err.response.data && typeof err.response.data === 'object') {
          // Check if it's a validation error object with 'errors' field
          if (err.response.data.errors) {
            console.log('Validation errors from server:', err.response.data.errors);

            // Format validation errors
            const errorMessages = Object.entries(err.response.data.errors)
              .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
              .join('; ');

            setError(`Validation failed: ${errorMessages}`);
          }
          // Check if it's a validation error object
          else if (Object.keys(err.response.data).some(key =>
            key !== 'detail' && Array.isArray(err.response.data[key])
          )) {
            const errorMessages = Object.entries(err.response.data)
              .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
              .join('; ');
            setError(errorMessages || 'Failed to create order');
          } else if (err.response.data.detail) {
            setError(err.response.data.detail);
          } else {
            setError(JSON.stringify(err.response.data));
          }
        } else if (err.response.data?.detail) {
          setError(err.response.data.detail);
        } else {
          setError(`Error ${err.response.status}: Failed to create order`);
        }
      } else if (err.request) {
        console.error('No response received:', err.request);
        setError('No response from server. Please check your connection and try again.');
      } else {
        console.error('Error message:', err.message);
        setError(err.message || 'An error occurred during checkout');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle successful Razorpay payment
  const handlePaymentSuccess = async (paymentData: any) => {
    try {
      console.log('Payment successful, verifying with backend:', paymentData);

      // Get the base URL and construct the API URL
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const apiUrl = `${baseUrl}/api`;
      const verifyUrl = `${apiUrl}/orders/${orderData.id}/verify_razorpay_payment/`;
      console.log('API URL for payment verification:', apiUrl);
      console.log('Verify Payment URL:', verifyUrl);

      // Verify payment with backend
      const response = await axios.post(
        verifyUrl,
        paymentData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `JWT ${localStorage.getItem('access_token')}`
          }
        }
      );

      console.log('Payment verification response:', response.data);

      // Clear cart and navigate to order confirmation
      clearCart();
      navigate(`/order-confirmation/${orderData.id}`);
    } catch (err: any) {
      console.error('Payment verification error:', err);
      let errorMessage = 'Payment verification failed';

      if (err.response) {
        console.error('Response data:', err.response.data);
        console.error('Response status:', err.response.status);

        if (err.response.data?.detail) {
          errorMessage = err.response.data.detail;
        } else if (typeof err.response.data === 'object') {
          errorMessage = JSON.stringify(err.response.data);
        } else {
          errorMessage = `Error ${err.response.status}: Payment verification failed`;
        }
      } else if (err.request) {
        console.error('No response received:', err.request);
        errorMessage = 'No response from server. Please check your connection.';
      } else if (err.message) {
        console.error('Error message:', err.message);
        errorMessage = err.message;
      }

      setError(`Payment verification failed: ${errorMessage}`);

      // Show a more user-friendly message with instructions
      alert('Your payment could not be verified. Please contact customer support with your order ID: ' +
        orderData.id + '. You can try again later from your order history.');
    }
  };

  // Handle Razorpay payment error
  const handlePaymentError = (error: any) => {
    console.error('Razorpay error:', error);
    let errorMessage = 'Unknown error';

    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.description) {
      errorMessage = error.description;
    }

    setError(`Payment failed: ${errorMessage}`);
  };

  if (!Array.isArray(cart) || cart.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex justify-center items-center">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-2xl shadow-2xl p-12 border border-gray-100 text-center">
            <span className="text-8xl mb-6 block">🛒</span>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Cart is Empty</h2>
            <p className="text-gray-600 mb-8 text-lg">Add some products to your cart before proceeding to checkout.</p>
            <button
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg"
              onClick={() => navigate('/shop')}
            >
              <span className="mr-2">🛍️</span>
              Go Shopping
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Hero Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700">
        {/* Background decorative elements */}
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-64 h-64 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>

        <div className="relative container mx-auto px-4 py-12">
          <div className="text-white text-center">
            <div className="flex items-center justify-center mb-4">
              <span className="text-5xl mr-4">💳</span>
              <div>
                <h1 className="text-4xl font-bold mb-2">Secure Checkout</h1>
                <p className="text-xl text-blue-100">Complete your order with confidence</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">

        {error && (
          <div className="mb-8 max-w-4xl mx-auto">
            <div className="bg-red-50 border border-red-200 rounded-2xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="text-3xl mr-4">⚠️</div>
                <div>
                  <h3 className="text-lg font-semibold text-red-800 mb-1">Payment Error</h3>
                  <p className="text-red-700">{error}</p>
                </div>
              </div>
              {orderData && (
                <div className="flex flex-col sm:flex-row gap-3 mt-4">
                  <button
                    onClick={() => setShowRazorpay(true)}
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg"
                  >
                    <span className="mr-2">🔄</span>
                    Try Payment Again
                  </button>
                  <button
                    onClick={() => {
                      setOrderData(null);
                      setShowRazorpay(false);
                      setError('');
                    }}
                    className="inline-flex items-center px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white font-semibold rounded-xl transition-all duration-200"
                  >
                    <span className="mr-2">✏️</span>
                    Change Order Details
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Show Razorpay checkout if order data is available and showRazorpay is true */}
        {orderData && showRazorpay && (
          <div className="mb-8 max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
              <div className="text-center mb-6">
                <span className="text-4xl mb-4 block">💳</span>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Complete Your Payment</h2>
                <p className="text-gray-600">Your order has been created. Please complete the payment to confirm your order.</p>
              </div>
              <RazorpayCheckout
                orderData={orderData}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
              />
            </div>
          </div>
        )}

        {!showRazorpay && (
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Shipping Information Form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                  <div className="flex items-center mb-6">
                    <span className="text-3xl mr-4">📦</span>
                    <h2 className="text-2xl font-bold text-gray-900">Shipping Information</h2>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="Enter your email address"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        required
                        placeholder="e.g., 9876543210 (10-digit Indian mobile number)"
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      />
                      <p className="text-xs text-gray-500 mt-1">Enter your 10-digit mobile number without +91</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Street Address</label>
                      <input
                        type="text"
                        name="address"
                        value={formData.address}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="Enter your full address"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                        <input
                          type="text"
                          name="city"
                          value={formData.city}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="Enter your city"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
                        <input
                          type="text"
                          name="state"
                          value={formData.state}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="Enter your state"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Postal Code</label>
                        <input
                          type="text"
                          name="postalCode"
                          value={formData.postalCode}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="Enter postal code"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
                      <select
                        name="country"
                        value={formData.country}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      >
                        <option value="IN">India</option>
                      </select>
                      <p className="text-xs text-gray-500 mt-1">We currently deliver only within India</p>
                    </div>

                    <div className="bg-gray-50 rounded-xl p-4">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="sameAsShipping"
                          checked={formData.sameAsShipping}
                          onChange={handleChange}
                          className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                        />
                        <span className="ml-3 text-sm font-medium text-gray-700">Use same address for shipping</span>
                      </label>
                    </div>

            {/* Shipping Address (only shown when sameAsShipping is false) */}
            {!formData.sameAsShipping && (
              <div className="mt-6 border-t pt-4">
                <h3 className="text-lg font-semibold mb-4">Shipping Address</h3>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                    <input
                      type="text"
                      name="shippingFirstName"
                      value={formData.shippingFirstName}
                      onChange={handleChange}
                      required={!formData.sameAsShipping}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                    <input
                      type="text"
                      name="shippingLastName"
                      value={formData.shippingLastName}
                      onChange={handleChange}
                      required={!formData.sameAsShipping}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                  <input
                    type="text"
                    name="shippingAddress"
                    value={formData.shippingAddress}
                    onChange={handleChange}
                    required={!formData.sameAsShipping}
                    className="w-full p-2 border rounded"
                  />
                </div>

                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                    <input
                      type="text"
                      name="shippingCity"
                      value={formData.shippingCity}
                      onChange={handleChange}
                      required={!formData.sameAsShipping}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">State</label>
                    <input
                      type="text"
                      name="shippingState"
                      value={formData.shippingState}
                      onChange={handleChange}
                      required={!formData.sameAsShipping}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                    <input
                      type="text"
                      name="shippingPostalCode"
                      value={formData.shippingPostalCode}
                      onChange={handleChange}
                      required={!formData.sameAsShipping}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
                  <select
                    name="shippingCountry"
                    value={formData.shippingCountry}
                    onChange={handleChange}
                    required={!formData.sameAsShipping}
                    className="w-full p-2 border rounded"
                  >
                    <option value="IN">India</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">We currently deliver only within India</p>
                </div>
              </div>
            )}

                    <div className="border-t border-gray-200 pt-6">
                      <div className="flex items-center mb-4">
                        <span className="text-2xl mr-3">💳</span>
                        <h3 className="text-lg font-semibold text-gray-900">Payment Method</h3>
                      </div>
                      <select
                        name="paymentMethod"
                        value={formData.paymentMethod}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      >
                        <option value="razorpay">💳 Online Payment (UPI, Cards, NetBanking) - Recommended</option>
                        <option value="cod">💰 Cash on Delivery (+₹50 charges)</option>
                      </select>
                      <div className="mt-2 text-xs text-gray-600">
                        <p>💡 Online Payment: UPI (Google Pay, PhonePe, Paytm), Credit/Debit Cards, NetBanking</p>
                        <p>📦 COD: Available in most areas with ₹50 additional charges</p>
                      </div>
                    </div>

                    <button
                      type="submit"
                      className="w-full py-4 px-6 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                      disabled={loading}
                    >
                      {loading ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                          Processing Order...
                        </div>
                      ) : (
                        <div className="flex items-center justify-center">
                          <span className="mr-2">🚀</span>
                          Place Order
                        </div>
                      )}
                    </button>
                  </form>
                </div>
              </div>

              {/* Order Summary Section */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100 sticky top-8">
                  <div className="flex items-center mb-6">
                    <span className="text-3xl mr-4">📋</span>
                    <h2 className="text-2xl font-bold text-gray-900">Order Summary</h2>
                  </div>

                  <div className="space-y-4 mb-6">
                    {Array.isArray(cart) && cart.map((item) => {
                      // Use variant price if available, otherwise use product price
                      const price = item.variant_price || item.product.price;
                      // Check if this product is token-eligible and if token discount is enabled
                      const isTokenEligible = !!(item.product && (item.product as any).token_discount_available === true);
                      let tokenDiscountPerItem = 0;
                      if (tokenDiscount.enabled && isTokenEligible && Array.isArray(tokenDiscount.discountBreakdown)) {
                        const breakdown = tokenDiscount.discountBreakdown.find((b: any) => b.product_name === item.product.name);
                        if (breakdown) {
                          tokenDiscountPerItem = breakdown.max_inr_discount / breakdown.quantity;
                        }
                      }
                      return (
                        <div key={item.id} className="flex justify-between items-start p-4 bg-gray-50 rounded-xl">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900">{item.product.name}</h4>
                            <div className="flex flex-wrap gap-2 mt-2">
                              <span className="text-sm text-gray-600">Qty: {item.quantity}</span>
                              {item.variant_price && (
                                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Variant</span>
                              )}
                              {isTokenEligible && (
                                <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">🪙 Token Eligible</span>
                              )}
                            </div>
                          </div>
                          <div className="text-right ml-4">
                            <div className="font-semibold text-gray-900">{formatINR(price * item.quantity)}</div>
                            {tokenDiscount.enabled && isTokenEligible && tokenDiscountPerItem > 0 && (
                              <div className="text-green-600 text-xs mt-1">- {formatINR(tokenDiscountPerItem * item.quantity)} token discount</div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Token Discount Section */}
                  {cartId && (
                    <div className="border-t border-gray-200 pt-6 mb-6">
                      <div className="flex items-center mb-4">
                        <span className="text-2xl mr-3">🪙</span>
                        <h3 className="text-lg font-semibold text-gray-900">Token Discount</h3>
                      </div>
                      <TokenDiscountToggle
                        cartId={cartId}
                        onTokenDiscountChange={handleTokenDiscountChange}
                        disabled={loading}
                      />
                    </div>
                  )}

                  {/* Order Total */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="text-gray-600 font-medium">Subtotal:</span>
                      <span className="text-xl font-bold text-gray-900">{formatINR(getSubtotal())}</span>
                    </div>

                    {tokenDiscount.enabled && (
                      <div className="flex justify-between items-center py-3 border-b border-gray-100">
                        <span className="text-green-600 font-medium">Token Discount ({tokenDiscount.tokensToUse} tokens):</span>
                        <span className="text-green-600 font-bold">-{formatINR(tokenDiscount.discountAmount)}</span>
                      </div>
                    )}

                    <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold text-gray-900">Total:</span>
                        <span className="text-2xl font-bold text-purple-600">{formatINR(calculateTotal())}</span>
                      </div>
                    </div>

                    {/* Security Badge */}
                    <div className="mt-6 p-4 bg-green-50 rounded-xl border border-green-200">
                      <div className="flex items-center justify-center text-green-700">
                        <span className="mr-2">🔒</span>
                        <span className="text-sm font-medium">Secure Checkout - SSL Encrypted</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Checkout;