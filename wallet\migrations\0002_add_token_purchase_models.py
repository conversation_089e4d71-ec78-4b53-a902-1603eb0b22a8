# Generated migration for token purchase models

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wallet', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='wallettransaction',
            name='transaction_type',
            field=models.CharField(choices=[('game_win', 'Game Win'), ('game_draw', 'Game Draw'), ('game_participation', 'Game Participation'), ('game_loss', 'Game Loss'), ('referral_bonus', 'Referral Bonus'), ('spin_wheel', 'Spin the Wheel'), ('purchase_redemption', 'Purchase Redemption'), ('admin_adjustment', 'Admin Adjustment'), ('bonus', 'Bonus'), ('signup_bonus', 'Signup Bonus'), ('refill_bonus', 'Token Refill'), ('token_purchase', 'Token Purchase')], max_length=20),
        ),
        migrations.CreateModel(
            name='TokenPack',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text="Pack name (e.g., 'Starter Pack')", max_length=100)),
                ('tokens', models.PositiveIntegerField(help_text='Number of tokens in this pack')),
                ('price_inr', models.DecimalField(decimal_places=2, help_text='Price in INR', max_digits=10)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this pack is available for purchase')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Display order (lower numbers first)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['sort_order', 'price_inr'],
            },
        ),
        migrations.CreateModel(
            name='TokenPurchase',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tokens_purchased', models.PositiveIntegerField(help_text='Number of tokens purchased')),
                ('amount_paid', models.DecimalField(decimal_places=2, help_text='Amount paid in INR', max_digits=10)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('razorpay_order_id', models.CharField(blank=True, max_length=100, null=True)),
                ('razorpay_payment_id', models.CharField(blank=True, max_length=100, null=True)),
                ('razorpay_signature', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('token_pack', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to='wallet.tokenpack')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='token_purchases', to=settings.AUTH_USER_MODEL)),
                ('wallet_transaction', models.ForeignKey(blank=True, help_text='Reference to the wallet transaction that credited the tokens', null=True, on_delete=django.db.models.deletion.SET_NULL, to='wallet.wallettransaction')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
