from django.core.management.base import BaseCommand
from customer_communication.models import EmailTemplate
import os

class Command(BaseCommand):
    help = 'Creates initial email templates'

    def handle(self, *args, **options):
        # Check if templates already exist
        if EmailTemplate.objects.exists():
            self.stdout.write(self.style.WARNING('Email templates already exist. Skipping...'))
            return

        # Create the initial email templates
        templates = [
            {
                'name': 'Order Confirmation',
                'template_type': 'order_confirmation',
                'subject': 'Order Confirmation - PickMeTrend Order #{{ order_id }}',
                'html_content': '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        .content {
            padding: 20px 0;
        }
        .order-details {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Order Confirmation</h1>
        </div>
        
        <div class="content">
            <p>Dear {{ customer_name }},</p>
            
            <p>Thank you for your order! We're pleased to confirm that we've received your order and it's being processed.</p>
            
            <div class="order-details">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> {{ order_id }}</p>
                <p><strong>Order Date:</strong> {{ order_date }}</p>
                <p><strong>Total Amount:</strong> ₹{{ order_total }}</p>
            </div>
            
            <p>We'll send you another email when your order ships with tracking information.</p>
            
            <p>If you have any questions or concerns about your order, please don't hesitate to contact our customer service <NAME_EMAIL>.</p>
            
            <p>Thank you for shopping with PickMeTrend!</p>
            
            <p>Best regards,<br>The PickMeTrend Team</p>
        </div>
        
        <div class="footer">
            <p>&copy; 2023 PickMeTrend. All rights reserved.</p>
        </div>
    </div>
</body>
</html>''',
                'text_content': '''Dear {{ customer_name }},

Thank you for your order! We're pleased to confirm that we've received your order and it's being processed.

Order Details:
Order Number: {{ order_id }}
Order Date: {{ order_date }}
Total Amount: ₹{{ order_total }}

We'll send you another email when your order ships with tracking information.

If you have any questions or concerns about your order, please don't hesitate to contact our customer service <NAME_EMAIL>.

Thank you for shopping with PickMeTrend!

Best regards,
The PickMeTrend Team''',
                'is_active': True
            },
            {
                'name': 'Feedback Request',
                'template_type': 'feedback_request',
                'subject': 'How was your PickMeTrend order? We\'d love your feedback!',
                'html_content': '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>We'd Love Your Feedback</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        .content {
            padding: 20px 0;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eee;
        }
        .button {
            display: inline-block;
            background-color: #4f46e5;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        .stars {
            font-size: 24px;
            color: #fbbf24;
            letter-spacing: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>How Was Your Experience?</h1>
        </div>
        
        <div class="content">
            <p>Dear {{ customer_name }},</p>
            
            <p>Thank you for your recent purchase from PickMeTrend! We hope you're enjoying your new items.</p>
            
            <p>We'd love to hear your feedback about your experience. Your input helps us improve our products and services for you and future customers.</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <div class="stars">★★★★★</div>
                <p>How would you rate your experience?</p>
                <a href="{{ feedback_url }}" style="display: inline-block; background-color: #4f46e5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Share Your Feedback</a>
            </div>
            
            <p>It will only take a minute of your time, and your feedback is invaluable to us.</p>
            
            <p>Thank you for choosing PickMeTrend!</p>
            
            <p>Best regards,<br>The PickMeTrend Team</p>
        </div>
        
        <div class="footer">
            <p>&copy; 2023 PickMeTrend. All rights reserved.</p>
        </div>
    </div>
</body>
</html>''',
                'text_content': '''Dear {{ customer_name }},

Thank you for your recent purchase from PickMeTrend! We hope you're enjoying your new items.

We'd love to hear your feedback about your experience. Your input helps us improve our products and services for you and future customers.

How would you rate your experience? Please visit the link below to share your feedback:

{{ feedback_url }}

It will only take a minute of your time, and your feedback is invaluable to us.

Thank you for choosing PickMeTrend!

Best regards,
The PickMeTrend Team''',
                'is_active': True
            },
            {
                'name': 'Support Ticket Confirmation',
                'template_type': 'support_ticket_confirmation',
                'subject': 'Support Ticket Received - {{ ticket_subject }}',
                'html_content': '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Ticket Received</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        .content {
            padding: 20px 0;
        }
        .ticket-details {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Support Ticket Received</h1>
        </div>
        
        <div class="content">
            <p>Dear {{ customer_name }},</p>
            
            <p>Thank you for contacting PickMeTrend support. We have received your ticket regarding:</p>
            
            <div class="ticket-details">
                <h3>Ticket Details</h3>
                <p><strong>Ticket ID:</strong> {{ ticket_id }}</p>
                <p><strong>Subject:</strong> {{ ticket_subject }}</p>
            </div>
            
            <p>Our team will review your inquiry and get back to you as soon as possible. Our typical response time is within 24 hours during business days.</p>
            
            <p>For your reference, here's a copy of your message:</p>
            
            <blockquote style="border-left: 3px solid #eee; padding-left: 15px; margin-left: 0; color: #555;">
                {{ ticket_message }}
            </blockquote>
            
            <p>If you have any additional information to add to your ticket, please reply to this email.</p>
            
            <p>Thank you for your patience.</p>
            
            <p>Best regards,<br>PickMeTrend Support Team</p>
        </div>
        
        <div class="footer">
            <p>&copy; 2023 PickMeTrend. All rights reserved.</p>
        </div>
    </div>
</body>
</html>''',
                'text_content': '''Dear {{ customer_name }},

Thank you for contacting PickMeTrend support. We have received your ticket regarding:

"{{ ticket_subject }}"

Ticket ID: {{ ticket_id }}

Our team will review your inquiry and get back to you as soon as possible. Our typical response time is within 24 hours during business days.

For your reference, here's a copy of your message:

{{ ticket_message }}

If you have any additional information to add to your ticket, please reply to this email.

Thank you for your patience.

Best regards,
PickMeTrend Support Team''',
                'is_active': True
            },
        ]

        # Add templates to the database
        for template_data in templates:
            EmailTemplate.objects.create(**template_data)
            self.stdout.write(self.style.SUCCESS(f'Created email template: {template_data["name"]}'))

        self.stdout.write(self.style.SUCCESS('Successfully created initial email templates'))
