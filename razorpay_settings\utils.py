"""
Utility functions for Razorpay integration.

These functions provide a convenient way to access Razorpay settings
from anywhere in the codebase.
"""
import logging
from django.conf import settings as django_settings
from .models import RazorpaySettings

logger = logging.getLogger(__name__)


def get_razorpay_keys():
    """
    Get the active Razorpay API keys based on the current mode (Test or Live).
    
    This function fetches the RazorpaySettings instance and returns the
    appropriate key_id and key_secret based on the is_live field.
    
    If no RazorpaySettings instance exists, it falls back to the keys defined
    in Django settings.
    
    Returns:
        tuple: A tuple containing (key_id, key_secret)
    """
    try:
        # Get the first (and should be only) RazorpaySettings instance
        razorpay_settings = RazorpaySettings.objects.first()
        
        if razorpay_settings:
            key_id, key_secret = razorpay_settings.get_keys()
            mode = "Live" if razorpay_settings.is_live else "Test"
            logger.info(f"Using Razorpay {mode} mode with key ID: {key_id[:6]}...")
            return key_id, key_secret
        
        # Fallback to Django settings if no RazorpaySettings instance exists
        logger.warning("No RazorpaySettings instance found, falling back to Django settings")
        return django_settings.RAZORPAY_KEY_ID, django_settings.RAZORPAY_KEY_SECRET
    
    except Exception as e:
        logger.error(f"Error getting Razorpay keys: {str(e)}")
        # Fallback to Django settings in case of any error
        return django_settings.RAZORPAY_KEY_ID, django_settings.RAZORPAY_KEY_SECRET


def get_razorpay_mode():
    """
    Get the current Razorpay mode (Test or Live).
    
    Returns:
        str: "Live" if in live mode, "Test" otherwise
    """
    try:
        razorpay_settings = RazorpaySettings.objects.first()
        if razorpay_settings and razorpay_settings.is_live:
            return "Live"
        return "Test"
    except Exception as e:
        logger.error(f"Error getting Razorpay mode: {str(e)}")
        return "Test"  # Default to Test mode for safety


def initialize_razorpay_client():
    """
    Initialize and return a Razorpay client with the current active keys.
    
    Returns:
        razorpay.Client: An initialized Razorpay client
    """
    try:
        import razorpay
        key_id, key_secret = get_razorpay_keys()
        return razorpay.Client(auth=(key_id, key_secret))
    except ImportError:
        logger.error("Razorpay package not installed")
        raise ImportError("Razorpay package is required. Install it with 'pip install razorpay'")
    except Exception as e:
        logger.error(f"Error initializing Razorpay client: {str(e)}")
        raise
