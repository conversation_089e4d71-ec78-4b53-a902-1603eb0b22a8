from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.template.loader import render_to_string
from django.http import HttpResponseRedirect
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
from .models import SupportTicket, TicketResponse, EmailTemplate, CustomerFeedback


class TicketResponseInline(admin.TabularInline):
    model = TicketResponse
    extra = 1
    readonly_fields = ['created_at']
    fields = ['message', 'is_staff', 'user', 'created_at']

    def save_model(self, request, obj, form, change):
        if not obj.user:
            obj.user = request.user
        if not obj.is_staff:
            obj.is_staff = request.user.is_staff
        super().save_model(request, obj, form, change)


@admin.register(SupportTicket)
class SupportTicketAdmin(admin.ModelAdmin):
    list_display = ['id', 'subject', 'name', 'email', 'status', 'priority', 'created_at', 'response_count']
    list_filter = ['status', 'priority', 'created_at']
    search_fields = ['name', 'email', 'subject', 'message']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [TicketResponseInline]
    actions = ['mark_as_resolved', 'mark_as_in_progress', 'send_bulk_response']

    fieldsets = (
        ('Customer Information', {
            'fields': ('name', 'email', 'user', 'order')
        }),
        ('Ticket Details', {
            'fields': ('subject', 'message', 'status', 'priority')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def response_count(self, obj):
        count = obj.responses.count()
        return format_html(
            '<span style="color: {};">{}</span>',
            'green' if count > 0 else 'red',
            count
        )
    response_count.short_description = 'Responses'

    def mark_as_resolved(self, request, queryset):
        updated = queryset.update(status='resolved')
        self.message_user(request, f'{updated} tickets marked as resolved.')
    mark_as_resolved.short_description = 'Mark selected tickets as resolved'

    def mark_as_in_progress(self, request, queryset):
        updated = queryset.update(status='in_progress')
        self.message_user(request, f'{updated} tickets marked as in progress.')
    mark_as_in_progress.short_description = 'Mark selected tickets as in progress'

    def send_bulk_response(self, request, queryset):
        # This would be implemented with a custom form in a real application
        # For simplicity, we'll just show a message
        self.message_user(
            request,
            'Bulk response functionality would be implemented with a custom form.',
            level=messages.INFO
        )
    send_bulk_response.short_description = 'Send bulk response to selected tickets'

    def save_formset(self, request, form, formset, change):
        instances = formset.save(commit=False)
        for instance in instances:
            if isinstance(instance, TicketResponse):
                if not instance.user:
                    instance.user = request.user
                if request.user.is_staff:
                    instance.is_staff = True

                # Send email notification to customer when staff responds
                if instance.is_staff:
                    try:
                        ticket = instance.ticket
                        subject = f"Response to your support ticket: {ticket.subject}"
                        message = f"""
                        Dear {ticket.name},

                        We have responded to your support ticket regarding "{ticket.subject}".

                        Response:
                        {instance.message}

                        Please reply to this email if you have any further questions.

                        Thank you,
                        PickMeTrend Support Team
                        <EMAIL>
                        +91 9353014895
                        """
                        from_email = settings.SUPPORT_EMAIL
                        recipient_list = [ticket.email]

                        send_mail(subject, message, from_email, recipient_list, fail_silently=True)
                    except Exception as e:
                        # Log the error but don't fail the save
                        print(f"Error sending email notification: {str(e)}")

            instance.save()
        formset.save_m2m()


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'template_type', 'subject', 'is_active', 'updated_at']
    list_filter = ['template_type', 'is_active', 'created_at']
    search_fields = ['name', 'subject', 'html_content', 'text_content']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'template_type', 'subject', 'is_active')
        }),
        ('Content', {
            'fields': ('html_content', 'text_content'),
            'description': 'Use {{ variable }} syntax for dynamic content. Available variables depend on the template type.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(CustomerFeedback)
class CustomerFeedbackAdmin(admin.ModelAdmin):
    list_display = ['order', 'user', 'rating_display', 'created_at']
    list_filter = ['rating', 'created_at']
    search_fields = ['order__id', 'user__email', 'comment']
    readonly_fields = ['order', 'user', 'rating', 'comment', 'created_at']

    def rating_display(self, obj):
        stars = '★' * obj.rating + '☆' * (5 - obj.rating)
        colors = {
            1: '#FF4136',  # Red
            2: '#FF851B',  # Orange
            3: '#FFDC00',  # Yellow
            4: '#2ECC40',  # Green
            5: '#3D9970',  # Olive
        }
        return format_html('<span style="color: {};">{}</span>', colors.get(obj.rating, 'black'), stars)
    rating_display.short_description = 'Rating'

    def has_add_permission(self, request):
        return False  # Prevent adding feedback through admin
