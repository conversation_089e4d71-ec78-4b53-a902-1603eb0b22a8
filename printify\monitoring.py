"""
Printify API Monitoring and Fallback System
"""
import logging
import time
from datetime import datetime, timedelta
from django.core.cache import cache
from django.conf import settings
from .api_client import PrintifyAPIClient

logger = logging.getLogger(__name__)

class PrintifyMonitor:
    """Monitor Printify API health and provide fallback mechanisms"""
    
    CACHE_KEY_API_STATUS = 'printify_api_status'
    CACHE_KEY_LAST_SUCCESS = 'printify_last_success'
    CACHE_KEY_ERROR_COUNT = 'printify_error_count'
    CACHE_TIMEOUT = 300  # 5 minutes
    
    def __init__(self):
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Printify client with error handling"""
        try:
            self.client = PrintifyAPIClient()
            logger.info("Printify client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Printify client: {str(e)}")
            self.client = None
    
    def check_api_health(self):
        """Check Printify API health"""
        if not self.client:
            self._initialize_client()
            if not self.client:
                return False, "Client initialization failed"
        
        try:
            start_time = time.time()
            shops = self.client.get_shops()
            response_time = time.time() - start_time
            
            # Update cache with success
            cache.set(self.CACHE_KEY_API_STATUS, 'healthy', self.CACHE_TIMEOUT)
            cache.set(self.CACHE_KEY_LAST_SUCCESS, datetime.now(), self.CACHE_TIMEOUT)
            cache.set(self.CACHE_KEY_ERROR_COUNT, 0, self.CACHE_TIMEOUT)
            
            logger.info(f"Printify API health check passed ({response_time:.2f}s)")
            return True, f"API healthy, {len(shops)} shops, {response_time:.2f}s response time"
            
        except Exception as e:
            # Update cache with error
            error_count = cache.get(self.CACHE_KEY_ERROR_COUNT, 0) + 1
            cache.set(self.CACHE_KEY_API_STATUS, 'unhealthy', self.CACHE_TIMEOUT)
            cache.set(self.CACHE_KEY_ERROR_COUNT, error_count, self.CACHE_TIMEOUT)
            
            logger.error(f"Printify API health check failed (error #{error_count}): {str(e)}")
            return False, f"API unhealthy: {str(e)}"
    
    def get_api_status(self):
        """Get current API status from cache"""
        status = cache.get(self.CACHE_KEY_API_STATUS, 'unknown')
        last_success = cache.get(self.CACHE_KEY_LAST_SUCCESS)
        error_count = cache.get(self.CACHE_KEY_ERROR_COUNT, 0)
        
        return {
            'status': status,
            'last_success': last_success,
            'error_count': error_count,
            'is_healthy': status == 'healthy'
        }
    
    def safe_api_call(self, method_name, *args, **kwargs):
        """Make a safe API call with fallback handling"""
        if not self.client:
            logger.error("Printify client not available")
            return None
        
        try:
            method = getattr(self.client, method_name)
            result = method(*args, **kwargs)
            
            # Reset error count on success
            cache.set(self.CACHE_KEY_ERROR_COUNT, 0, self.CACHE_TIMEOUT)
            cache.set(self.CACHE_KEY_LAST_SUCCESS, datetime.now(), self.CACHE_TIMEOUT)
            
            return result
            
        except Exception as e:
            error_count = cache.get(self.CACHE_KEY_ERROR_COUNT, 0) + 1
            cache.set(self.CACHE_KEY_ERROR_COUNT, error_count, self.CACHE_TIMEOUT)
            
            logger.error(f"Printify API call failed ({method_name}): {str(e)}")
            
            # If too many errors, mark as unhealthy
            if error_count >= 5:
                cache.set(self.CACHE_KEY_API_STATUS, 'unhealthy', self.CACHE_TIMEOUT)
            
            return None

class PrintifyFallbackManager:
    """Manage fallback strategies when Printify API is unavailable"""
    
    def __init__(self):
        self.monitor = PrintifyMonitor()
    
    def get_products_with_fallback(self, shop_id):
        """Get products with fallback to cached/database data"""
        # Try API first
        products = self.monitor.safe_api_call('get_products', shop_id)
        
        if products is not None:
            # Cache successful response
            cache.set(f'printify_products_{shop_id}', products, 3600)  # 1 hour
            return products, 'api'
        
        # Fallback to cache
        cached_products = cache.get(f'printify_products_{shop_id}')
        if cached_products:
            logger.warning("Using cached Printify products (API unavailable)")
            return cached_products, 'cache'
        
        # Fallback to database
        from .models import PrintifyProduct
        db_products = list(PrintifyProduct.objects.all().values(
            'printify_id', 'title', 'description', 'images_json'
        ))
        
        if db_products:
            logger.warning("Using database Printify products (API and cache unavailable)")
            return db_products, 'database'
        
        logger.error("No fallback data available for Printify products")
        return [], 'none'
    
    def get_product_with_fallback(self, shop_id, product_id):
        """Get single product with fallback"""
        # Try API first
        product = self.monitor.safe_api_call('get_product', shop_id, product_id)
        
        if product is not None:
            # Cache successful response
            cache.set(f'printify_product_{product_id}', product, 3600)
            return product, 'api'
        
        # Fallback to cache
        cached_product = cache.get(f'printify_product_{product_id}')
        if cached_product:
            logger.warning(f"Using cached product {product_id} (API unavailable)")
            return cached_product, 'cache'
        
        # Fallback to database
        from .models import PrintifyProduct
        try:
            db_product = PrintifyProduct.objects.get(printify_id=product_id)
            product_data = {
                'id': db_product.printify_id,
                'title': db_product.title,
                'description': db_product.description,
                'images': db_product.images_json,
                'variants': db_product.variants_json
            }
            logger.warning(f"Using database product {product_id} (API and cache unavailable)")
            return product_data, 'database'
        except PrintifyProduct.DoesNotExist:
            pass
        
        logger.error(f"No fallback data available for product {product_id}")
        return None, 'none'

# Global instances
printify_monitor = PrintifyMonitor()
printify_fallback = PrintifyFallbackManager()

def get_printify_status():
    """Get comprehensive Printify status"""
    status = printify_monitor.get_api_status()
    health_check = printify_monitor.check_api_health()
    
    return {
        'api_status': status,
        'health_check': {
            'success': health_check[0],
            'message': health_check[1]
        },
        'fallback_available': True,  # We always have fallback mechanisms
        'timestamp': datetime.now().isoformat()
    }

def safe_printify_call(method_name, *args, **kwargs):
    """Global function for safe Printify API calls"""
    return printify_monitor.safe_api_call(method_name, *args, **kwargs)

def get_products_safely(shop_id):
    """Global function to get products with fallback"""
    return printify_fallback.get_products_with_fallback(shop_id)

def get_product_safely(shop_id, product_id):
    """Global function to get single product with fallback"""
    return printify_fallback.get_product_with_fallback(shop_id, product_id)
