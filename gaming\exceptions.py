"""
Custom exceptions for the gaming module
"""

class SuspiciousActivity(Exception):
    """Raised when suspicious gaming activity is detected"""
    pass

class TokenValidationError(Exception):
    """Raised when token validation fails"""
    pass

class GameSessionError(Exception):
    """Base exception for game session errors"""
    pass

class SessionTimeoutError(GameSessionError):
    """Raised when a game session times out"""
    pass

class InvalidGameStateError(GameSessionError):
    """Raised when game state is invalid"""
    pass 