{% comment %}
This is a template for integrating a Tidio chatbot into your website.
Include this template in your base.html file to add the chatbot to all pages.

Example usage in base.html:
{% include 'customer_communication/chatbot_integration.html' %}
{% endcomment %}

<!-- Tidio Chat Integration -->
<script src="//code.tidio.co/YOUR_TIDIO_KEY.js" async></script>

<!-- Customization Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // This function will run once Tidio is loaded
        function setupTidio() {
            if (window.tidioChatApi) {
                // Set custom visitor data
                window.tidioChatApi.setVisitorData({
                    // You can pass user information if available
                    {% if user.is_authenticated %}
                    name: "{{ user.get_full_name|default:user.username }}",
                    email: "{{ user.email }}",
                    {% endif %}
                    // Add any other custom data
                    source: "PickMeTrend Website"
                });
                
                // Set custom labels/tags
                window.tidioChatApi.setTags(["pickmetrend", "ecommerce"]);
                
                // Example of pre-chat form configuration
                window.tidioChatApi.on('ready', function() {
                    // You can customize the pre-chat form
                    window.tidioChatApi.setPreChatData({
                        fields: [
                            {
                                name: 'name',
                                required: true
                            },
                            {
                                name: 'email',
                                required: true
                            },
                            {
                                name: 'orderNumber',
                                label: 'Order Number (if applicable)',
                                required: false
                            }
                        ]
                    });
                });
                
                // Example of how to open the chat automatically on specific pages
                {% if request.path == '/contact/' or request.path == '/support/' %}
                setTimeout(function() {
                    window.tidioChatApi.open();
                }, 3000); // Open after 3 seconds
                {% endif %}
            } else {
                // If Tidio isn't loaded yet, try again in a moment
                setTimeout(setupTidio, 500);
            }
        }
        
        // Start the setup process
        setupTidio();
    });
</script>

{% comment %}
Alternative: Dialogflow Integration

To use Dialogflow instead of Tidio, replace the above code with:

<!-- Dialogflow Messenger Integration -->
<script src="https://www.gstatic.com/dialogflow-console/fast/messenger/bootstrap.js?v=1"></script>
<df-messenger
  intent="WELCOME"
  chat-title="PickMeTrend Support"
  agent-id="YOUR_DIALOGFLOW_AGENT_ID"
  language-code="en">
</df-messenger>
<style>
  df-messenger {
    z-index: 999;
    position: fixed;
    bottom: 16px;
    right: 16px;
  }
  df-messenger-chat {
    width: 350px;
    height: 450px;
  }
</style>
{% endcomment %}
