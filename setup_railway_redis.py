#!/usr/bin/env python3
"""
Railway Redis Setup and Verification Script
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')

# Setup Django
django.setup()

def check_railway_environment():
    """Check if we're running in Railway environment"""
    railway_vars = [
        'RAILWAY_ENVIRONMENT',
        'RAILWAY_PROJECT_ID',
        'RAILWAY_SERVICE_ID',
        'REDIS_URL'
    ]
    
    print("🔍 Checking Railway Environment Variables:")
    for var in railway_vars:
        value = os.environ.get(var)
        if value:
            if 'password' in var.lower() or 'secret' in var.lower():
                print(f"  ✅ {var}: {'*' * len(value)}")
            else:
                print(f"  ✅ {var}: {value}")
        else:
            print(f"  ❌ {var}: Not set")
    
    return bool(os.environ.get('RAILWAY_ENVIRONMENT'))

def check_redis_configuration():
    """Check Redis configuration"""
    from dropshipping_backend.settings import REDIS_URL, REDIS_AVAILABLE
    
    print("\n🔍 Checking Redis Configuration:")
    print(f"  Redis URL: {REDIS_URL}")
    print(f"  Redis Available: {REDIS_AVAILABLE}")
    
    # Check if it's Railway Redis
    if 'railway.internal' in REDIS_URL:
        print("  ✅ Using Railway Redis")
    elif 'localhost' in REDIS_URL:
        print("  ⚠️ Using Local Redis (development)")
    else:
        print("  ℹ️ Using External Redis")
    
    return REDIS_AVAILABLE

def test_redis_connection():
    """Test Redis connection"""
    from dropshipping_backend.settings import get_redis_connection
    
    print("\n🧪 Testing Redis Connection:")
    
    try:
        redis_client = get_redis_connection()
        
        # Test basic operations
        redis_client.set('railway_test', 'success')
        value = redis_client.get('railway_test')
        redis_client.delete('railway_test')
        
        print("  ✅ Redis connection successful!")
        print(f"  ✅ Test value: {value}")
        
        # Get Redis info
        info = redis_client.info()
        print(f"  ✅ Redis version: {info.get('redis_version', 'Unknown')}")
        print(f"  ✅ Connected clients: {info.get('connected_clients', 'Unknown')}")
        print(f"  ✅ Used memory: {info.get('used_memory_human', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Redis connection failed: {e}")
        return False

def setup_railway_redis():
    """Setup instructions for Railway Redis"""
    print("\n🚀 Railway Redis Setup Instructions:")
    print("1. Go to your Railway project dashboard")
    print("2. Click 'New Service'")
    print("3. Select 'Redis' from the templates")
    print("4. Railway will automatically provision Redis")
    print("5. Your Django app will receive Redis environment variables")
    print("6. Deploy your app: railway up")
    print("7. Test connection: railway run python test_redis_connection.py")

def main():
    """Main function"""
    print("=" * 60)
    print("🚂 Railway Redis Setup and Verification")
    print("=" * 60)
    
    # Check Railway environment
    is_railway = check_railway_environment()
    
    # Check Redis configuration
    redis_configured = check_redis_configuration()
    
    # Test Redis connection
    redis_working = test_redis_connection()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Summary:")
    print(f"  Railway Environment: {'✅ Yes' if is_railway else '❌ No'}")
    print(f"  Redis Configured: {'✅ Yes' if redis_configured else '❌ No'}")
    print(f"  Redis Working: {'✅ Yes' if redis_working else '❌ No'}")
    
    if not is_railway:
        print("\n⚠️ Not running in Railway environment")
        print("This script is designed for Railway deployment")
    
    if not redis_working:
        print("\n🔧 Troubleshooting:")
        print("1. Ensure Redis service is added to Railway project")
        print("2. Check if services are in same Railway project")
        print("3. Verify environment variables are set")
        print("4. Check Railway logs for errors")
    
    if redis_working:
        print("\n🎉 Redis is working perfectly!")
        print("Your Django app can now use Redis for:")
        print("  - WebSocket gaming")
        print("  - Background tasks (Celery)")
        print("  - Caching and session storage")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 