import React from 'react';
import { ReturnRequest } from '../../services/api';

interface ReturnStatusProps {
  returnRequest: ReturnRequest;
}

const ReturnStatus: React.FC<ReturnStatusProps> = ({ returnRequest }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'approved':
        return '✅';
      case 'rejected':
        return '❌';
      case 'processing':
        return '🔄';
      case 'completed':
        return '✅';
      default:
        return '📋';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Return Request</h3>
        <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(returnRequest.status)}`}>
          {getStatusIcon(returnRequest.status)} {returnRequest.status_display}
        </span>
      </div>

      <div className="space-y-4">
        <div>
          <h4 className="text-sm font-medium text-gray-700">Order ID</h4>
          <p className="text-gray-900">{returnRequest.order.id}</p>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-700">Reason</h4>
          <p className="text-gray-900">{returnRequest.reason_display}</p>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-700">Details</h4>
          <p className="text-gray-900">{returnRequest.reason_detail}</p>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-700">Submitted</h4>
          <p className="text-gray-900">{new Date(returnRequest.created_at).toLocaleDateString()}</p>
        </div>

        {returnRequest.admin_response && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2">Response from our team</h4>
            <p className="text-blue-700">{returnRequest.admin_response}</p>
          </div>
        )}

        {returnRequest.refund_amount && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-green-800 mb-2">Refund Amount</h4>
            <p className="text-green-700 text-lg font-semibold">₹{returnRequest.refund_amount}</p>
          </div>
        )}

        {returnRequest.tracking_number && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-800 mb-2">Return Tracking Number</h4>
            <p className="text-gray-700 font-mono">{returnRequest.tracking_number}</p>
          </div>
        )}

        {returnRequest.processed_at && (
          <div>
            <h4 className="text-sm font-medium text-gray-700">Processed</h4>
            <p className="text-gray-900">{new Date(returnRequest.processed_at).toLocaleDateString()}</p>
          </div>
        )}
      </div>

      {returnRequest.status === 'pending' && (
        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800 text-sm">
            Your return request is being reviewed. We'll email you with an update within 24-48 hours.
          </p>
        </div>
      )}

      {returnRequest.status === 'approved' && (
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
          <p className="text-green-800 text-sm">
            Your return has been approved! Please check your email for return instructions.
          </p>
        </div>
      )}

      {returnRequest.status === 'rejected' && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm">
            Your return request was not approved. If you have questions, please contact our support team.
          </p>
        </div>
      )}
    </div>
  );
};

export default ReturnStatus;
