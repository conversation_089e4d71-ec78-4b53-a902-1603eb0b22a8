from django.db.models.signals import post_save
from django.dispatch import receiver
from orders.models import Order
from .tasks import send_order_confirmation_email
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Order)
def order_created_handler(sender, instance, created, **kwargs):
    """
    Signal handler to send order confirmation email when a new order is created.

    Args:
        sender: The model class (Order)
        instance: The actual instance being saved
        created: Boolean; True if a new record was created
        **kwargs: Additional keyword arguments
    """
    # Only send email for new orders that are paid
    if created and instance.is_paid:
        try:
            # Send the confirmation email asynchronously
            send_order_confirmation_email.delay(str(instance.id))
        except Exception as e:
            logger.error(f"Error sending order confirmation email: {str(e)}")
            # Try to send it synchronously as a fallback
            try:
                send_order_confirmation_email(str(instance.id))
            except Exception as e:
                logger.error(f"Error sending order confirmation email synchronously: {str(e)}")
