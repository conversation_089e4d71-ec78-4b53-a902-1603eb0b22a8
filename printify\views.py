import logging
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.conf import settings
from .models import PrintifyConfig, PrintifyProduct, PrintifyOrder
from .api_client import PrintifyAPIClient
from .serializers import (
    PrintifyConfigSerializer,
    PrintifyProductSerializer,
    PrintifyOrderSerializer,
    PrintifyCatalogSerializer
)
from products.models import Product, ProductImage, Category
from django.utils.text import slugify
import uuid
import decimal

logger = logging.getLogger(__name__)

class PrintifyViewSet(viewsets.ViewSet):
    """
    ViewSet for Printify API integration
    """
    permission_classes = [permissions.IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.client = None
        try:
            self.client = PrintifyAPIClient()
        except Exception as e:
            logger.error(f"Failed to initialize Printify API client: {str(e)}")

    @action(detail=False, methods=['get'])
    def shops(self, request):
        """
        Get a list of Printify shops
        """
        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            shops = self.client.get_shops()
            return Response(shops)
        except Exception as e:
            logger.error(f"Error getting Printify shops: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify shops: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def blueprints(self, request):
        """
        Get a list of Printify blueprints
        """
        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            blueprints = self.client.get_blueprints()
            return Response(blueprints)
        except Exception as e:
            logger.error(f"Error getting Printify blueprints: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify blueprints: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def blueprint_detail(self, request, pk=None):
        """
        Get details for a specific blueprint
        """
        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            blueprint = self.client.get_blueprint(pk)
            return Response(blueprint)
        except Exception as e:
            logger.error(f"Error getting Printify blueprint detail: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify blueprint detail: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def blueprint_providers(self, request, pk=None):
        """
        Get print providers for a specific blueprint
        """
        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            providers = self.client.get_blueprint_print_providers(pk)
            return Response(providers)
        except Exception as e:
            logger.error(f"Error getting Printify blueprint providers: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify blueprint providers: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def blueprint_variants(self, request):
        """
        Get variants for a specific blueprint and print provider
        """
        blueprint_id = request.query_params.get('blueprint_id')
        provider_id = request.query_params.get('provider_id')

        if not blueprint_id or not provider_id:
            return Response(
                {'detail': 'blueprint_id and provider_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            variants = self.client.get_blueprint_variants(blueprint_id, provider_id)
            return Response(variants)
        except Exception as e:
            logger.error(f"Error getting Printify blueprint variants: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify blueprint variants: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def blueprint_shipping(self, request):
        """
        Get shipping information for a specific blueprint and print provider
        """
        blueprint_id = request.query_params.get('blueprint_id')
        provider_id = request.query_params.get('provider_id')

        if not blueprint_id or not provider_id:
            return Response(
                {'detail': 'blueprint_id and provider_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            shipping = self.client.get_blueprint_shipping(blueprint_id, provider_id)
            return Response(shipping)
        except Exception as e:
            logger.error(f"Error getting Printify blueprint shipping: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify blueprint shipping: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def products(self, request):
        """
        Get products for a specific shop
        """
        shop_id = request.query_params.get('shop_id')

        if not shop_id:
            return Response(
                {'detail': 'shop_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            products = self.client.get_products(shop_id)
            return Response(products)
        except Exception as e:
            logger.error(f"Error getting Printify products: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify products: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def synced_products(self, request):
        """
        Get a list of products that have been synced from Printify
        """
        try:
            # Get query parameters for filtering
            search = request.query_params.get('search', '')
            blueprint_id = request.query_params.get('blueprint_id', '')
            provider_id = request.query_params.get('provider_id', '')

            # Start with all products
            queryset = PrintifyProduct.objects.all()

            # Apply filters
            if search:
                queryset = queryset.filter(title__icontains=search)

            if blueprint_id:
                queryset = queryset.filter(blueprint_id=blueprint_id)

            if provider_id:
                queryset = queryset.filter(print_provider_id=provider_id)

            # Order by most recently updated
            queryset = queryset.order_by('-updated_at')

            # Serialize the data
            serializer = PrintifyProductSerializer(queryset, many=True)

            return Response({
                'count': queryset.count(),
                'results': serializer.data
            })
        except Exception as e:
            logger.error(f"Error getting synced products: {str(e)}")
            return Response(
                {'detail': f'Error getting synced products: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def synced_product_detail(self, request, pk=None):
        """
        Get details for a specific synced product
        """
        try:
            # Try to get the product by ID
            try:
                product = PrintifyProduct.objects.get(pk=pk)
            except PrintifyProduct.DoesNotExist:
                return Response(
                    {'detail': f'Product with ID {pk} not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Serialize the product
            serializer = PrintifyProductSerializer(product)

            # Get the variants and images from the JSON fields
            variants = product.variants_json
            images = product.images_json

            # Return the product data with variants and images
            response_data = serializer.data
            response_data['variants'] = variants
            response_data['images'] = images

            return Response(response_data)
        except Exception as e:
            logger.error(f"Error getting synced product detail: {str(e)}")
            return Response(
                {'detail': f'Error getting synced product detail: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'], permission_classes=[permissions.AllowAny])
    def refresh_product_images(self, request, pk=None):
        """
        Fetch fresh images for a specific product from Printify API
        """
        try:
            # Get the shop_id from query parameters
            shop_id = request.query_params.get('shop_id')
            if not shop_id:
                return Response(
                    {'detail': 'shop_id is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Try to get the product by ID
            try:
                printify_product = PrintifyProduct.objects.get(pk=pk)
            except PrintifyProduct.DoesNotExist:
                return Response(
                    {'detail': f'Product with ID {pk} not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Initialize API client
            if not self.client:
                self.client = PrintifyAPIClient()

            # Get detailed product information from Printify API
            detailed_product = self.client.get_product(shop_id, printify_product.printify_id)

            # Extract images
            images_json = detailed_product.get('images', [])

            if not images_json:
                return Response(
                    {'detail': f'No images found for product {printify_product.printify_id}'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Update Printify product with images
            printify_product.images_json = images_json
            printify_product.save()

            logger.info(f'Updated Printify product with {len(images_json)} images')

            # Find corresponding Django product
            try:
                product = Product.objects.get(printify_id=printify_product.printify_id)

                # Delete existing product images
                deleted_count = ProductImage.objects.filter(product=product).delete()
                logger.info(f'Deleted {deleted_count[0]} existing product images')

                # Create new product images
                for i, image_data in enumerate(images_json):
                    image_url = image_data.get('src', '')
                    if image_url:
                        ProductImage.objects.create(
                            product=product,
                            image=image_url,
                            image_url=image_url,
                            is_primary=(i == 0),
                            alt_text=f"{product.name} - Image {i+1}"
                        )
                        logger.info(f'Added image {i+1}: {image_url}')

                logger.info(f'Updated Django product with {len(images_json)} images')

                # Return success response with images
                return Response({
                    'detail': f'Successfully refreshed images for product {printify_product.title}',
                    'product_id': printify_product.printify_id,
                    'images_count': len(images_json),
                    'images': images_json
                })

            except Product.DoesNotExist:
                # Return success response for Printify product only
                return Response({
                    'detail': f'Updated Printify product but no corresponding Django product found',
                    'product_id': printify_product.printify_id,
                    'images_count': len(images_json),
                    'images': images_json
                })

        except Exception as e:
            logger.error(f"Error refreshing product images: {str(e)}")
            return Response(
                {'detail': f'Error refreshing product images: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def sync_products(self, request):
        """
        Sync products from Printify to the local database
        """
        shop_id = request.data.get('shop_id')

        if not shop_id:
            return Response(
                {'detail': 'shop_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            # Get products from Printify
            logger.info(f"Fetching products from Printify for shop {shop_id}")
            printify_products = self.client.get_products(shop_id)

            if not isinstance(printify_products, list):
                # Handle case where the response might be paginated or have a different structure
                if isinstance(printify_products, dict) and 'data' in printify_products:
                    printify_products = printify_products.get('data', [])
                else:
                    logger.warning(f"Unexpected response format from Printify: {type(printify_products)}")
                    return Response(
                        {'detail': 'Unexpected response format from Printify API'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            # Track statistics
            stats = {
                'total': len(printify_products),
                'created': 0,
                'updated': 0,
                'skipped': 0,
                'errors': 0
            }

            # Process each product
            for product_data in printify_products:
                try:
                    printify_id = str(product_data.get('id'))
                    if not printify_id:
                        logger.warning(f"Product missing ID: {product_data}")
                        stats['skipped'] += 1
                        continue

                    # Get detailed product information
                    detailed_product = self.client.get_product(shop_id, printify_id)

                    # Extract relevant data
                    title = detailed_product.get('title', '')
                    description = detailed_product.get('description', '')
                    blueprint_id = str(detailed_product.get('blueprint_id', ''))
                    print_provider_id = str(detailed_product.get('print_provider_id', ''))

                    # Extract variants and images
                    variants_json = detailed_product.get('variants', [])
                    images_json = detailed_product.get('images', [])

                    # Try to find existing product
                    try:
                        product = PrintifyProduct.objects.get(printify_id=printify_id)
                        # Update existing product
                        product.title = title
                        product.description = description
                        product.blueprint_id = blueprint_id
                        product.print_provider_id = print_provider_id
                        product.variants_json = variants_json
                        product.images_json = images_json
                        product.save()

                        logger.info(f"Updated product: {title} (ID: {printify_id})")
                        stats['updated'] += 1
                    except PrintifyProduct.DoesNotExist:
                        # Create new product
                        product = PrintifyProduct.objects.create(
                            printify_id=printify_id,
                            title=title,
                            description=description,
                            blueprint_id=blueprint_id,
                            print_provider_id=print_provider_id,
                            variants_json=variants_json,
                            images_json=images_json
                        )

                        logger.info(f"Created product: {title} (ID: {printify_id})")
                        stats['created'] += 1

                except Exception as product_error:
                    logger.error(f"Error processing product {product_data.get('id', 'unknown')}: {str(product_error)}")
                    stats['errors'] += 1

            # Return statistics
            return Response({
                'detail': f"Sync completed. Created: {stats['created']}, Updated: {stats['updated']}, Skipped: {stats['skipped']}, Errors: {stats['errors']}",
                'stats': stats
            })

        except Exception as e:
            logger.error(f"Error syncing Printify products: {str(e)}")
            return Response(
                {'detail': f'Error syncing Printify products: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def create_product(self, request):
        """
        Create a new product
        """
        shop_id = request.data.get('shop_id')
        product_data = request.data.get('product_data')

        if not shop_id or not product_data:
            return Response(
                {'detail': 'shop_id and product_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            product = self.client.create_product(shop_id, product_data)
            return Response(product, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.error(f"Error creating Printify product: {str(e)}")
            return Response(
                {'detail': f'Error creating Printify product: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def publish_product(self, request):
        """
        Publish a product
        """
        shop_id = request.data.get('shop_id')
        product_id = request.data.get('product_id')
        publish_data = request.data.get('publish_data')

        if not shop_id or not product_id or not publish_data:
            return Response(
                {'detail': 'shop_id, product_id, and publish_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            result = self.client.publish_product(shop_id, product_id, publish_data)
            return Response(result)
        except Exception as e:
            logger.error(f"Error publishing Printify product: {str(e)}")
            return Response(
                {'detail': f'Error publishing Printify product: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def orders(self, request):
        """
        Get orders for a specific shop
        """
        shop_id = request.query_params.get('shop_id')

        if not shop_id:
            return Response(
                {'detail': 'shop_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            params = {}
            for key in ['limit', 'page', 'status']:
                if key in request.query_params:
                    params[key] = request.query_params[key]

            orders = self.client.get_orders(shop_id, params)
            return Response(orders)
        except Exception as e:
            logger.error(f"Error getting Printify orders: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify orders: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def create_order(self, request):
        """
        Create a new order
        """
        shop_id = request.data.get('shop_id')
        order_data = request.data.get('order_data')

        if not shop_id or not order_data:
            return Response(
                {'detail': 'shop_id and order_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            order = self.client.create_order(shop_id, order_data)
            return Response(order, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.error(f"Error creating Printify order: {str(e)}")
            return Response(
                {'detail': f'Error creating Printify order: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def calculate_shipping(self, request):
        """
        Calculate shipping costs
        """
        shop_id = request.data.get('shop_id')
        shipping_data = request.data.get('shipping_data')

        if not shop_id or not shipping_data:
            return Response(
                {'detail': 'shop_id and shipping_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            result = self.client.calculate_shipping(shop_id, shipping_data)
            return Response(result)
        except Exception as e:
            logger.error(f"Error calculating Printify shipping: {str(e)}")
            return Response(
                {'detail': f'Error calculating Printify shipping: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def send_to_production(self, request):
        """
        Send an order to production
        """
        shop_id = request.data.get('shop_id')
        order_id = request.data.get('order_id')

        if not shop_id or not order_id:
            return Response(
                {'detail': 'shop_id and order_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            result = self.client.send_order_to_production(shop_id, order_id)
            return Response(result)
        except Exception as e:
            logger.error(f"Error sending Printify order to production: {str(e)}")
            return Response(
                {'detail': f'Error sending Printify order to production: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def uploads(self, request):
        """
        Get a list of uploaded images
        """
        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            uploads = self.client.get_uploads()
            return Response(uploads)
        except Exception as e:
            logger.error(f"Error getting Printify uploads: {str(e)}")
            return Response(
                {'detail': f'Error getting Printify uploads: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], permission_classes=[permissions.AllowAny])
    def refresh_all_product_images(self, request):
        """
        Fetch fresh images for all products from Printify API
        """
        try:
            # Get the shop_id from request data
            shop_id = request.data.get('shop_id')
            if not shop_id:
                return Response(
                    {'detail': 'shop_id is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get limit from request data (optional)
            limit = request.data.get('limit', None)
            if limit:
                try:
                    limit = int(limit)
                except ValueError:
                    return Response(
                        {'detail': 'limit must be an integer'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Initialize API client
            if not self.client:
                self.client = PrintifyAPIClient()

            # Get all Printify products
            printify_products = PrintifyProduct.objects.all().order_by('-updated_at')

            # Apply limit if specified
            if limit and limit > 0:
                printify_products = printify_products[:limit]

            total_products = printify_products.count()
            logger.info(f'Refreshing images for {total_products} products')

            # Track statistics
            stats = {
                'total': total_products,
                'updated': 0,
                'skipped': 0,
                'errors': 0,
                'images_added': 0
            }

            # Process each product
            for printify_product in printify_products:
                try:
                    logger.info(f'Processing product: {printify_product.printify_id}')

                    # Get detailed product information from Printify API
                    detailed_product = self.client.get_product(shop_id, printify_product.printify_id)

                    # Extract images
                    images_json = detailed_product.get('images', [])

                    if not images_json:
                        logger.warning(f'No images found for product {printify_product.printify_id}')
                        stats['skipped'] += 1
                        continue

                    # Update Printify product with images
                    printify_product.images_json = images_json
                    printify_product.save()

                    logger.info(f'Updated Printify product with {len(images_json)} images')

                    # Find corresponding Django product
                    try:
                        product = Product.objects.get(printify_id=printify_product.printify_id)

                        # Delete existing product images
                        deleted_count = ProductImage.objects.filter(product=product).delete()
                        logger.info(f'Deleted {deleted_count[0]} existing product images')

                        # Create new product images
                        for i, image_data in enumerate(images_json):
                            image_url = image_data.get('src', '')
                            if image_url:
                                ProductImage.objects.create(
                                    product=product,
                                    image=image_url,
                                    image_url=image_url,
                                    is_primary=(i == 0),
                                    alt_text=f"{product.name} - Image {i+1}"
                                )
                                stats['images_added'] += 1

                        logger.info(f'Updated Django product with {len(images_json)} images')
                        stats['updated'] += 1

                    except Product.DoesNotExist:
                        logger.warning(f'No Django product found for Printify ID: {printify_product.printify_id}')
                        stats['skipped'] += 1

                except Exception as e:
                    logger.error(f'Error processing product {printify_product.printify_id}: {str(e)}')
                    stats['errors'] += 1

            # Return statistics
            return Response({
                'detail': f"Refresh completed. Total: {stats['total']}, Updated: {stats['updated']}, Skipped: {stats['skipped']}, Errors: {stats['errors']}, Images added: {stats['images_added']}",
                'stats': stats
            })

        except Exception as e:
            logger.error(f"Error refreshing product images: {str(e)}")
            return Response(
                {'detail': f'Error refreshing product images: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def upload_image(self, request):
        """
        Upload an image
        """
        file_name = request.data.get('file_name')
        url = request.data.get('url')
        contents = request.data.get('contents')

        if not file_name:
            return Response(
                {'detail': 'file_name is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not url and not contents:
            return Response(
                {'detail': 'Either url or contents is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if not self.client:
                self.client = PrintifyAPIClient()

            if url:
                result = self.client.upload_image_url(file_name, url)
            else:
                result = self.client.upload_image_base64(file_name, contents)

            return Response(result, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.error(f"Error uploading image to Printify: {str(e)}")
            return Response(
                {'detail': f'Error uploading image to Printify: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def import_to_products(self, request):
        """
        Import Printify products to the Django products model
        """
        try:
            # Get parameters
            printify_ids = request.data.get('printify_ids', [])
            category_name = request.data.get('category', 'Printify Products')

            if not printify_ids:
                return Response(
                    {'detail': 'printify_ids is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get or create the category
            category, created = Category.objects.get_or_create(
                name=category_name,
                defaults={'slug': slugify(category_name)}
            )

            if created:
                logger.info(f"Created new category: {category_name}")

            # Track statistics
            stats = {
                'total': len(printify_ids),
                'created': 0,
                'updated': 0,
                'skipped': 0,
                'errors': 0
            }

            # Process each product
            for printify_id in printify_ids:
                try:
                    # Get the Printify product
                    try:
                        printify_product = PrintifyProduct.objects.get(printify_id=printify_id)
                    except PrintifyProduct.DoesNotExist:
                        logger.warning(f"Printify product with ID {printify_id} not found")
                        stats['skipped'] += 1
                        continue

                    # Generate a slug from the title
                    slug = slugify(printify_product.title)

                    # Check if the product already exists
                    existing_product = Product.objects.filter(printify_id=printify_id).first()

                    if existing_product:
                        # Update existing product
                        existing_product.name = printify_product.title
                        existing_product.description = printify_product.description
                        existing_product.slug = slug
                        existing_product.variants_json = printify_product.variants_json  # Update variants

                        # Get the first variant for pricing
                        variants = printify_product.variants_json
                        if variants and len(variants) > 0:
                            variant = variants[0]
                            price = variant.get('price', 0)
                            try:
                                price = decimal.Decimal(price)
                            except (decimal.InvalidOperation, TypeError):
                                price = decimal.Decimal('0.00')

                            existing_product.price = price

                        existing_product.save()

                        # Update category
                        existing_product.categories.add(category)

                        # Update images
                        images = printify_product.images_json
                        if images and len(images) > 0:
                            # Clear existing images
                            existing_product.images.all().delete()

                            # Add new images
                            for i, image_data in enumerate(images):
                                image_url = image_data.get('src', '')
                                if image_url:
                                    ProductImage.objects.create(
                                        product=existing_product,
                                        image=image_url,
                                        image_url=image_url,
                                        is_primary=(i == 0),
                                        alt_text=f"{existing_product.name} - Image {i+1}"
                                    )

                        logger.info(f"Updated product: {printify_product.title} (ID: {printify_id})")
                        stats['updated'] += 1
                    else:
                        # Create new product
                        new_product = Product(
                            name=printify_product.title,
                            description=printify_product.description,
                            slug=slug,
                            printify_id=printify_id,
                            stock=100,  # Default stock
                            variants_json=printify_product.variants_json  # Include variants
                        )

                        # Get the first variant for pricing
                        variants = printify_product.variants_json
                        if variants and len(variants) > 0:
                            variant = variants[0]
                            price = variant.get('price', 0)
                            try:
                                price = decimal.Decimal(price)
                            except (decimal.InvalidOperation, TypeError):
                                price = decimal.Decimal('0.00')

                            new_product.price = price

                        new_product.save()

                        # Add category
                        new_product.categories.add(category)

                        # Add images
                        images = printify_product.images_json
                        if images and len(images) > 0:
                            for i, image_data in enumerate(images):
                                image_url = image_data.get('src', '')
                                if image_url:
                                    ProductImage.objects.create(
                                        product=new_product,
                                        image=image_url,
                                        image_url=image_url,
                                        is_primary=(i == 0),
                                        alt_text=f"{new_product.name} - Image {i+1}"
                                    )

                        logger.info(f"Created product: {printify_product.title} (ID: {printify_id})")
                        stats['created'] += 1

                except Exception as product_error:
                    logger.error(f"Error processing product {printify_id}: {str(product_error)}")
                    stats['errors'] += 1

            # Return statistics
            return Response({
                'detail': f"Import completed. Created: {stats['created']}, Updated: {stats['updated']}, Skipped: {stats['skipped']}, Errors: {stats['errors']}",
                'stats': stats
            })

        except Exception as e:
            logger.error(f"Error importing Printify products: {str(e)}")
            return Response(
                {'detail': f'Error importing Printify products: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], permission_classes=[permissions.AllowAny])
    def force_sync_products(self, request):
        """
        Force sync products from Printify (simple version)
        """
        try:
            from django.core.management import call_command
            from io import StringIO

            # Capture command output
            out = StringIO()

            # Get limit from request
            limit = request.data.get('limit', 5)

            # Run the management command
            call_command('force_sync_printify', limit=limit, stdout=out)

            # Get the output
            output = out.getvalue()

            return Response({
                'status': 'success',
                'message': 'Sync completed successfully',
                'output': output
            })

        except Exception as e:
            import traceback
            return Response({
                'status': 'error',
                'error': str(e),
                'traceback': traceback.format_exc()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
