from django.shortcuts import render
from rest_framework import viewsets, permissions
from .models import TeamMember
from .serializers import TeamMemberSerializer

def team_view(request):
    """
    View for the team page.

    Displays all team members in order of their display order.

    Args:
        request: The HTTP request object

    Returns:
        HttpResponse: The rendered team.html template
    """
    # Get all team members ordered by their display order
    # Note: We don't need to explicitly fetch team members here
    # since they're already available via the context processor

    return render(request, 'team_members/team.html', {
        'title': 'Our Team',
        'description': 'Meet the talented individuals behind PickMeTrend.'
    })


class TeamMemberViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for team members.

    Provides read-only access to team member data.
    """
    queryset = TeamMember.objects.all().order_by('order', 'name')
    serializer_class = TeamMemberSerializer
    permission_classes = [permissions.AllowAny]
    pagination_class = None  # Disable pagination for this endpoint
