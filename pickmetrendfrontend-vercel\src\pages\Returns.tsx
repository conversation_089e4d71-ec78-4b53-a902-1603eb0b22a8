import React from 'react';
import { COMPANY_CONTACT, getPhoneNumber } from '../utils/phoneUtils';

const Returns: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Return and Refund Policy</h1>

      <div className="prose prose-lg">
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Return and Replacement Policy</h2>
          <p>
            We want you to love what you receive! We gladly offer replacements or refunds if:
          </p>
          <ul className="list-disc pl-6 space-y-2 mt-2">
            <li>The product is defective</li>
            <li>The product was damaged during shipping</li>
            <li>You received the wrong product</li>
          </ul>
        </section>

        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">How to Request a Return or Replacement</h2>
          <ul className="list-disc pl-6 space-y-2">
            <li>Please contact us within 30 days of receiving your order.</li>
            <li>Provide clear photos of the damaged/incorrect product.</li>
            <li>Email us at: <a href={`mailto:${COMPANY_CONTACT.EMAIL}`} className="text-blue-600 hover:text-blue-800">{COMPANY_CONTACT.EMAIL}</a></li>
          </ul>
        </section>

        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Important</h2>
          <p>
            We do not accept returns for reasons such as:
          </p>
          <ul className="list-disc pl-6 space-y-2 mt-2">
            <li>Wrong size ordered</li>
            <li>Change of mind</li>
            <li>Incorrect shipping address provided by the customer</li>
          </ul>
        </section>

        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Refund Policy</h2>

          <h3 className="text-lg font-medium mt-4 mb-2">Eligibility</h3>
          <p>Refunds are issued if:</p>
          <ul className="list-disc pl-6 space-y-2 mt-2">
            <li>Products are damaged or defective</li>
            <li>There is a major printing issue</li>
            <li>The item is lost in transit</li>
          </ul>

          <h3 className="text-lg font-medium mt-4 mb-2">Non-Eligibility</h3>
          <p>Refunds are not issued for:</p>
          <ul className="list-disc pl-6 space-y-2 mt-2">
            <li>Buyer's remorse (changing your mind)</li>
            <li>Size fitting issues (please refer to our size chart)</li>
            <li>Wrong address at checkout</li>
          </ul>

          <h3 className="text-lg font-medium mt-4 mb-2">Refund Processing</h3>
          <p>
            Once approved, refunds will be processed to your original payment method. Please allow 5–10 business days for your bank to reflect the refund.
          </p>
        </section>

        <div className="bg-gray-50 p-6 rounded-lg mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Need Help?</h3>
          <p>
            If you have any questions or issues with your order, feel free to contact our customer support team:
          </p>
          <ul className="mt-2">
            <li>✉️ Email: <a href={`mailto:${COMPANY_CONTACT.EMAIL}`} className="text-blue-600 hover:text-blue-800">{COMPANY_CONTACT.EMAIL}</a></li>
            <li>📞 Phone/WhatsApp: <a href={`tel:${getPhoneNumber('link')}`} className="text-blue-600 hover:text-blue-800">{getPhoneNumber('display')}</a></li>
            <li>Business Hours: {COMPANY_CONTACT.BUSINESS_HOURS}</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Returns;