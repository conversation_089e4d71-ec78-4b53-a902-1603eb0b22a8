#!/usr/bin/env python
"""
Test script for Token Purchase System
=====================================

This script tests the token purchase system APIs and functionality.
Run this after setting up the system to verify everything works.

Usage:
    python test_token_purchase_system.py
"""

import os
import sys
import django
from decimal import Decimal

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Setup Django with local test settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'local_test_settings')
django.setup()

from django.contrib.auth.models import User
from wallet.models import Wallet, TokenPack, TokenPurchase, WalletTransaction


def test_token_purchase_system():
    """Test the complete token purchase system"""
    
    print("🧪 Testing Token Purchase System")
    print("=" * 50)
    
    # Test 1: Create/Get Token Packs
    print("\n1️⃣ Testing Token Packs...")
    
    # Create default token packs if they don't exist
    packs_data = [
        {'name': 'Starter Pack', 'tokens': 100, 'price_inr': Decimal('10.00'), 'sort_order': 1},
        {'name': 'Popular Pack', 'tokens': 500, 'price_inr': Decimal('45.00'), 'sort_order': 2},
        {'name': 'Best Value Pack', 'tokens': 1000, 'price_inr': Decimal('80.00'), 'sort_order': 3},
    ]
    
    for pack_data in packs_data:
        pack, created = TokenPack.objects.get_or_create(
            tokens=pack_data['tokens'],
            defaults=pack_data
        )
        if created:
            print(f"   ✅ Created: {pack.name} - {pack.tokens} tokens for ₹{pack.price_inr}")
        else:
            print(f"   ℹ️  Exists: {pack.name} - {pack.tokens} tokens for ₹{pack.price_inr}")
    
    # Test tokens per rupee calculation
    starter_pack = TokenPack.objects.get(tokens=100)
    print(f"   📊 Starter Pack value: {starter_pack.tokens_per_rupee:.2f} tokens per ₹")
    
    # Test 2: Create Test User and Wallet
    print("\n2️⃣ Testing User and Wallet...")
    
    test_user, created = User.objects.get_or_create(
        username='token_test_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Token',
            'last_name': 'Tester'
        }
    )
    
    if created:
        print(f"   ✅ Created test user: {test_user.username}")
    else:
        print(f"   ℹ️  Using existing test user: {test_user.username}")
    
    # Get or create wallet
    wallet, wallet_created = Wallet.objects.get_or_create(user=test_user)
    if wallet_created:
        print(f"   ✅ Created wallet for {test_user.username}")
    else:
        print(f"   ℹ️  Using existing wallet for {test_user.username}")
    
    print(f"   💰 Current balance: {wallet.balance} tokens")
    
    # Test 3: Create Mock Token Purchase
    print("\n3️⃣ Testing Token Purchase Creation...")
    
    # Create a pending purchase
    token_purchase = TokenPurchase.objects.create(
        user=test_user,
        token_pack=starter_pack,
        tokens_purchased=starter_pack.tokens,
        amount_paid=starter_pack.price_inr,
        payment_status='pending',
        razorpay_order_id='order_test_123456',
        notes='Test purchase for system verification'
    )
    
    print(f"   ✅ Created token purchase: {token_purchase.id}")
    print(f"   📦 Pack: {token_purchase.token_pack.name}")
    print(f"   🪙 Tokens: {token_purchase.tokens_purchased}")
    print(f"   💵 Amount: ₹{token_purchase.amount_paid}")
    print(f"   📊 Status: {token_purchase.payment_status}")
    
    # Test 4: Complete Purchase (Simulate Successful Payment)
    print("\n4️⃣ Testing Purchase Completion...")
    
    initial_balance = wallet.balance
    print(f"   💰 Initial balance: {initial_balance} tokens")
    
    # Simulate payment completion
    token_purchase.razorpay_payment_id = 'pay_test_789012'
    token_purchase.razorpay_signature = 'test_signature_hash'
    
    # Complete the purchase
    try:
        updated_wallet = token_purchase.complete_purchase()
        print(f"   ✅ Purchase completed successfully!")
        print(f"   💰 New balance: {updated_wallet.balance} tokens")
        print(f"   📈 Tokens added: {updated_wallet.balance - initial_balance}")
        print(f"   📊 Purchase status: {token_purchase.payment_status}")
        
        # Verify transaction was created
        latest_transaction = WalletTransaction.objects.filter(
            wallet=wallet,
            transaction_type='token_purchase'
        ).first()
        
        if latest_transaction:
            print(f"   ✅ Transaction recorded: {latest_transaction.amount} tokens")
            print(f"   📝 Description: {latest_transaction.description}")
        else:
            print(f"   ❌ No transaction record found!")
            
    except Exception as e:
        print(f"   ❌ Error completing purchase: {str(e)}")
    
    # Test 5: Verify Token Pack Properties
    print("\n5️⃣ Testing Token Pack Properties...")
    
    all_packs = TokenPack.objects.filter(is_active=True).order_by('sort_order')
    for pack in all_packs:
        print(f"   📦 {pack.name}:")
        print(f"      🪙 Tokens: {pack.tokens}")
        print(f"      💵 Price: ₹{pack.price_inr}")
        print(f"      📊 Value: {pack.tokens_per_rupee:.2f} tokens/₹")
        print(f"      🔢 Sort Order: {pack.sort_order}")
    
    # Test 6: Purchase History
    print("\n6️⃣ Testing Purchase History...")
    
    user_purchases = TokenPurchase.objects.filter(user=test_user).order_by('-created_at')
    print(f"   📊 Total purchases by {test_user.username}: {user_purchases.count()}")
    
    for purchase in user_purchases[:3]:  # Show last 3 purchases
        print(f"   💳 {purchase.created_at.strftime('%Y-%m-%d %H:%M')} - "
              f"{purchase.tokens_purchased} tokens for ₹{purchase.amount_paid} "
              f"({purchase.payment_status})")
    
    # Test 7: Wallet Transaction History
    print("\n7️⃣ Testing Wallet Transactions...")
    
    token_transactions = WalletTransaction.objects.filter(
        wallet=wallet,
        transaction_type='token_purchase'
    ).order_by('-created_at')
    
    print(f"   📊 Token purchase transactions: {token_transactions.count()}")
    
    for transaction in token_transactions[:3]:  # Show last 3
        print(f"   💰 {transaction.created_at.strftime('%Y-%m-%d %H:%M')} - "
              f"{transaction.amount:+d} tokens "
              f"(Balance: {transaction.balance_after})")
    
    # Test Summary
    print("\n" + "=" * 50)
    print("🎉 Token Purchase System Test Summary")
    print("=" * 50)
    
    final_wallet = Wallet.objects.get(user=test_user)
    total_purchases = TokenPurchase.objects.filter(user=test_user, payment_status='completed').count()
    total_spent = sum(
        float(p.amount_paid) for p in TokenPurchase.objects.filter(
            user=test_user, 
            payment_status='completed'
        )
    )
    
    print(f"✅ Test User: {test_user.username}")
    print(f"✅ Final Token Balance: {final_wallet.balance} tokens")
    print(f"✅ Total Purchases: {total_purchases}")
    print(f"✅ Total Amount Spent: ₹{total_spent:.2f}")
    print(f"✅ Available Token Packs: {TokenPack.objects.filter(is_active=True).count()}")
    
    print("\n🚀 Token Purchase System is working correctly!")
    print("\nNext steps:")
    print("1. Run: python manage.py create_token_packs")
    print("2. Test the frontend components")
    print("3. Test with real Razorpay payments")
    print("4. Deploy to production")


if __name__ == '__main__':
    try:
        test_token_purchase_system()
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
