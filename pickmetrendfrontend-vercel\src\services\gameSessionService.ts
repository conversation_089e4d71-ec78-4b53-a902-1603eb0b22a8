/**
 * Game Session Service
 * ===================
 * 
 * Comprehensive service for managing game sessions and tokens
 * for all 5 games with exact token rules implementation.
 */

import { api } from './api';

export interface GameSessionStart {
  success: boolean;
  session_id: string;
  message: string;
  balance: number;
  is_resume: boolean;
  error?: string;
}

export interface GameSessionComplete {
  success: boolean;
  tokens_earned: number;
  new_balance: number;
  balance_in_inr: number;
  transaction_type: string;
  description: string;
  can_play_more: boolean;
  status: string;
  result: string;
  error?: string;
}

export interface GameSessionStatus {
  success: boolean;
  session_id: string;
  game_type: string;
  status: 'active' | 'completed' | 'incomplete' | 'pending_replay';
  result?: 'win' | 'loss' | 'draw' | 'forfeit';
  tokens_deducted: number;
  tokens_awarded: number;
  net_change: number;
  created_at: string;
  can_resume: boolean;
  is_draw_replay: boolean;
  game_data: any;
  error?: string;
}

export interface ActiveSession {
  session_id: string;
  game_type: string;
  status: string;
  created_at: string;
  last_activity: string;
  is_draw_replay: boolean;
}

export interface GameHistory {
  session_id: string;
  game_type: string;
  result: string;
  tokens_change: number;
  completed_at: string;
  moves_count: number;
}

export interface UserStats {
  current_balance: number;
  total_sessions: number;
  completed_sessions: number;
  wins: number;
  losses: number;
  draws: number;
  forfeits: number;
  win_rate: number;
  total_tokens_earned: number;
  total_tokens_spent: number;
  net_tokens: number;
  game_breakdown: {
    [key: string]: {
      display_name: string;
      total_games: number;
      wins: number;
      losses: number;
      draws: number;
      forfeits: number;
    };
  };
}

export const gameSessionService = {
  /**
   * Start a new game session with 2 token participation fee
   */
  startGameSession: async (gameType: string): Promise<GameSessionStart> => {
    try {
      const response = await api.post('/api/gaming/session/start/', {
        game_type: gameType
      });
      return response.data;
    } catch (error: any) {
      console.error('Error starting game session:', error);
      return {
        success: false,
        session_id: '',
        message: '',
        balance: 0,
        is_resume: false,
        error: error.response?.data?.error || 'Failed to start game session'
      };
    }
  },

  /**
   * Complete a game session with result and token calculation
   */
  completeGameSession: async (
    sessionId: string,
    result: 'win' | 'loss' | 'draw' | 'forfeit',
    gameData?: any
  ): Promise<GameSessionComplete> => {
    try {
      const response = await api.post('/api/gaming/session/complete/', {
        session_id: sessionId,
        result,
        game_data: gameData || {}
      });
      return response.data;
    } catch (error: any) {
      console.error('Error completing game session:', error);
      return {
        success: false,
        tokens_earned: 0,
        new_balance: 0,
        balance_in_inr: 0,
        transaction_type: '',
        description: '',
        can_play_more: false,
        status: '',
        result: '',
        error: error.response?.data?.error || 'Failed to complete game session'
      };
    }
  },

  /**
   * Forfeit an active game session
   */
  forfeitGameSession: async (sessionId: string): Promise<GameSessionComplete> => {
    try {
      const response = await api.post('/api/gaming/session/forfeit/', {
        session_id: sessionId
      });
      return response.data;
    } catch (error: any) {
      console.error('Error forfeiting game session:', error);
      return {
        success: false,
        tokens_earned: 0,
        new_balance: 0,
        balance_in_inr: 0,
        transaction_type: '',
        description: '',
        can_play_more: false,
        status: '',
        result: '',
        error: error.response?.data?.error || 'Failed to forfeit game session'
      };
    }
  },

  /**
   * Get status of a specific game session
   */
  getSessionStatus: async (sessionId: string): Promise<GameSessionStatus> => {
    try {
      const response = await api.get(`/api/gaming/session/${sessionId}/`);
      return response.data;
    } catch (error: any) {
      console.error('Error getting session status:', error);
      return {
        success: false,
        session_id: '',
        game_type: '',
        status: 'active',
        tokens_deducted: 0,
        tokens_awarded: 0,
        net_change: 0,
        created_at: '',
        can_resume: false,
        is_draw_replay: false,
        game_data: {},
        error: error.response?.data?.error || 'Failed to get session status'
      };
    }
  },

  /**
   * Get all active or pending replay sessions for the user
   */
  getActiveSessions: async (): Promise<{ success: boolean; sessions: ActiveSession[]; error?: string }> => {
    try {
      const response = await api.get('/api/gaming/session/active/');
      return response.data;
    } catch (error: any) {
      console.error('Error getting active sessions:', error);
      return {
        success: false,
        sessions: [],
        error: error.response?.data?.error || 'Failed to get active sessions'
      };
    }
  },

  /**
   * Get user's game history
   */
  getGameHistory: async (
    gameType?: string,
    limit: number = 10
  ): Promise<{ success: boolean; history: GameHistory[]; error?: string }> => {
    try {
      const params = new URLSearchParams();
      if (gameType) params.append('game_type', gameType);
      params.append('limit', limit.toString());

      const response = await api.get(`/api/gaming/session/history/?${params}`);
      return response.data;
    } catch (error: any) {
      console.error('Error getting game history:', error);
      return {
        success: false,
        history: [],
        error: error.response?.data?.error || 'Failed to get game history'
      };
    }
  },

  /**
   * Get comprehensive user gaming statistics
   */
  getUserStats: async (): Promise<{ success: boolean; stats: UserStats; error?: string }> => {
    try {
      const response = await api.get('/api/gaming/session/stats/');
      return response.data;
    } catch (error: any) {
      console.error('Error getting user stats:', error);
      return {
        success: false,
        stats: {
          current_balance: 0,
          total_sessions: 0,
          completed_sessions: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          forfeits: 0,
          win_rate: 0,
          total_tokens_earned: 0,
          total_tokens_spent: 0,
          net_tokens: 0,
          game_breakdown: {}
        },
        error: error.response?.data?.error || 'Failed to get user stats'
      };
    }
  },

  /**
   * Check if user can play games (has enough tokens)
   */
  canPlayGames: async (): Promise<{ canPlay: boolean; balance: number; message: string }> => {
    try {
      const statsResult = await gameSessionService.getUserStats();
      
      if (statsResult.success) {
        const balance = statsResult.stats.current_balance;
        const canPlay = balance >= 2;
        
        return {
          canPlay,
          balance,
          message: canPlay 
            ? `You have ${balance} tokens. Ready to play!`
            : `You need 2 tokens to play. Current balance: ${balance} tokens.`
        };
      } else {
        return {
          canPlay: false,
          balance: 0,
          message: 'Unable to check token balance'
        };
      }
    } catch (error) {
      return {
        canPlay: false,
        balance: 0,
        message: 'Error checking token balance'
      };
    }
  },

  /**
   * Show exit warning for active games
   */
  showExitWarning: (onConfirm: () => void, onCancel: () => void) => {
    const confirmed = window.confirm(
      "⚠️ WARNING: Exiting now will forfeit your participation tokens!\n\n" +
      "• You will lose 2 tokens (participation fee)\n" +
      "• Game will be marked as incomplete\n" +
      "• No additional penalties will apply\n\n" +
      "Are you sure you want to exit?"
    );
    
    if (confirmed) {
      onConfirm();
    } else {
      onCancel();
    }
  },

  /**
   * Handle draw game continuation
   */
  handleDrawContinuation: (onContinue: () => void, onForfeit: () => void) => {
    const confirmed = window.confirm(
      "🤝 DRAW GAME!\n\n" +
      "• No tokens gained or lost\n" +
      "• You must replay with the same participation token\n" +
      "• No additional tokens will be deducted\n\n" +
      "Continue playing to win or lose?"
    );
    
    if (confirmed) {
      onContinue();
    } else {
      onForfeit();
    }
  }
};
