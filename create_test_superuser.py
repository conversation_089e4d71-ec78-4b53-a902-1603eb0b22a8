#!/usr/bin/env python
"""
Create Test Superuser for Token Purchase System
===============================================

This script creates a test superuser account for accessing the Django admin
interface to manage the token purchase system.

Usage:
    python create_test_superuser.py
"""

import os
import sys
import django

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Setup Django with local test settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'local_test_settings')
django.setup()

from django.contrib.auth.models import User
from wallet.models import Wallet

def create_test_superuser():
    """Create a test superuser account"""
    
    print("👤 Creating Test Superuser Account")
    print("=" * 40)
    
    # Test superuser credentials
    username = 'admin'
    email = '<EMAIL>'
    password = 'admin123'
    
    try:
        # Check if superuser already exists
        if User.objects.filter(username=username).exists():
            user = User.objects.get(username=username)
            print(f"ℹ️  Superuser '{username}' already exists")
        else:
            # Create superuser
            user = User.objects.create_superuser(
                username=username,
                email=email,
                password=password,
                first_name='Admin',
                last_name='User'
            )
            print(f"✅ Created superuser: {username}")
        
        # Ensure user has a wallet
        wallet, created = Wallet.objects.get_or_create(user=user)
        if created:
            # Give admin some test tokens
            wallet.add_tokens(1000, 'admin_bonus', 'Initial admin tokens for testing')
            print(f"✅ Created wallet with 1000 test tokens")
        else:
            print(f"ℹ️  Wallet exists with {wallet.balance} tokens")
        
        print("\n🎉 Test Superuser Ready!")
        print("=" * 40)
        print(f"👤 Username: {username}")
        print(f"🔑 Password: {password}")
        print(f"📧 Email: {email}")
        print(f"💰 Tokens: {wallet.balance}")
        print("\n🌐 Admin Interface: http://127.0.0.1:8000/admin/")
        print("🎮 Frontend Test: http://localhost:3000/token-purchase-test")
        
        return user
        
    except Exception as e:
        print(f"❌ Error creating superuser: {str(e)}")
        return None

def create_test_regular_user():
    """Create a test regular user for frontend testing"""
    
    print("\n👤 Creating Test Regular User")
    print("=" * 40)
    
    username = 'testuser'
    email = '<EMAIL>'
    password = 'test123'
    
    try:
        if User.objects.filter(username=username).exists():
            user = User.objects.get(username=username)
            print(f"ℹ️  Test user '{username}' already exists")
        else:
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name='Test',
                last_name='User'
            )
            print(f"✅ Created test user: {username}")
        
        # Ensure user has a wallet with low balance for testing
        wallet, created = Wallet.objects.get_or_create(user=user)
        if created:
            wallet.add_tokens(5, 'signup_bonus', 'Low balance for testing token purchase')
            print(f"✅ Created wallet with 5 test tokens (low balance)")
        else:
            print(f"ℹ️  Wallet exists with {wallet.balance} tokens")
        
        print(f"👤 Test User: {username}")
        print(f"🔑 Password: {password}")
        print(f"💰 Tokens: {wallet.balance} (low balance for testing)")
        
        return user
        
    except Exception as e:
        print(f"❌ Error creating test user: {str(e)}")
        return None

if __name__ == '__main__':
    print("🚀 Setting up Test Accounts for Token Purchase System")
    print("=" * 60)
    
    # Create superuser
    admin_user = create_test_superuser()
    
    # Create regular test user
    test_user = create_test_regular_user()
    
    if admin_user and test_user:
        print("\n🎉 All Test Accounts Created Successfully!")
        print("=" * 60)
        print("🔧 ADMIN ACCESS:")
        print("   URL: http://127.0.0.1:8000/admin/")
        print("   Username: admin")
        print("   Password: admin123")
        print("\n🎮 FRONTEND TESTING:")
        print("   URL: http://localhost:3000/token-purchase-test")
        print("   Username: testuser")
        print("   Password: test123")
        print("\n📊 What you can test:")
        print("   ✅ View token packs in admin")
        print("   ✅ Monitor token purchases")
        print("   ✅ Test purchase flow in frontend")
        print("   ✅ Verify token crediting")
        print("   ✅ Check transaction history")
    else:
        print("\n❌ Failed to create test accounts")
        sys.exit(1)
