from django.contrib import admin
from django.utils.html import format_html
from .models import TeamMember

@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    """
    Admin interface for TeamMember model.

    Provides a clean interface to manage team members with:
    - List display showing key information
    - Photo preview
    - Ordering by display order and name
    """
    list_display = ['name', 'role', 'photo_preview', 'has_linkedin', 'order']
    list_filter = ['role']
    search_fields = ['name', 'role']
    readonly_fields = ['photo_preview', 'created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'role', 'order')
        }),
        ('Profile Photo', {
            'fields': ('photo', 'photo_preview'),
            'description': 'Upload a professional photo of the team member. The image will be displayed on the team page.'
        }),
        ('Social Media', {
            'fields': ('linkedin_url',),
            'description': 'Add social media links for the team member.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def photo_preview(self, obj):
        """Display a preview of the team member's photo."""
        if obj.photo:
            return format_html(
                '<img src="{}" style="max-height: 50px; max-width: 50px; border-radius: 50%;" />',
                obj.photo.url
            )
        return "No photo"
    photo_preview.short_description = "Photo"

    def has_linkedin(self, obj):
        """Display whether the team member has a LinkedIn profile."""
        if obj.linkedin_url:
            return format_html(
                '<a href="{}" target="_blank" style="color: #0077B5;"><i class="fas fa-check"></i> Yes</a>',
                obj.linkedin_url
            )
        return "No"
    has_linkedin.short_description = "LinkedIn"
