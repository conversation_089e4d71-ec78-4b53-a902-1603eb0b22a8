import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { formatINR } from '../utils/currencyFormatter';

const Cart = () => {
  const { cart, loading, error, removeFromCart, updateQuantity: updateCartItem, fetchCart: refetchCart } = useCart();
  const { isAuthenticated, user } = useAuth();
  const [updatingItems, setUpdatingItems] = useState<Record<number, boolean>>({});

  // Memoize the calculateTotal function to prevent recalculation on every render
  const calculateTotal = useMemo(() => {
    return Array.isArray(cart)
      ? cart.reduce((total, item) => {
          // Use variant price if available, otherwise use product price
          const price = item.variant_price || item.product.price;
          return total + (price * item.quantity);
        }, 0)
      : 0;
  }, [cart]);

  // Use useCallback for event handlers to prevent recreation on every render
  const handleQuantityChange = useCallback(async (itemId: number, newQuantity: number) => {
    if (newQuantity > 0) {
      // Set local loading state for this specific item
      setUpdatingItems(prev => ({ ...prev, [itemId]: true }));

      // Update the cart
      await updateCartItem(itemId, newQuantity);

      // Clear loading state
      setUpdatingItems(prev => ({ ...prev, [itemId]: false }));
    }
  }, [updateCartItem]);

  // Event handler for the remove button
  const handleRemoveItem = useCallback(async (itemId: number) => {
    // Set local loading state for this specific item
    setUpdatingItems(prev => ({ ...prev, [itemId]: true }));

    // Remove the item
    await removeFromCart(itemId);

    // Clear loading state (though the item will be gone from the UI)
    setUpdatingItems(prev => {
      const newState = { ...prev };
      delete newState[itemId];
      return newState;
    });
  }, [removeFromCart]);

  // Track if initial loading has happened
  const [initialLoadDone, setInitialLoadDone] = useState(false);

  // Refetch cart only when component mounts, not on every re-render
  useEffect(() => {
    // Only fetch if authenticated and initial load hasn't happened
    if (isAuthenticated && !initialLoadDone) {
      refetchCart();
      setInitialLoadDone(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]); // Intentionally omit dependencies to prevent re-fetching on every change

  // Only show loading spinner on initial load, not on subsequent updates
  const showLoadingSpinner = loading && !initialLoadDone;

  if (showLoadingSpinner) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex justify-center items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-600 border-t-transparent mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Your Cart</h2>
          <p className="text-gray-600">Please wait while we fetch your items...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex justify-center items-center">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Oops! Something went wrong</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={refetchCart}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
            >
              <span className="mr-2">🔄</span>
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex justify-center items-center">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100 text-center">
            <span className="text-6xl mb-4 block">🔐</span>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Sign In Required</h2>
            <p className="text-gray-600 mb-6">You need to be signed in to view your cart and manage your items.</p>
            <Link
              to="/login?returnUrl=/cart"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
            >
              <span className="mr-2">🚀</span>
              Sign In to Continue
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Hero Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700">
        {/* Background decorative elements */}
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-64 h-64 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>

        <div className="relative container mx-auto px-4 py-12">
          <div className="flex flex-col lg:flex-row justify-between items-center text-white">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center mb-4">
                <span className="text-5xl mr-4">🛒</span>
                <div>
                  <h1 className="text-4xl font-bold mb-2">Your Shopping Cart</h1>
                  <p className="text-xl text-blue-100">Review your items and proceed to checkout</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">

        {Array.isArray(cart) && cart.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items Section */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                <div className="flex items-center mb-6">
                  <span className="text-3xl mr-4">📦</span>
                  <h2 className="text-2xl font-bold text-gray-900">Cart Items ({cart.length})</h2>
                </div>

                <div className="space-y-6">
                  {cart.map((item, index) => (
                    <div key={item.id} className={`flex flex-col md:flex-row p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors duration-200 ${index !== cart.length - 1 ? 'border-b border-gray-200' : ''}`}>
                      <div className="w-full md:w-1/3 mb-4 md:mb-0">
                        <img
                          src={item.product.image || 'https://via.placeholder.com/150'}
                          alt={item.product.name}
                          className="w-full h-40 object-cover rounded-xl shadow-md"
                          onError={(e) => {
                            console.error(`Failed to load image for ${item.product.name}`);
                            const target = e.target as HTMLImageElement;
                            target.onerror = null; // Prevent infinite loops
                            target.src = 'https://via.placeholder.com/150?text=No+Image';
                          }}
                        />
                      </div>
                      <div className="w-full md:w-2/3 md:pl-6 flex flex-col justify-between">
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 mb-3">{item.product.name}</h3>

                          {/* Display variant information if available */}
                          {item.variant_details && (
                            <div className="mb-3">
                              <div className="flex flex-wrap gap-2">
                                {item.variant_details.color && (
                                  <span className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                                    🎨 {item.variant_details.color}
                                  </span>
                                )}
                                {item.variant_details.size && (
                                  <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                                    📏 {item.variant_details.size}
                                  </span>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Display price - use variant price if available */}
                          <div className="mb-4">
                            <span className="text-2xl font-bold text-purple-600">
                              {formatINR(item.variant_price || item.product.price)}
                            </span>
                            {item.variant_details && (
                              <span className="text-sm text-gray-500 ml-2">
                                ({item.variant_details.title})
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                          {/* Quantity Controls */}
                          <div className="flex items-center bg-white rounded-xl shadow-md">
                            <button
                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                              className="px-4 py-2 text-purple-600 hover:bg-purple-50 rounded-l-xl transition-colors duration-200 font-semibold"
                              disabled={updatingItems[item.id]}
                            >
                              −
                            </button>
                            <div className="px-6 py-2 bg-gray-50 min-w-[60px] text-center font-semibold">
                              {updatingItems[item.id] ? (
                                <span className="inline-block w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></span>
                              ) : (
                                item.quantity
                              )}
                            </div>
                            <button
                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                              className="px-4 py-2 text-purple-600 hover:bg-purple-50 rounded-r-xl transition-colors duration-200 font-semibold"
                              disabled={updatingItems[item.id]}
                            >
                              +
                            </button>
                          </div>

                          {/* Remove Button */}
                          <button
                            onClick={() => handleRemoveItem(item.id)}
                            className="inline-flex items-center px-4 py-2 bg-red-50 text-red-600 hover:bg-red-100 rounded-xl transition-colors duration-200 font-medium"
                            disabled={updatingItems[item.id]}
                          >
                            {updatingItems[item.id] ? (
                              <>
                                <span className="inline-block w-4 h-4 mr-2 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></span>
                                Removing...
                              </>
                            ) : (
                              <>
                                <span className="mr-2">🗑️</span>
                                Remove
                              </>
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Order Summary Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100 sticky top-8">
                <div className="flex items-center mb-6">
                  <span className="text-3xl mr-4">💰</span>
                  <h2 className="text-2xl font-bold text-gray-900">Order Summary</h2>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-600 font-medium">Subtotal:</span>
                    <span className="text-xl font-bold text-gray-900">{formatINR(calculateTotal)}</span>
                  </div>
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-600 font-medium">Shipping:</span>
                    <span className="text-gray-500">Calculated at checkout</span>
                  </div>
                  <div className="flex justify-between items-center py-3">
                    <span className="text-gray-600 font-medium">Tax:</span>
                    <span className="text-gray-500">Calculated at checkout</span>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-4 mb-6">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-gray-900">Total:</span>
                    <span className="text-2xl font-bold text-purple-600">{formatINR(calculateTotal)}</span>
                  </div>
                </div>

                <Link
                  to="/checkout"
                  className="w-full block text-center bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg mb-4"
                >
                  <span className="mr-2">🚀</span>
                  Proceed to Checkout
                </Link>

                <div className="text-center">
                  <Link
                    to="/shop"
                    className="text-purple-600 hover:text-purple-700 font-medium"
                  >
                    ← Continue Shopping
                  </Link>
                </div>

                {/* Security Badge */}
                <div className="mt-6 p-4 bg-green-50 rounded-xl border border-green-200">
                  <div className="flex items-center justify-center text-green-700">
                    <span className="mr-2">🔒</span>
                    <span className="text-sm font-medium">Secure Checkout Guaranteed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto bg-white rounded-2xl shadow-2xl p-12 border border-gray-100">
              <div className="mb-8">
                <span className="text-8xl mb-6 block">🛒</span>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
              <p className="text-gray-600 mb-8 text-lg">Looks like you haven't added any products to your cart yet. Start shopping to fill it up!</p>

              <div className="space-y-4">
                <Link
                  to="/shop"
                  className="w-full inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🛍️</span>
                  Browse Products
                </Link>

                <Link
                  to="/game-dashboard"
                  className="w-full inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🎮</span>
                  Play Games & Earn Tokens
                </Link>
              </div>

              {/* Quick Stats */}
              <div className="mt-8 grid grid-cols-2 gap-4 text-center">
                <div className="p-4 bg-purple-50 rounded-xl">
                  <div className="text-2xl mb-1">🎯</div>
                  <div className="text-sm font-medium text-purple-700">Play Games</div>
                </div>
                <div className="p-4 bg-blue-50 rounded-xl">
                  <div className="text-2xl mb-1">🪙</div>
                  <div className="text-sm font-medium text-blue-700">Earn Tokens</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Cart;