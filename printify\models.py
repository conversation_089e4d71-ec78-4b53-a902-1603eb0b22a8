from django.db import models
from django.conf import settings

class PrintifyConfig(models.Model):
    """
    Model to store Printify API configuration
    """
    api_token = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Printify Config (Active: {self.is_active})"


class PrintifyProduct(models.Model):
    """
    Model to store Printify products
    """
    printify_id = models.CharField(max_length=255, unique=True)
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    blueprint_id = models.Char<PERSON>ield(max_length=255)
    print_provider_id = models.CharField(max_length=255)
    variants_json = models.JSONField(default=dict)
    images_json = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title


class PrintifyOrder(models.Model):
    """
    Model to store Printify orders
    """
    ORDER_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    )

    printify_id = models.CharField(max_length=255, unique=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='printify_orders')
    status = models.CharField(max_length=20, choices=ORDER_STATUS_CHOICES, default='pending')
    external_id = models.CharField(max_length=255, blank=True, null=True)
    shipping_method = models.CharField(max_length=255, blank=True, null=True)
    shipping_address_json = models.JSONField(default=dict)
    line_items_json = models.JSONField(default=dict)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_shipping = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_tax = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Order {self.printify_id} - {self.status}"
