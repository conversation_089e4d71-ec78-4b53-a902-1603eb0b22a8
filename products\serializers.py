from rest_framework import serializers
from .models import Category, Product, ProductImage, Review, ProductVariant
from django.contrib.auth.models import User
from django.conf import settings


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name']


class CategorySerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = ['id', 'name', 'slug', 'description', 'image']

    def get_image(self, obj):
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return f"{settings.MEDIA_URL}{obj.image}"
        return None


class ProductImageSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    class Meta:
        model = ProductImage
        fields = ['id', 'image', 'alt_text', 'is_primary']

    def get_image(self, obj):
        # If image_url is set, return that directly
        if obj.image_url:
            return obj.image_url

        # Otherwise try to get the image file URL
        if obj.image:
            try:
                request = self.context.get('request')
                if request:
                    return request.build_absolute_uri(obj.image.url)

                # If no request object, construct URL manually
                base_url = getattr(settings, 'BASE_URL', None)
                if not base_url:
                    # Determine base URL based on environment
                    if getattr(settings, 'DEBUG', True):
                        base_url = 'http://localhost:8000'
                    else:
                        base_url = 'https://pickmetrendofficial-render.onrender.com'
                return f"{base_url}{settings.MEDIA_URL}{obj.image}"
            except Exception as e:
                print(f"Error getting image URL for {obj}: {str(e)}")
                # If the image is a string and looks like a URL, return it directly
                if isinstance(obj.image, str) and obj.image.startswith(('http://', 'https://')):
                    return obj.image

                # Return a placeholder if all else fails
                return "https://via.placeholder.com/400x400?text=No+Image"

        return None

    def validate_image(self, value):
        """
        Additional validation for image uploads
        """
        # The model validator will handle basic validation
        # This method could be expanded for more complex validations
        return value


class ProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = [
            'id', 'variant_id', 'title', 'color', 'size',
            'gender', 'price', 'is_available'
        ]


class ReviewSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = Review
        fields = ['id', 'user', 'rating', 'comment', 'created_at']
        read_only_fields = ['id', 'created_at']

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class ProductListSerializer(serializers.ModelSerializer):
    categories = CategorySerializer(many=True, read_only=True)
    main_image = serializers.SerializerMethodField()
    discount_percentage = serializers.IntegerField(read_only=True)
    token_discount_available = serializers.BooleanField(read_only=True)
    token_discount_info = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'price', 'compare_price',
            'discount_percentage', 'stock', 'categories',
            'is_featured', 'main_image', 'created_at',
            'token_discount_available', 'token_discount_info'
        ]

    def get_main_image(self, obj):
        if obj.images.exists():
            image = obj.images.filter(is_primary=True).first() or obj.images.first()
            try:
                # If image_url is set, use that directly
                if image.image_url:
                    image_url = image.image_url
                # Otherwise try to get the image file URL
                elif image.image:
                    request = self.context.get('request')
                    if request:
                        image_url = request.build_absolute_uri(image.image.url)
                    else:
                        # If no request object, construct URL manually
                        base_url = getattr(settings, 'BASE_URL', None)
                        if not base_url:
                            # Determine base URL based on environment
                            if getattr(settings, 'DEBUG', True):
                                base_url = 'http://localhost:8000'
                            else:
                                base_url = 'https://pickmetrendofficial-render.onrender.com'
                        image_url = f"{base_url}{settings.MEDIA_URL}{image.image}"
                else:
                    # No image available
                    image_url = "https://via.placeholder.com/400x400?text=No+Image"

                return {
                    'id': image.id,
                    'image': image_url,
                    'alt_text': image.alt_text
                }
            except Exception as e:
                # Handle potential errors with image URLs
                print(f"Error getting image URL for product {obj.name}: {str(e)}")
                # Return a placeholder if all else fails
                return {
                    'id': image.id,
                    'image': "https://via.placeholder.com/400x400?text=No+Image",
                    'alt_text': "Image not available"
                }
        return None

    def get_token_discount_info(self, obj):
        """Get token discount information for the product"""
        if not obj.token_discount_available:
            return None

        return {
            'percentage': obj.token_discount_percentage,
            'max_amount': float(obj.token_discount_max_amount) if obj.token_discount_max_amount else None,
            'max_discount_for_quantity_1': obj.calculate_max_token_discount(1)
        }


class ProductDetailSerializer(serializers.ModelSerializer):
    categories = CategorySerializer(many=True, read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)
    reviews = ReviewSerializer(many=True, read_only=True)
    average_rating = serializers.SerializerMethodField()
    discount_percentage = serializers.IntegerField(read_only=True)
    variants = serializers.SerializerMethodField()
    token_discount_available = serializers.BooleanField(read_only=True)
    token_discount_info = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'description', 'price',
            'compare_price', 'discount_percentage', 'stock',
            'categories', 'is_featured', 'images', 'reviews',
            'average_rating', 'created_at', 'updated_at',
            'printify_id', 'variants', 'token_discount_available', 'token_discount_info'
        ]

    def get_variants(self, obj):
        """
        Return the product variants
        """
        # First try to get actual ProductVariant objects
        if hasattr(obj, 'variants') and obj.variants.exists():
            serializer = ProductVariantSerializer(obj.variants.all(), many=True)
            return serializer.data

        # Fallback to variants_json if no ProductVariant objects exist
        if obj.variants_json:
            return obj.variants_json

        return []

    def get_average_rating(self, obj):
        if obj.reviews.exists():
            total = sum(review.rating for review in obj.reviews.all())
            return round(total / obj.reviews.count(), 1)
        return 0

    def get_token_discount_info(self, obj):
        """Get detailed token discount information for the product"""
        if not obj.token_discount_available:
            return None

        return {
            'percentage': obj.token_discount_percentage,
            'max_amount': float(obj.token_discount_max_amount) if obj.token_discount_max_amount else None,
            'max_discount_for_quantity_1': obj.calculate_max_token_discount(1),
            'max_discount_for_quantity_2': obj.calculate_max_token_discount(2),
            'max_discount_for_quantity_3': obj.calculate_max_token_discount(3)
        }


class ProductCreateUpdateSerializer(serializers.ModelSerializer):
    categories = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(),
        many=True,
        required=False
    )

    class Meta:
        model = Product
        fields = [
            'name', 'description', 'price', 'compare_price',
            'stock', 'categories', 'is_featured', 'is_active',
            'allow_token_discount', 'token_discount_percentage',
            'token_discount_max_amount'
        ]

    def validate(self, data):
        # Validate that compare_price is greater than price if provided
        if 'compare_price' in data and data['compare_price'] is not None:
            if data['compare_price'] <= data.get('price', 0):
                data['compare_price'] = None

        # Validate token discount settings
        if data.get('allow_token_discount'):
            if 'token_discount_percentage' not in data:
                data['token_discount_percentage'] = 20  # Default value
            elif not (1 <= data['token_discount_percentage'] <= 100):
                raise serializers.ValidationError({
                    'token_discount_percentage': 'Must be between 1 and 100'
                })

        return data

    def create(self, validated_data):
        categories = validated_data.pop('categories', [])
        product = Product.objects.create(**validated_data)
        product.categories.set(categories)
        return product

    def update(self, instance, validated_data):
        categories = validated_data.pop('categories', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        if categories is not None:
            instance.categories.set(categories)
        
        return instance