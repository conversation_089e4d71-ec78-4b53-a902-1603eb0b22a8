from django.contrib import admin
from django.utils.html import format_html
from .models import SiteSettings

@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    """
    Admin interface for SiteSettings model.

    Provides a clean interface to manage site-wide settings with:
    - Image previews for logo and favicon
    - Readonly timestamps
    - Custom fieldsets for better organization
    """
    list_display = ['__str__', 'logo_preview', 'favicon_preview', 'updated_at']
    readonly_fields = ['created_at', 'updated_at', 'logo_preview', 'favicon_preview']

    fieldsets = (
        ('Site Branding', {
            'fields': ('site_logo', 'logo_preview', 'favicon', 'favicon_preview'),
            'description': 'Upload your site logo and favicon here. Images will be automatically resized if needed.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def logo_preview(self, obj):
        """Display a preview of the logo."""
        if obj.site_logo:
            return format_html(
                '<img src="{}" style="max-height: 50px; max-width: 200px;" />',
                obj.site_logo.url
            )
        return "No logo uploaded"
    logo_preview.short_description = "Logo Preview"

    def favicon_preview(self, obj):
        """Display a preview of the favicon."""
        if obj.favicon:
            return format_html(
                '<img src="{}" style="max-height: 32px; max-width: 32px;" />',
                obj.favicon.url
            )
        return "No favicon uploaded"
    favicon_preview.short_description = "Favicon Preview"

    def has_add_permission(self, request):
        """Only allow adding if no SiteSettings exists."""
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of SiteSettings."""
        return False
