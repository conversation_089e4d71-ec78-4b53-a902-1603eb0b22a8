# Generated by Django 5.0.2 on 2025-06-03 15:05

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wallet', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='wallettransaction',
            name='transaction_type',
            field=models.CharField(choices=[('game_win', 'Game Win'), ('game_draw', 'Game Draw'), ('game_participation', 'Game Participation'), ('game_loss', 'Game Loss'), ('referral_bonus', 'Referral Bonus'), ('spin_wheel', 'Spin the Wheel'), ('purchase_redemption', 'Purchase Redemption'), ('admin_adjustment', 'Admin Adjustment'), ('bonus', 'Bonus'), ('signup_bonus', 'Signup Bonus'), ('refill_bonus', 'Token Refill')], max_length=20),
        ),
        migrations.CreateModel(
            name='TokenRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('message', models.TextField(blank=True, help_text='Optional message from user', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=10)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('tokens_granted', models.PositiveIntegerField(default=0, help_text='Tokens granted if approved')),
                ('admin_notes', models.TextField(blank=True, help_text='Internal notes for admin', null=True)),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_token_requests', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='token_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Token Refill Request',
                'verbose_name_plural': 'Token Refill Requests',
                'ordering': ['-requested_at'],
            },
        ),
    ]
