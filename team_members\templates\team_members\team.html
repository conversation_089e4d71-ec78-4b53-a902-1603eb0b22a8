{% extends "base.html" %}
{% load static %}

{% block title %}{{ title }} | PickMeTrend{% endblock %}

{% block extra_css %}
<style>
    .team-member-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .team-member-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    .linkedin-button {
        background-color: #0077B5;
        transition: background-color 0.3s ease;
    }
    .linkedin-button:hover {
        background-color: #005582;
    }
    .team-photo {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 50%;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-12">
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">{{ title }}</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">{{ description }}</p>
    </div>
    
    {% if team_members %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for member in team_members %}
                <div class="team-member-card bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6 text-center">
                        {% if member.photo %}
                            <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="team-photo mx-auto mb-4">
                        {% else %}
                            <div class="team-photo mx-auto mb-4 bg-gray-200 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                        {% endif %}
                        
                        <h3 class="text-xl font-semibold mb-1">{{ member.name }}</h3>
                        <p class="text-gray-600 mb-4">{{ member.role }}</p>
                        
                        {% if member.linkedin_url %}
                            <a href="{{ member.linkedin_url }}" target="_blank" rel="noopener noreferrer" class="linkedin-button inline-block px-4 py-2 text-white rounded">
                                Connect on LinkedIn
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-12">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <h2 class="text-2xl font-semibold mb-2">No Team Members Found</h2>
            <p class="text-gray-600">Our team information is currently being updated. Please check back later.</p>
        </div>
    {% endif %}
    
    <div class="mt-16 text-center">
        <h2 class="text-2xl font-semibold mb-4">Join Our Team</h2>
        <p class="text-gray-600 max-w-2xl mx-auto mb-6">
            We're always looking for talented individuals to join our team. If you're passionate about fashion and technology, we'd love to hear from you.
        </p>
        <a href="/contact" class="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300">
            Contact Us
        </a>
    </div>
</div>
{% endblock %}
