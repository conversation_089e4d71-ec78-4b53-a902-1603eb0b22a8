import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useWallet } from '../hooks/useWallet';

const ModalDebugTest: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { wallet } = useWallet();
  const [showSimpleModal, setShowSimpleModal] = useState(false);
  const [showTokenModal, setShowTokenModal] = useState(false);

  // Simple test modal
  const SimpleModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
    if (!isOpen) return null;

    return (
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        style={{ zIndex: 9999 }}
      >
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <h2 className="text-2xl font-bold mb-4">🧪 Simple Test Modal</h2>
          <p className="text-gray-600 mb-6">
            This is a simple modal to test if modals work at all.
          </p>
          <div className="flex gap-4">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Close
            </button>
            <button
              onClick={() => {
                alert('Modal button clicked!');
                onClose();
              }}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Test Alert
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Token Purchase Modal Test
  const TokenModalTest = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
    if (!isOpen) return null;

    return (
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        style={{ zIndex: 9999 }}
      >
        <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white p-6 rounded-t-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-3xl mr-3">💰</span>
                <div>
                  <h2 className="text-2xl font-bold">Buy Tokens (Debug)</h2>
                  <p className="text-emerald-100">Current Balance: {wallet?.balance || 0} tokens</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-emerald-200 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">🧪 Debug Token Packs</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Mock token packs */}
              {[
                { name: 'Starter Pack', tokens: 100, price: 10 },
                { name: 'Popular Pack', tokens: 500, price: 45 },
                { name: 'Best Value Pack', tokens: 1000, price: 80 }
              ].map((pack, index) => (
                <div
                  key={index}
                  onClick={() => {
                    alert(`Selected: ${pack.name} - ${pack.tokens} tokens for ₹${pack.price}`);
                  }}
                  className="cursor-pointer border-2 border-gray-200 rounded-xl p-6 hover:border-emerald-300 hover:scale-105 transition-all duration-200"
                >
                  <div className="text-center">
                    <div className="text-3xl mb-2">🪙</div>
                    <h4 className="font-bold text-lg text-gray-900 mb-2">{pack.name}</h4>
                    <div className="text-2xl font-bold text-emerald-600 mb-1">
                      {pack.tokens} tokens
                    </div>
                    <div className="text-xl font-semibold text-gray-900 mb-2">
                      ₹{pack.price}
                    </div>
                    <div className="text-sm text-gray-600">
                      {(pack.tokens / pack.price).toFixed(1)} tokens per ₹
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-semibold text-yellow-800 mb-2">🔍 Debug Info:</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• User: {user?.username || 'Not logged in'}</li>
                <li>• Authenticated: {isAuthenticated ? 'Yes' : 'No'}</li>
                <li>• Wallet Balance: {wallet?.balance || 'N/A'} tokens</li>
                <li>• Modal Z-Index: 9999</li>
                <li>• Click any pack to test interaction</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🔍 Modal Debug Test</h1>
          <p className="text-gray-600">Testing if modals work properly</p>
        </div>

        {/* User Status */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">👤 User Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="font-semibold text-blue-800">Authentication</div>
              <div className="text-blue-600">{isAuthenticated ? '✅ Logged In' : '❌ Not Logged In'}</div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="font-semibold text-green-800">Username</div>
              <div className="text-green-600">{user?.username || 'N/A'}</div>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="font-semibold text-purple-800">Token Balance</div>
              <div className="text-purple-600">{wallet?.balance || 'N/A'} tokens</div>
            </div>
          </div>
        </div>

        {/* Modal Tests */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          
          {/* Simple Modal Test */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">🧪 Simple Modal Test</h2>
            <p className="text-gray-600 mb-4">Test if basic modals work at all</p>
            <button
              onClick={() => setShowSimpleModal(true)}
              className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 font-semibold"
            >
              Open Simple Modal
            </button>
          </div>

          {/* Token Modal Test */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">💰 Token Modal Test</h2>
            <p className="text-gray-600 mb-4">Test token purchase modal design</p>
            <button
              onClick={() => setShowTokenModal(true)}
              className="w-full bg-emerald-600 text-white px-4 py-3 rounded-lg hover:bg-emerald-700 font-semibold"
            >
              Open Token Modal (Debug)
            </button>
          </div>
        </div>

        {/* Import Test */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">📦 Component Import Test</h2>
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-2">Real Token Purchase Button Test</h3>
              <p className="text-gray-600 mb-4">This uses the actual TokenPurchaseButton component:</p>
              
              {isAuthenticated && wallet ? (
                <div>
                  <p className="text-sm text-gray-500 mb-2">Current balance: {wallet.balance} tokens</p>
                  <button
                    onClick={() => {
                      console.log('TokenPurchaseButton clicked');
                      alert('TokenPurchaseButton clicked! Check browser console for any errors.');
                    }}
                    className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-4 py-3 rounded-xl hover:from-emerald-700 hover:to-teal-700 font-semibold"
                  >
                    🪙 Test Buy Tokens (Manual)
                  </button>
                </div>
              ) : (
                <div className="text-red-600">
                  ❌ Please log in first to test token purchase
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-semibold text-yellow-800 mb-4">📋 Testing Instructions</h2>
          <ol className="list-decimal list-inside text-yellow-700 space-y-2">
            <li>First, test the "Simple Modal" to see if modals work at all</li>
            <li>Then test the "Token Modal (Debug)" to see the design</li>
            <li>Check browser console (F12) for any JavaScript errors</li>
            <li>If modals don't appear, check for CSS conflicts or z-index issues</li>
            <li>Make sure you're logged in for the real token purchase test</li>
          </ol>
        </div>
      </div>

      {/* Modals */}
      <SimpleModal isOpen={showSimpleModal} onClose={() => setShowSimpleModal(false)} />
      <TokenModalTest isOpen={showTokenModal} onClose={() => setShowTokenModal(false)} />
    </div>
  );
};

export default ModalDebugTest;
