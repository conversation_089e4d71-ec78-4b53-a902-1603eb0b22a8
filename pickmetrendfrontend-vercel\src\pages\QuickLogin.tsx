import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const QuickLogin: React.FC = () => {
  const { login, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleQuickLogin = async (username: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await login(username, password);
      
      // Redirect to wallet page after successful login
      navigate('/wallet');
      
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
          <div className="text-center">
            <span className="text-6xl mb-4 block">✅</span>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Already Logged In!</h1>
            <p className="text-gray-600 mb-6">Welcome back, {user?.username}!</p>
            
            <div className="space-y-3">
              <button
                onClick={() => navigate('/wallet')}
                className="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 font-semibold"
              >
                🪙 Go to Wallet
              </button>
              
              <button
                onClick={() => navigate('/token-purchase-test')}
                className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 font-semibold"
              >
                🧪 Test Token Purchase
              </button>
              
              <button
                onClick={() => navigate('/game-dashboard')}
                className="w-full bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 font-semibold"
              >
                🎮 Play Games
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <div className="text-center mb-8">
          <span className="text-6xl mb-4 block">🔐</span>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Quick Login</h1>
          <p className="text-gray-600">Test the token purchase system</p>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-700">
              <span className="mr-2">⚠️</span>
              <span>{error}</span>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 mb-2">🧪 Test Account</h3>
            <p className="text-sm text-blue-700 mb-3">Use this account to test token purchases:</p>
            <div className="text-sm text-blue-600">
              <div>👤 Username: <strong>testuser</strong></div>
              <div>🔑 Password: <strong>test123</strong></div>
              <div>💰 Balance: 200 tokens</div>
            </div>
            <button
              onClick={() => handleQuickLogin('testuser', 'test123')}
              disabled={loading}
              className="w-full mt-3 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 font-semibold"
            >
              {loading ? '🔄 Logging in...' : '🚀 Login as Test User'}
            </button>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold text-green-800 mb-2">🔧 Admin Account</h3>
            <p className="text-sm text-green-700 mb-3">Access admin interface:</p>
            <div className="text-sm text-green-600">
              <div>👤 Username: <strong>admin</strong></div>
              <div>🔑 Password: <strong>admin123</strong></div>
              <div>💰 Balance: 200 tokens</div>
            </div>
            <button
              onClick={() => handleQuickLogin('admin', 'admin123')}
              disabled={loading}
              className="w-full mt-3 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50 font-semibold"
            >
              {loading ? '🔄 Logging in...' : '🔧 Login as Admin'}
            </button>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <h3 className="font-semibold text-gray-800 mb-2">🎯 What to Test:</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Login with test account</li>
            <li>• Go to Wallet page</li>
            <li>• Click "Buy Tokens" button</li>
            <li>• Select a token pack</li>
            <li>• Test payment with card: 4111 1111 1111 1111</li>
            <li>• Verify tokens are added</li>
          </ul>
        </div>

        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="text-xs text-yellow-700">
            <strong>💡 Backend Status:</strong> Make sure Django server is running on http://127.0.0.1:8000
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickLogin;
