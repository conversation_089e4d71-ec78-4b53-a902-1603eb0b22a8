import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';
import { formatINR } from '../utils/currencyFormatter';
import { api } from '../services/api';
import WalletBalance from '../components/wallet/WalletBalance';

interface OrderSummary {
  id: number;
  order_number: string;
  total: number;
  status: string;
  created_at: string;
}

// Extended User interface to include missing properties
interface ExtendedUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  last_login?: string;
  date_joined?: string;
  order_count?: number;
}

const Dashboard = () => {
  const { user, isAuthenticated } = useAuth();
  const { cart, loading: cartLoading } = useCart();
  const [recentOrders, setRecentOrders] = useState<OrderSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchRecentOrders = async () => {
      if (!isAuthenticated) return;

      try {
        const response = await api.get('/api/orders/?limit=5');
        setRecentOrders(response.data.results || []);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching orders:', err);
        setError('Failed to load recent orders');
        setLoading(false);
      }
    };

    fetchRecentOrders();
  }, [isAuthenticated]);

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mb-8">
            <span className="text-6xl mb-4 block">🔐</span>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Access Required</h1>
            <p className="text-xl text-gray-600 mb-8">Please log in to view your dashboard</p>
          </div>
          <Link
            to="/login"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
          >
            <span className="mr-2">🚀</span>
            Login to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  // Cast user to ExtendedUser to use the additional properties
  const extendedUser = user as unknown as ExtendedUser;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Hero Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700">
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-64 h-64 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>

        <div className="relative container mx-auto px-4 py-12">
          <div className="text-white">
            <div className="flex items-center mb-4">
              <span className="text-5xl mr-4">👋</span>
              <div>
                <h1 className="text-4xl font-bold mb-2">Welcome back, {user.username}!</h1>
                <p className="text-xl text-blue-100">Manage your account, orders, and gaming rewards</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Account Information Card */}
          <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">👤</span>
              <h2 className="text-xl font-semibold text-gray-800">Account Info</h2>
            </div>
            <div className="space-y-2">
              <p className="text-gray-600"><span className="font-medium">Name:</span> {extendedUser.first_name || ''} {extendedUser.last_name || ''}</p>
              <p className="text-gray-600"><span className="font-medium">Email:</span> {extendedUser.email || ''}</p>
              <Link to="/profile/edit" className="inline-flex items-center mt-4 text-purple-600 hover:text-purple-700 font-medium">
                <span className="mr-1">✏️</span>
                Edit Profile
              </Link>
            </div>
          </div>

          {/* Cart Summary Card */}
          <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">🛒</span>
              <h2 className="text-xl font-semibold text-gray-800">Shopping Cart</h2>
            </div>
            {cartLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
              </div>
            ) : cart && cart.length > 0 ? (
              <div className="space-y-2">
                <p className="text-gray-600"><span className="font-medium">Items:</span> {cart.length || 0}</p>
                <p className="text-gray-600"><span className="font-medium">Total:</span> {formatINR(cart.reduce((total, item) => total + (item.price * item.quantity), 0))}</p>
                <Link to="/cart" className="inline-flex items-center mt-4 text-emerald-600 hover:text-emerald-700 font-medium">
                  <span className="mr-1">👀</span>
                  View Cart
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500 mb-2">Your cart is empty</p>
                <Link to="/shop" className="text-purple-600 hover:text-purple-700 font-medium">
                  Start Shopping
                </Link>
              </div>
            )}
          </div>

          {/* Account Summary Card */}
          <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">📊</span>
              <h2 className="text-xl font-semibold text-gray-800">Account Stats</h2>
            </div>
            <div className="space-y-2">
              <p className="text-gray-600"><span className="font-medium">Username:</span> {user.username || ''}</p>
              <p className="text-gray-600"><span className="font-medium">Total Orders:</span> {recentOrders.length || 0}</p>
              <Link to="/orders" className="inline-flex items-center mt-4 text-blue-600 hover:text-blue-700 font-medium">
                <span className="mr-1">📋</span>
                View All Orders
              </Link>
            </div>
          </div>

          {/* Gaming & Wallet Card */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-700 p-6 text-white shadow-2xl transform hover:scale-105 transition-all duration-300">
            <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
            <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

            <div className="relative z-10">
              <div className="flex items-center mb-4">
                <span className="text-3xl mr-3">🎮</span>
                <h2 className="text-xl font-bold">Gaming & Wallet</h2>
              </div>
              <div className="space-y-3">
                <div className="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-3">
                  <WalletBalance showDetails={true} />
                </div>
                <div className="grid grid-cols-1 gap-2">
                  <Link
                    to="/game-dashboard"
                    className="px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm text-white font-medium rounded-lg hover:bg-opacity-30 transition-all duration-200 text-center text-sm border border-white border-opacity-30"
                  >
                    🎯 Play Games
                  </Link>
                  <Link
                    to="/wallet"
                    className="px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm text-white font-medium rounded-lg hover:bg-opacity-30 transition-all duration-200 text-center text-sm border border-white border-opacity-30"
                  >
                    💰 Wallet History
                  </Link>
                </div>
                <div className="text-xs bg-white bg-opacity-20 backdrop-blur-sm p-3 rounded-lg border border-white border-opacity-30">
                  💡 <strong>Tip:</strong> Earn 5 tokens per game win! Use tokens for discounts.
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Orders */}
        <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <span className="text-3xl mr-4">📋</span>
              <h2 className="text-2xl font-bold text-gray-900">Recent Orders</h2>
            </div>
            <Link to="/orders" className="text-purple-600 hover:text-purple-700 font-medium">
              View All Orders
            </Link>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              <span className="ml-3 text-gray-600">Loading recent orders...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <span className="text-4xl mb-4 block">⚠️</span>
              <p className="text-red-600 font-medium">{error}</p>
            </div>
          ) : recentOrders.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {recentOrders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">
                        {order.order_number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                        {new Date(order.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          order.status === 'completed' ? 'bg-green-100 text-green-800' :
                          order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                          order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap font-semibold text-gray-900">
                        {formatINR(order.total)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Link to={`/order/${order.id}`} className="text-purple-600 hover:text-purple-700 font-medium">
                          View Details
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <span className="text-6xl mb-4 block">📦</span>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No orders yet</h3>
              <p className="text-gray-600 mb-8">Start shopping to see your order history here</p>
              <Link
                to="/shop"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
              >
                <span className="mr-2">🛍️</span>
                Start Shopping
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;