from django.contrib import admin
from django.utils.html import format_html
from django.urls import path
from django.shortcuts import redirect
from django.contrib import messages
from django.template.response import TemplateResponse
from django.urls import reverse
from .models import OrderTracking
from .utils import sync_orders_from_printify

@admin.register(OrderTracking)
class OrderTrackingAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderTracking model.

    Provides a clean interface to manage order tracking information with:
    - List display showing key tracking information
    - Search functionality by order ID and tracking number
    - Filtering by status and carrier
    - Custom display for tracking links
    - "Sync Orders" button to fetch latest orders from Printify
    """
    list_display = [
        'order_id',
        'user_display',
        'status_display',
        'tracking_number_display',
        'carrier',
        'estimated_delivery',
        'last_update'
    ]
    list_filter = ['status', 'carrier', 'created_at', 'last_update']
    search_fields = ['order_id', 'tracking_number', 'user__username', 'user__email']
    readonly_fields = ['created_at', 'last_update']

    fieldsets = (
        ('Order Information', {
            'fields': ('user', 'order_id', 'printify_order_id', 'status')
        }),
        ('Tracking Information', {
            'fields': ('tracking_number', 'carrier', 'carrier_link', 'estimated_delivery')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'last_update'),
            'classes': ('collapse',)
        }),
    )

    change_list_template = 'admin/order_tracking/ordertracking/change_list.html'

    def get_urls(self):
        """Add custom URLs for the admin interface."""
        urls = super().get_urls()
        custom_urls = [
            path('sync-orders/', self.admin_site.admin_view(self.sync_orders_view), name='sync-orders'),
        ]
        return custom_urls + urls

    def sync_orders_view(self, request):
        """View to handle the sync orders action."""
        if request.method == 'POST':
            # Get the number of days to look back (default to 30)
            days_back = int(request.POST.get('days_back', 30))

            # Call the sync function
            result = sync_orders_from_printify(days_back=days_back)

            if result['success']:
                self.message_user(
                    request,
                    result['message'],
                    messages.SUCCESS
                )
            else:
                self.message_user(
                    request,
                    result['message'],
                    messages.ERROR
                )

                # Add detailed error messages if available
                if result['errors']:
                    for error in result['errors'][:5]:  # Show at most 5 errors
                        self.message_user(
                            request,
                            f"Error: {error}",
                            messages.WARNING
                        )

                    if len(result['errors']) > 5:
                        self.message_user(
                            request,
                            f"... and {len(result['errors']) - 5} more errors. Check the logs for details.",
                            messages.WARNING
                        )

            # Redirect back to the change list
            return redirect(reverse('admin:order_tracking_ordertracking_changelist'))

        # If not a POST request, show the sync form
        context = {
            'title': 'Sync Orders from Printify',
            'opts': self.model._meta,
            'app_label': self.model._meta.app_label,
            'days_back_options': [7, 14, 30, 60, 90],
        }
        return TemplateResponse(request, 'admin/order_tracking/ordertracking/sync_orders_form.html', context)

    def user_display(self, obj):
        """Display the user's username and email."""
        if obj.user:
            return f"{obj.user.username} ({obj.user.email})"
        return "N/A"
    user_display.short_description = "User"

    def status_display(self, obj):
        """Display the status with color coding."""
        status_colors = {
            'pending': '#f39c12',     # Orange
            'processing': '#3498db',  # Blue
            'shipped': '#2ecc71',     # Green
            'delivered': '#27ae60',   # Dark Green
            'cancelled': '#e74c3c',   # Red
        }
        color = status_colors.get(obj.status, '#95a5a6')  # Default gray
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = "Status"

    def tracking_number_display(self, obj):
        """Display the tracking number with a link if available."""
        if obj.tracking_number and obj.carrier_link:
            return format_html(
                '<a href="{}" target="_blank">{}</a>',
                obj.carrier_link,
                obj.tracking_number
            )
        return obj.tracking_number or "N/A"
    tracking_number_display.short_description = "Tracking Number"
