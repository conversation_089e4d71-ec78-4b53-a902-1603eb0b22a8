# Generated manually to simplify ProductImage model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0012_fix_productimage_schema'),
    ]

    operations = [
        # Change image field back to regular ImageField
        migrations.AlterField(
            model_name='productimage',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='products/'),
        ),
        
        # Ensure image_url field exists with correct definition
        migrations.AlterField(
            model_name='productimage',
            name='image_url',
            field=models.URLField(blank=True, null=True, help_text='URL for the image (used for display only)'),
        ),
        
        # Update ordering
        migrations.AlterModelOptions(
            name='productimage',
            options={'ordering': ['-is_primary', 'created_at']},
        ),
    ]
