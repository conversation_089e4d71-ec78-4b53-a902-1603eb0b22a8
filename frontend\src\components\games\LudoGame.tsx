import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';
import { ludoService } from '../../services/ludoService';

interface LudoToken {
  position: number;
  is_home: boolean;
  is_finished: boolean;
}

interface LudoGameState {
  board: { [key: string]: string | null };
  current_player: 'player1' | 'player2';
  dice_value: number;
  game_timer: number;
  start_time: string;
  player1_moves: number;
  player2_moves: number;
  player1_tokens: { [key: string]: LudoToken };
  player2_tokens: { [key: string]: LudoToken };
  status: 'active' | 'completed' | 'draw';
  winner: string | null;
  last_move: any;
  difficulty: string;
  num_tokens: number;
  game_mode: string;
  session_id: string;
}

interface GameResult {
  success: boolean;
  game_id: string;
  game_state: LudoGameState;
  difficulty: string;
  num_tokens: number;
  game_mode: string;
  balance: number;
  message: string;
  error?: string;
}

const LudoGame: React.FC = () => {
  const { user } = useAuth();
  const { wallet, refreshWallet } = useWallet();
  
  const [gameState, setGameState] = useState<LudoGameState | null>(null);
  const [gameStatus, setGameStatus] = useState<'waiting' | 'playing' | 'finished'>('waiting');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [diceValue, setDiceValue] = useState<number>(0);
  const [validMoves, setValidMoves] = useState<any[]>([]);
  const [selectedToken, setSelectedToken] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(300); // 5 minutes
  const [gameResult, setGameResult] = useState<'win' | 'loss' | 'draw' | null>(null);
  const [tokensEarned, setTokensEarned] = useState<number>(0);
  const [showResult, setShowResult] = useState(false);
  
  // Game settings
  const [numTokens, setNumTokens] = useState<number>(4);
  const [gameMode, setGameMode] = useState<'vs_bot' | 'vs_user'>('vs_bot');
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');

  // Timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (gameStatus === 'playing' && gameState && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            // Time's up - end game
            endGame();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [gameStatus, gameState, timeLeft]);

  // Format time display
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Start new game
  const startGame = async () => {
    if (!user) return;
    
    setIsLoading(true);
    setError('');
    
    try {
      const result = await ludoService.startGame({
        difficulty,
        num_tokens: numTokens,
        mode: gameMode
      });
      
      if (result.success) {
        setGameState(result.game_state);
        setGameStatus('playing');
        setTimeLeft(300); // Reset timer
        setShowResult(false);
        await refreshWallet();
      } else {
        setError(result.error || 'Failed to start game');
      }
    } catch (error) {
      console.error('Error starting game:', error);
      setError('Failed to start game');
    } finally {
      setIsLoading(false);
    }
  };

  // Roll dice
  const rollDice = async () => {
    if (!gameState || gameState.current_player !== 'player1') return;
    
    setIsLoading(true);
    try {
      const result = await ludoService.rollDice(gameState.session_id);
      
      if (result.success) {
        setDiceValue(result.dice_value);
        
        // Get valid moves after rolling dice
        const updatedState = { ...gameState, dice_value: result.dice_value };
        const movesResult = await ludoService.getValidMoves(updatedState, 'player1');
        
        if (movesResult.success) {
          setValidMoves(movesResult.valid_moves);
        }
        
        setGameState(updatedState);
      }
    } catch (error) {
      console.error('Error rolling dice:', error);
      setError('Failed to roll dice');
    } finally {
      setIsLoading(false);
    }
  };

  // Make move
  const makeMove = async (move: any) => {
    if (!gameState) return;
    
    setIsLoading(true);
    try {
      const result = await ludoService.makeMove(gameState.session_id, move, gameState);
      
      if (result.success) {
        setGameState(result.game_state);
        setValidMoves([]);
        setSelectedToken(null);
        setDiceValue(0);
        
        // Check if game ended
        if (result.game_ended) {
          endGame();
          return;
        }
        
        // If it's bot's turn, make bot move
        if (result.game_state.current_player === 'player2' && gameMode === 'vs_bot') {
          setTimeout(() => makeBotMove(result.game_state), 1000);
        }
      }
    } catch (error) {
      console.error('Error making move:', error);
      setError('Failed to make move');
    } finally {
      setIsLoading(false);
    }
  };

  // Bot move
  const makeBotMove = async (currentState: LudoGameState) => {
    try {
      // Roll dice for bot
      const diceResult = await ludoService.rollDice(currentState.session_id);
      if (!diceResult.success) return;
      
      const stateWithDice = { ...currentState, dice_value: diceResult.dice_value };
      
      // Get bot move
      const botResult = await ludoService.getBotMove(stateWithDice, difficulty);
      
      if (botResult.success && botResult.bot_move) {
        setGameState(botResult.game_state);
        
        // Check if game ended
        if (botResult.game_state.status !== 'active') {
          endGame();
        }
      }
    } catch (error) {
      console.error('Error making bot move:', error);
    }
  };

  // End game
  const endGame = async () => {
    if (!gameState) return;
    
    try {
      // Determine result based on moves
      let result: 'win' | 'loss' | 'draw';
      const playerMoves = gameState.player1_moves;
      const opponentMoves = gameState.player2_moves;
      
      if (playerMoves > opponentMoves) {
        result = 'win';
      } else if (opponentMoves > playerMoves) {
        result = 'loss';
      } else {
        result = 'draw';
      }
      
      // Complete game
      const completeResult = await ludoService.completeGame(
        gameState.session_id,
        result,
        gameState
      );
      
      if (completeResult.success) {
        setGameResult(result);
        setTokensEarned(completeResult.tokens_earned);
        setGameStatus('finished');
        setShowResult(true);
        await refreshWallet();
      }
    } catch (error) {
      console.error('Error ending game:', error);
    }
  };

  // Forfeit game
  const forfeitGame = async () => {
    if (!gameState) return;
    
    if (window.confirm('Are you sure you want to forfeit? You will lose your participation tokens.')) {
      try {
        const result = await ludoService.forfeitGame(gameState.session_id);
        if (result.success) {
          setGameResult('loss');
          setTokensEarned(result.tokens_earned || -2);
          setGameStatus('finished');
          setShowResult(true);
          await refreshWallet();
        }
      } catch (error) {
        console.error('Error forfeiting game:', error);
      }
    }
  };

  // Render token on board
  const renderToken = (tokenId: string, tokenData: LudoToken, player: 'player1' | 'player2') => {
    const isSelected = selectedToken === tokenId;
    const canMove = validMoves.some(move => move.token === tokenId);
    const color = player === 'player1' ? 'bg-blue-500' : 'bg-red-500';
    
    return (
      <div
        key={tokenId}
        className={`w-6 h-6 rounded-full ${color} border-2 border-white cursor-pointer transform transition-all duration-200 ${
          isSelected ? 'scale-125 ring-2 ring-yellow-400' : ''
        } ${canMove ? 'hover:scale-110' : 'opacity-50'}`}
        onClick={() => canMove && setSelectedToken(tokenId)}
      >
        <div className="w-full h-full flex items-center justify-center text-xs font-bold text-white">
          {tokenId.split('_')[1]}
        </div>
      </div>
    );
  };

  if (showResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h2 className="text-3xl font-bold mb-6">🎲 Game Complete!</h2>
          
          <div className="space-y-4 mb-6">
            <div className="flex justify-between text-lg">
              <span>Your Moves:</span>
              <span className="font-bold">{gameState?.player1_moves || 0}</span>
            </div>
            <div className="flex justify-between text-lg">
              <span>Opponent Moves:</span>
              <span className="font-bold">{gameState?.player2_moves || 0}</span>
            </div>
            <div className="flex justify-between text-lg">
              <span>Game Duration:</span>
              <span className="font-bold">5:00</span>
            </div>
          </div>

          <div className="mb-6">
            {gameResult === 'win' && (
              <div className="text-green-400">
                <div className="text-6xl mb-4">🏆</div>
                <div className="text-2xl font-bold">You Won!</div>
                <div className="text-lg">+{tokensEarned} tokens</div>
                <div className="text-sm opacity-75 mt-2">More moves than opponent!</div>
              </div>
            )}
            {gameResult === 'loss' && (
              <div className="text-red-400">
                <div className="text-6xl mb-4">😔</div>
                <div className="text-2xl font-bold">You Lost</div>
                <div className="text-lg">{tokensEarned} tokens</div>
                <div className="text-sm opacity-75 mt-2">Opponent had more moves</div>
              </div>
            )}
            {gameResult === 'draw' && (
              <div className="text-yellow-400">
                <div className="text-6xl mb-4">🤝</div>
                <div className="text-2xl font-bold">Draw!</div>
                <div className="text-lg">Must replay</div>
                <div className="text-sm opacity-75 mt-2">Same number of moves</div>
              </div>
            )}
          </div>

          <div className="mb-6">
            <div className="text-sm opacity-75">Current Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Play Again'}
          </button>
        </div>
      </div>
    );
  }

  if (gameStatus === 'waiting') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h1 className="text-4xl font-bold mb-4">🎲 Ludo Game</h1>
          <p className="text-lg mb-4 opacity-90">
            5-minute timer-based Ludo! Make as many valid moves as possible to win!
          </p>

          <div className="mb-4">
            <div className="px-3 py-2 bg-yellow-50/20 border border-yellow-200/30 rounded-lg inline-block">
              <span className="text-sm font-medium text-yellow-200">🟡 Medium Difficulty</span>
            </div>
          </div>

          {/* Game Settings */}
          <div className="mb-6 space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Number of Tokens</label>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setNumTokens(2)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all ${
                    numTokens === 2
                      ? 'bg-blue-500 text-white'
                      : 'bg-white/20 text-white/70 hover:bg-white/30'
                  }`}
                >
                  2 Tokens
                </button>
                <button
                  onClick={() => setNumTokens(4)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all ${
                    numTokens === 4
                      ? 'bg-blue-500 text-white'
                      : 'bg-white/20 text-white/70 hover:bg-white/30'
                  }`}
                >
                  4 Tokens
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Game Mode</label>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setGameMode('vs_bot')}
                  className={`px-4 py-2 rounded-lg font-medium transition-all ${
                    gameMode === 'vs_bot'
                      ? 'bg-purple-500 text-white'
                      : 'bg-white/20 text-white/70 hover:bg-white/30'
                  }`}
                >
                  vs Bot
                </button>
                <button
                  onClick={() => setGameMode('vs_user')}
                  className={`px-4 py-2 rounded-lg font-medium transition-all ${
                    gameMode === 'vs_user'
                      ? 'bg-purple-500 text-white'
                      : 'bg-white/20 text-white/70 hover:bg-white/30'
                  }`}
                >
                  vs User
                </button>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <div className="text-sm opacity-75">Your Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <div className="mb-6 text-sm opacity-75">
            <div>Entry: -2 tokens</div>
            <div>Win: +5 tokens</div>
            <div>Loss: -1 token</div>
            <div>Draw: Replay required</div>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-500/20 border border-red-400/30 rounded-lg text-red-300">
              {error}
            </div>
          )}

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Start Game'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Game Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 text-white">
          <div className="flex justify-between items-center mb-4">
            <div className="text-center">
              <div className="text-sm opacity-75">Your Moves</div>
              <div className="text-2xl font-bold">{gameState?.player1_moves || 0}</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold">⏰ {formatTime(timeLeft)}</div>
              <div className="text-sm opacity-75">Time Remaining</div>
            </div>
            <div className="text-center">
              <div className="text-sm opacity-75">Opponent Moves</div>
              <div className="text-2xl font-bold">{gameState?.player2_moves || 0}</div>
            </div>
          </div>

          <div className="text-center">
            <div className="text-sm opacity-75">Current Turn</div>
            <div className="text-lg font-bold">
              {gameState?.current_player === 'player1' ? 'Your Turn' : 'Opponent Turn'}
            </div>
          </div>
        </div>

        {/* Game Board */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6">
          <div className="text-center text-white mb-6">
            <h2 className="text-2xl font-bold mb-2">🎲 Ludo Board</h2>
            <p className="text-sm opacity-75">Make valid moves to score points!</p>
          </div>

          {/* Simplified Ludo Board Representation */}
          <div className="grid grid-cols-8 gap-2 max-w-md mx-auto">
            {Array.from({ length: 64 }, (_, i) => (
              <div
                key={i}
                className={`w-8 h-8 rounded border-2 border-white/30 flex items-center justify-center text-xs ${
                  [0, 7, 14, 21, 28, 35, 42, 49, 56, 63].includes(i)
                    ? 'bg-yellow-400/30'
                    : 'bg-white/10'
                }`}
              >
                {i}
              </div>
            ))}
          </div>

          {/* Token Display */}
          <div className="mt-6 grid grid-cols-2 gap-4">
            <div className="text-center">
              <h3 className="text-white font-bold mb-2">Your Tokens</h3>
              <div className="flex justify-center space-x-2">
                {gameState && Object.entries(gameState.player1_tokens).map(([tokenId, tokenData]) =>
                  renderToken(tokenId, tokenData, 'player1')
                )}
              </div>
            </div>
            <div className="text-center">
              <h3 className="text-white font-bold mb-2">Opponent Tokens</h3>
              <div className="flex justify-center space-x-2">
                {gameState && Object.entries(gameState.player2_tokens).map(([tokenId, tokenData]) =>
                  renderToken(tokenId, tokenData, 'player2')
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Game Controls */}
        {gameState?.current_player === 'player1' && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center">
            <div className="mb-4">
              {diceValue > 0 ? (
                <div className="text-white">
                  <div className="text-6xl mb-2">🎲</div>
                  <div className="text-2xl font-bold">You rolled: {diceValue}</div>
                </div>
              ) : (
                <button
                  onClick={rollDice}
                  disabled={isLoading}
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white py-3 px-8 rounded-xl font-bold text-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 disabled:opacity-50"
                >
                  {isLoading ? 'Rolling...' : '🎲 Roll Dice'}
                </button>
              )}
            </div>

            {validMoves.length > 0 && (
              <div className="mb-4">
                <h3 className="text-white font-bold mb-2">Available Moves:</h3>
                <div className="space-y-2">
                  {validMoves.map((move, index) => (
                    <button
                      key={index}
                      onClick={() => makeMove(move)}
                      className="block w-full bg-green-500/20 border border-green-400/30 text-green-300 py-2 px-4 rounded-lg hover:bg-green-500/30 transition-all"
                    >
                      Move {move.token} from {move.from === -1 ? 'home' : move.from} to {move.to}
                    </button>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-center space-x-4">
              <button
                onClick={forfeitGame}
                className="px-4 py-2 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-200"
              >
                ⚠️ Forfeit Game
              </button>
            </div>

            <div className="mt-4 text-center text-sm opacity-75 text-white">
              <div>Balance: {wallet?.balance || 0} tokens</div>
              <div className="text-xs mt-1">Game ends when timer reaches 0:00</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LudoGame;
