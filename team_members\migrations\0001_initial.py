# Generated by Django 5.0.2 on 2025-05-08 13:43

import team_members.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(help_text="The team member's full name", max_length=100)),
                ('role', models.Char<PERSON>ield(help_text="The team member's role or position", max_length=100)),
                ('photo', models.ImageField(blank=True, help_text='Profile photo (max 2MB, 1200x1200)', null=True, upload_to=team_members.models.team_photo_path, validators=[team_members.models.validate_image])),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile URL (optional)', null=True)),
                ('order', models.PositiveInteger<PERSON>ield(default=0, help_text='Display order (lower numbers displayed first)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Team Member',
                'verbose_name_plural': 'Team Members',
                'ordering': ['order', 'name'],
            },
        ),
    ]
