import React, { useState } from 'react';
import axios from 'axios';
import { getPhoneNumber } from '../utils/phoneUtils';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [status, setStatus] = useState({
    submitted: false,
    success: false,
    error: ''
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setStatus({ submitted: false, success: false, error: '' });

    try {
      // Send the contact form data to the backend
      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/contact/`, formData);

      console.log('Contact form submission successful:', response.data);

      setStatus({
        submitted: true,
        success: true,
        error: ''
      });

      // Reset form after successful submission
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: ''
      });
    } catch (err: any) {
      console.error('Contact form submission error:', err);

      // Handle different types of errors
      let errorMessage = 'There was an error sending your message. Please try again.';

      if (err.response?.data) {
        if (err.response.data.email) {
          errorMessage = `Email: ${err.response.data.email}`;
        } else if (err.response.data.name) {
          errorMessage = `Name: ${err.response.data.name}`;
        } else if (err.response.data.subject) {
          errorMessage = `Subject: ${err.response.data.subject}`;
        } else if (err.response.data.message) {
          errorMessage = `Message: ${err.response.data.message}`;
        } else if (err.response.data.detail) {
          errorMessage = err.response.data.detail;
        }
      }

      setStatus({
        submitted: true,
        success: false,
        error: errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Contact Us</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Get in Touch</h2>

            {status.submitted && status.success && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                Thank you for your message! We'll get back to you as soon as possible.
              </div>
            )}

            {status.submitted && !status.success && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {status.error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Your Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full p-2 border rounded focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full p-2 border rounded focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Subject
                </label>
                <select
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  required
                  className="w-full p-2 border rounded focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a subject</option>
                  <option value="General Inquiry">General Inquiry</option>
                  <option value="Product Question">Product Question</option>
                  <option value="Order Status">Order Status</option>
                  <option value="Returns & Refunds">Returns & Refunds</option>
                  <option value="Feedback">Feedback</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Message
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={6}
                  className="w-full p-2 border rounded focus:ring-blue-500 focus:border-blue-500"
                ></textarea>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {loading ? 'Sending...' : 'Send Message'}
              </button>
            </form>
          </div>

          <div>
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Contact Information</h2>

              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-1">Address</h3>
                  <p className="text-gray-700">
                    #42, 2nd Floor<br />
                    MG Road, Bangalore<br />
                    Karnataka, India 560001
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-1">Email</h3>
                  <p className="text-gray-700">
                    <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                      <EMAIL>
                    </a>
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-1">Phone</h3>
                  <p className="text-gray-700">
                    <a href={`tel:${getPhoneNumber('link')}`} className="text-blue-600 hover:underline">
                      {getPhoneNumber('display')}
                    </a>
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-1">Hours of Operation</h3>
                  <p className="text-gray-700">
                    Monday - Saturday: 10:00 AM - 7:00 PM IST<br />
                    Sunday: Closed
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Follow Us</h2>
              <div className="flex space-x-4">
                <a href="#" className="text-blue-600 hover:text-blue-800">Facebook</a>
                <a href="#" className="text-blue-400 hover:text-blue-600">Twitter</a>
                <a href="#" className="text-pink-600 hover:text-pink-800">Instagram</a>
                <a href="#" className="text-blue-700 hover:text-blue-900">LinkedIn</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;