from django.core.management.base import BaseCommand
from django.conf import settings
from printify.models import PrintifyConfig

class Command(BaseCommand):
    help = 'Updates the Printify API token in the database'

    def handle(self, *args, **options):
        # Get the token from settings
        token = settings.PRINTIFY_API_TOKEN
        
        if not token:
            self.stdout.write(self.style.ERROR("No Printify API token found in settings."))
            return
        
        # Check if there are any existing configs
        configs = PrintifyConfig.objects.all()
        
        if configs.exists():
            # Update all existing configs
            for config in configs:
                config.api_token = token
                config.save()
                self.stdout.write(self.style.SUCCESS(f"Updated PrintifyConfig with ID {config.id}"))
        else:
            # Create a new config
            config = PrintifyConfig.objects.create(
                api_token=token,
                is_active=True
            )
            self.stdout.write(self.style.SUCCESS(f"Created new PrintifyConfig with ID {config.id}"))
        
        # Print all configs
        self.stdout.write("\nCurrent PrintifyConfig objects:")
        for config in PrintifyConfig.objects.all():
            self.stdout.write(f"ID: {config.id}, Active: {config.is_active}, Created: {config.created_at}")
