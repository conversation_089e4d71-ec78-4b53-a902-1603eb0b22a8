from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q
from django.utils import timezone
from .models import GameType, Battle, PlayerStats
from .serializers import (
    GameTypeSerializer, BattleSerializer, BattleListSerializer,
    PlayerStatsSerializer, CreateBattleSerializer
)
from .matchmaking import MatchmakingService


class GameTypeListView(generics.ListAPIView):
    """
    List all available game types
    """
    queryset = GameType.objects.filter(is_active=True)
    serializer_class = GameTypeSerializer
    permission_classes = [permissions.AllowAny]


class BattleListView(generics.ListAPIView):
    """
    List user's battles
    """
    serializer_class = BattleListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        return Battle.objects.filter(
            Q(player1=user) | Q(player2=user)
        ).select_related('game_type', 'player1', 'player2').order_by('-created_at')


class BattleDetailView(generics.RetrieveAPIView):
    """
    Get battle details
    """
    serializer_class = BattleSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        return Battle.objects.filter(
            Q(player1=user) | Q(player2=user)
        ).select_related('game_type', 'player1', 'player2')


class PlayerStatsView(generics.RetrieveAPIView):
    """
    Get player's gaming statistics
    """
    serializer_class = PlayerStatsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        stats, created = PlayerStats.objects.get_or_create(user=self.request.user)
        return stats


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_battle(request):
    """
    Create a new battle (either find opponent or create AI battle)
    """
    # Check if user can play games (has tokens)
    from wallet.game_integration import check_game_eligibility
    eligibility = check_game_eligibility(request.user.id)
    if not eligibility['can_play']:
        return Response({
            'error': 'You need tokens to play games',
            'message': eligibility['message'],
            'balance': eligibility['balance']
        }, status=status.HTTP_400_BAD_REQUEST)

    serializer = CreateBattleSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    game_type_name = serializer.validated_data['game_type']
    is_ai_battle = serializer.validated_data['is_ai_battle']

    try:
        if is_ai_battle:
            # Create AI battle immediately
            import asyncio
            battle = asyncio.run(MatchmakingService.create_ai_battle(request.user, game_type_name))
            
            return Response({
                'battle_id': str(battle.id),
                'type': 'ai_battle',
                'message': 'AI battle created successfully'
            }, status=status.HTTP_201_CREATED)
        else:
            # Try to find a match
            import asyncio
            battle = asyncio.run(MatchmakingService.find_match(request.user, game_type_name))
            
            if battle:
                return Response({
                    'battle_id': str(battle.id),
                    'type': 'match_found',
                    'message': 'Match found!'
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'type': 'added_to_queue',
                    'message': 'Added to matchmaking queue'
                }, status=status.HTTP_202_ACCEPTED)
    
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def matchmaking_status(request):
    """
    Get current matchmaking status
    """
    try:
        import asyncio
        status_data = asyncio.run(MatchmakingService.get_queue_status(request.user))
        
        return Response(status_data)
    
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_ai_battle(request):
    """
    Create an AI battle directly (simplified endpoint) and deduct 2 tokens for participation
    """
    # Check if user can play games (requires 2 tokens)
    from wallet.game_integration import check_game_eligibility, deduct_game_participation_fee
    eligibility = check_game_eligibility(request.user.id)
    if not eligibility['can_play']:
        return Response({
            'error': 'You need at least 2 tokens to play games',
            'message': eligibility['message'],
            'balance': eligibility['balance']
        }, status=status.HTTP_400_BAD_REQUEST)

    game_type = request.data.get('game_type')
    if not game_type:
        return Response({
            'error': 'game_type is required'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Deduct 2 tokens for participation before creating battle
        participation_result = deduct_game_participation_fee(
            user_id=request.user.id,
            game_type=game_type,
            game_id=None  # Will be set after battle creation
        )

        if not participation_result['success']:
            return Response({
                'error': participation_result['error'],
                'balance': participation_result.get('balance', 0)
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create AI battle immediately
        import asyncio
        battle = asyncio.run(MatchmakingService.create_ai_battle(request.user, game_type))

        return Response({
            'battle_id': str(battle.id),
            'type': 'ai_battle_created',
            'message': f'AI battle created successfully (2 tokens deducted for participation)',
            'remaining_balance': participation_result['remaining_balance']
        }, status=status.HTTP_201_CREATED)

    except ValueError as e:
        # Handle specific game type not found error
        return Response({
            'error': f'Game type error: {str(e)}',
            'game_type_requested': game_type
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # Handle any other errors
        import traceback
        error_details = traceback.format_exc()
        print(f"AI Battle Creation Error: {error_details}")

        return Response({
            'error': f'Failed to create AI battle: {str(e)}',
            'game_type_requested': game_type,
            'error_type': type(e).__name__
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def start_battle(request, battle_id):
    """
    Start a battle
    """
    try:
        battle = Battle.objects.get(id=battle_id)

        # Check if user is part of this battle
        if battle.player1 != request.user and battle.player2 != request.user:
            return Response({
                'error': 'You are not part of this battle'
            }, status=status.HTTP_403_FORBIDDEN)

        # Start the battle
        from .game_logic import GameEngine

        battle.status = 'in_progress'
        battle.started_at = timezone.now()
        battle.save()

        # Initialize game state
        battle.game_state = GameEngine.create_initial_state(battle.game_type.name)
        battle.save()

        return Response({
            'message': 'Battle started successfully',
            'battle': BattleSerializer(battle).data
        })

    except Battle.DoesNotExist:
        return Response({
            'error': 'Battle not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def make_move(request, battle_id):
    """
    Make a move in a battle
    """
    try:
        battle = Battle.objects.get(id=battle_id)

        # Check if user is part of this battle
        if battle.player1 != request.user and battle.player2 != request.user:
            return Response({
                'error': 'You are not part of this battle'
            }, status=status.HTTP_403_FORBIDDEN)

        if battle.status != 'in_progress':
            return Response({
                'error': 'Battle is not in progress'
            }, status=status.HTTP_400_BAD_REQUEST)

        move = request.data.get('move')
        if not move:
            return Response({
                'error': 'Move is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Process the move
        from .game_logic import GameEngine
        import random

        player_name = 'player1' if battle.player1 == request.user else 'player2'

        # Update game state with the move
        battle.game_state = GameEngine.process_move(battle.game_type.name, battle.game_state, player_name, move)

        # If it's an AI battle and player 1 made a move, generate AI move
        if battle.is_ai_battle and player_name == 'player1':
            # Generate AI move based on game type
            if battle.game_type.name == 'rock_paper_scissors':
                ai_move = random.choice(['rock', 'paper', 'scissors'])
            elif battle.game_type.name == 'number_guessing':
                ai_move = random.randint(1, 100)
            else:
                ai_move = 'rock'  # fallback

            battle.game_state = GameEngine.process_move(battle.game_type.name, battle.game_state, 'player2', ai_move)

        # Check if battle is complete
        if GameEngine.is_game_over(battle.game_type.name, battle.game_state):
            battle.status = 'completed'
            battle.completed_at = timezone.now()
            battle.result = GameEngine.get_game_result(battle.game_type.name, battle.game_state)

            # Handle token transactions based on game result
            from wallet.models import Wallet
            from wallet.game_integration import handle_game_loss
            from django.conf import settings

            try:
                wallet, created = Wallet.objects.get_or_create(user=request.user)

                # New token economy: 5 tokens per win, 1 token deducted per loss
                if battle.result == 'player1_win':
                    # Player wins - award 5 tokens
                    wallet.add_tokens(
                        amount=5,
                        transaction_type='game_win',
                        description=f'Won battle {battle.id}'
                    )
                    print(f"✅ Awarded 5 tokens for winning battle {battle.id}")

                elif battle.result == 'player2_win':
                    # Player loses - deduct 1 token (only if they have tokens)
                    if wallet.balance > 0:
                        loss_result = handle_game_loss(
                            user_id=request.user.id,
                            game_id=str(battle.id),
                            description=f'Lost battle {battle.id}'
                        )
                        if loss_result['success']:
                            print(f"✅ Deducted 1 token for losing battle {battle.id}")
                        else:
                            print(f"⚠️ Could not deduct token: {loss_result['error']}")
                    else:
                        print(f"⚠️ User has no tokens to deduct for loss in battle {battle.id}")

                elif battle.result == 'draw':
                    # Draw - no token change
                    print(f"🤝 Draw in battle {battle.id} - no token change")

            except Exception as e:
                print(f"❌ Error processing token transaction: {e}")

        battle.save()

        return Response({
            'message': 'Move processed successfully',
            'battle': BattleSerializer(battle).data
        })

    except Battle.DoesNotExist:
        return Response({
            'error': 'Battle not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def cancel_matchmaking(request):
    """
    Cancel matchmaking
    """
    try:
        import asyncio
        asyncio.run(MatchmakingService.cancel_matchmaking(request.user))
        
        return Response({
            'message': 'Matchmaking cancelled'
        })
    
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def gaming_stats(request):
    """
    Get overall gaming statistics
    """
    try:
        total_battles = Battle.objects.filter(status='completed').count()
        total_players = PlayerStats.objects.count()
        active_battles = Battle.objects.filter(status='in_progress').count()
        waiting_players = MatchmakingService.get_waiting_players_count()
        
        # Top players by win rate
        top_players = PlayerStats.objects.filter(
            total_battles__gte=5
        ).order_by('-win_rate', '-battles_won')[:10]
        
        top_players_data = PlayerStatsSerializer(top_players, many=True).data
        
        return Response({
            'total_battles': total_battles,
            'total_players': total_players,
            'active_battles': active_battles,
            'waiting_players': waiting_players,
            'top_players': top_players_data
        })
    
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def recent_battles(request):
    """
    Get user's recent battles
    """
    try:
        user = request.user
        recent = Battle.objects.filter(
            Q(player1=user) | Q(player2=user),
            status='completed'
        ).select_related('game_type', 'player1', 'player2').order_by('-completed_at')[:10]
        
        serializer = BattleListSerializer(recent, many=True, context={'request': request})
        
        return Response(serializer.data)
    
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
