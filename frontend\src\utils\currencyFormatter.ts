/**
 * Utility functions for currency formatting
 */

/**
 * Format a number as INR currency
 * @param value - The value to format
 * @returns Formatted currency string
 */
export const formatINR = (value: any): string => {
  console.log('formatINR input:', value, typeof value);

  // Handle null or undefined
  if (value === null || value === undefined) {
    console.log('formatINR: value is null or undefined');
    return '₹0.00';
  }

  // Parse the value to a number if it's a string
  let numValue: number;

  if (typeof value === 'string') {
    // Remove any non-numeric characters except decimal point
    const cleanValue = value.replace(/[^0-9.]/g, '');
    numValue = parseFloat(cleanValue);
    console.log('formatINR: parsed string to number:', numValue);
  } else if (typeof value === 'number') {
    numValue = value;
  } else {
    console.log('formatINR: value is not a string or number:', value);
    return '₹0.00';
  }

  // Return formatted value or 0.00 if invalid
  if (isNaN(numValue)) {
    console.log('formatINR: numValue is NaN');
    return '₹0.00';
  }

  // Format as INR with Indian thousands separator format
  const formatted = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numValue);

  console.log('formatINR output:', formatted);
  return formatted;
};

/**
 * Format a number as INR without the currency symbol
 * @param value - The value to format
 * @returns Formatted number string
 */
export const formatINRValue = (value: any): string => {
  // Parse the value to a number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  // Return formatted value or 0.00 if invalid
  if (typeof numValue !== 'number' || isNaN(numValue)) {
    return '0.00';
  }

  // Format with Indian thousands separator format
  return new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numValue);
};
