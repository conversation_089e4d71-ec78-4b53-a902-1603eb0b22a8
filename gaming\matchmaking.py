"""
Matchmaking service for finding opponents
"""
import asyncio
from typing import Optional, Dict, Any
from django.contrib.auth.models import User
from django.utils import timezone
from django.conf import settings
from .models import Battle, GameType, PlayerStats


class MatchmakingService:
    """
    Service for matching players for battles
    """
    
    # In-memory queue for waiting players
    waiting_players: Dict[str, Dict[str, Any]] = {}
    
    @classmethod
    async def find_match(cls, user: User, game_type_name: str) -> Optional[Battle]:
        """
        Find a match for a player
        Returns existing battle if found, None if added to queue
        """
        from channels.db import database_sync_to_async
        
        # Check if there's already a waiting player for this game type
        for player_id, player_data in cls.waiting_players.items():
            if (player_data['game_type'] == game_type_name and 
                player_data['user_id'] != user.id):
                
                # Found a match!
                opponent_user = await database_sync_to_async(User.objects.get)(id=player_data['user_id'])
                game_type = await database_sync_to_async(GameType.objects.get)(name=game_type_name)
                
                # Create battle
                battle = await database_sync_to_async(Battle.objects.create)(
                    game_type=game_type,
                    player1=opponent_user,
                    player2=user,
                    status='waiting'
                )
                
                # Remove from waiting queue
                del cls.waiting_players[player_id]
                
                return battle
        
        # No match found, add to queue
        cls.waiting_players[str(user.id)] = {
            'user_id': user.id,
            'game_type': game_type_name,
            'timestamp': timezone.now(),
            'username': user.username
        }
        
        # Set timeout to remove from queue
        asyncio.create_task(cls._remove_after_timeout(str(user.id)))
        
        return None
    
    @classmethod
    async def create_ai_battle(cls, user: User, game_type_name: str) -> Battle:
        """
        Create a battle against AI
        """
        from channels.db import database_sync_to_async

        try:
            game_type = await database_sync_to_async(GameType.objects.get)(name=game_type_name)
        except GameType.DoesNotExist:
            # If game type doesn't exist, provide a helpful error message
            raise ValueError(f"Game type '{game_type_name}' not found. Available games: {await database_sync_to_async(list)(GameType.objects.filter(is_active=True).values_list('name', flat=True))}")

        battle = await database_sync_to_async(Battle.objects.create)(
            game_type=game_type,
            player1=user,
            is_ai_battle=True,
            status='waiting'
        )

        return battle
    
    @classmethod
    async def cancel_matchmaking(cls, user: User):
        """
        Remove user from matchmaking queue
        """
        user_id = str(user.id)
        if user_id in cls.waiting_players:
            del cls.waiting_players[user_id]
    
    @classmethod
    async def get_queue_status(cls, user: User) -> Dict[str, Any]:
        """
        Get current queue status for user
        """
        user_id = str(user.id)
        if user_id in cls.waiting_players:
            player_data = cls.waiting_players[user_id]
            wait_time = timezone.now() - player_data['timestamp']
            return {
                'in_queue': True,
                'game_type': player_data['game_type'],
                'wait_time_seconds': wait_time.total_seconds(),
                'queue_position': list(cls.waiting_players.keys()).index(user_id) + 1
            }
        
        return {'in_queue': False}
    
    @classmethod
    async def _remove_after_timeout(cls, user_id: str):
        """
        Remove player from queue after timeout
        """
        timeout_seconds = settings.GAMING_SETTINGS.get('MATCHMAKING_TIMEOUT_SECONDS', 60)
        await asyncio.sleep(timeout_seconds)
        
        if user_id in cls.waiting_players:
            del cls.waiting_players[user_id]
    
    @classmethod
    def get_waiting_players_count(cls, game_type: str = None) -> int:
        """
        Get count of waiting players
        """
        if game_type:
            return sum(1 for p in cls.waiting_players.values() if p['game_type'] == game_type)
        return len(cls.waiting_players)
    
    @classmethod
    async def get_recommended_opponent(cls, user: User, game_type_name: str) -> Optional[User]:
        """
        Get a recommended opponent based on skill level
        """
        from channels.db import database_sync_to_async
        
        # Get user's stats
        try:
            user_stats = await database_sync_to_async(PlayerStats.objects.get)(user=user)
            user_win_rate = user_stats.win_rate
        except PlayerStats.DoesNotExist:
            user_win_rate = 0
        
        # Find players with similar win rates
        similar_players = await database_sync_to_async(list)(
            User.objects.filter(
                gaming_stats__isnull=False
            ).exclude(id=user.id)
        )
        
        if not similar_players:
            return None
        
        # Sort by win rate similarity
        def win_rate_diff(player):
            try:
                return abs(player.gaming_stats.win_rate - user_win_rate)
            except:
                return float('inf')
        
        similar_players.sort(key=win_rate_diff)
        
        # Return the most similar player
        return similar_players[0] if similar_players else None


# MatchmakingConsumer is now in consumers.py to avoid circular imports
