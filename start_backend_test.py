#!/usr/bin/env python
"""
Start Django Backend for Token Purchase Testing
===============================================

This script starts the Django backend server with local test settings
for testing the token purchase system.

Usage:
    python start_backend_test.py
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'local_test_settings')

if __name__ == '__main__':
    print("🚀 Starting Django Backend for Token Purchase Testing")
    print("=" * 60)
    print("📁 Project Root:", project_root)
    print("⚙️  Settings Module: local_test_settings")
    print("🗄️  Database: SQLite (test_db.sqlite3)")
    print("🌐 Server will start on: http://127.0.0.1:8000")
    print("=" * 60)
    
    try:
        # Setup Django
        django.setup()
        
        # Start the development server
        execute_from_command_line([
            'manage.py', 
            'runserver', 
            '127.0.0.1:8000',
            '--noreload'  # Disable auto-reloader to avoid path issues
        ])
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {str(e)}")
        sys.exit(1)
