import React from 'react';
import TeamMembers from '../components/TeamMembers';

const About = () => {
  return (
    <div className="container mx-auto p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">About Us</h1>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4">Our Story</h2>
          <p className="mb-4">
            Founded in 2023, PickMeTrend was born with a simple mission: To empower individuality through stylish, high-quality apparel at fair prices.
          </p>
          <p className="mb-4">
            What started as a passion for fashion and creative design has evolved into a vibrant e-commerce brand. We specialize in trend-driven, meaningful clothing — blending culture, creativity, and technology into every piece.
          </p>
          <p className="mb-4">
            From minimal classics to bold statement designs, every product we offer is curated with love and purpose.
          </p>
          <p>
            Our journey is fueled by a commitment to ethical sourcing, sustainable practices, and creating apparel that feels just as good as it looks.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Our Mission</h2>
            <p>
              Our mission is to inspire confidence, creativity, and self-expression through unique fashion. We aim to deliver exceptional quality and outstanding service, making your shopping experience easy, enjoyable, and personal.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Our Vision</h2>
            <p>
              We believe fashion is more than just clothing — it's a way to tell your story. Our vision is a world where authentic self-expression is accessible to everyone, through sustainable and trend-forward fashion.
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-6">Our Values</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Quality First</h3>
              <p className="text-gray-700">
                Every product is carefully selected to meet our high standards for comfort, durability, and style.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Customer-Centric Approach</h3>
              <p className="text-gray-700">
                Your satisfaction drives everything we do — from design to delivery.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Integrity</h3>
              <p className="text-gray-700">
                We operate with honesty, transparency, and respect for our customers and our planet.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Innovation & Creativity</h3>
              <p className="text-gray-700">
                We embrace new ideas, technologies, and designs to keep evolving with the trends.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold mb-6">Meet Our Team</h2>
          <TeamMembers />
        </div>

        <div className="bg-blue-50 rounded-lg shadow-md p-6 mt-8 text-center">
          <h2 className="text-2xl font-semibold text-blue-800">PickMeTrend — Where Trends Meet Purpose.</h2>
        </div>
      </div>
    </div>
  );
};

export default About;