from django.urls import path
from . import views
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.contrib.auth.models import User

@api_view(['GET'])
def debug_status(request):
    """Debug endpoint to check system status"""
    return Response({
        'status': 'Backend is working!',
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'endpoints': {
            'register': '/api/auth/register/',
            'login': '/api/auth/login/',
            'profile': '/api/auth/profile/'
        }
    })

urlpatterns = [
    path('debug/', debug_status, name='debug-status'),
    path('profile/', views.UserProfileView.as_view(), name='user-profile'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change-password'),
    path('register/', views.RegisterView.as_view(), name='register'),
    path('login/', views.LoginView.as_view(), name='login'),
]