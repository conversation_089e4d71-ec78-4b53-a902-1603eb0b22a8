import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils.text import slugify
from printify.api_client import PrintifyAPIClient
from products.models import Product, ProductVariant, ProductImage, Category
import decimal
import re

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Sync products from Printify API to the database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--shop-id',
            help='Printify shop ID to sync products from (defaults to first shop)',
        )
        parser.add_argument(
            '--delete-others',
            action='store_true',
            help='Delete products that are not in Printify',
        )

    def handle(self, *args, **options):
        shop_id = options.get('shop_id')
        delete_others = options.get('delete_others', False)

        self.stdout.write(self.style.SUCCESS('Starting Printify product sync...'))

        try:
            # Initialize API client
            client = PrintifyAPIClient()

            # Get shop ID if not provided
            if not shop_id:
                shops = client.get_shops()
                if not shops:
                    self.stdout.write(self.style.ERROR('No shops found'))
                    return
                shop_id = shops[0]['id']

            self.stdout.write(f'Using shop ID: {shop_id}')

            # Get products from Printify
            printify_products = client.get_products(shop_id)

            # Check if the response is a list or a dict with a 'data' key
            if isinstance(printify_products, dict) and 'data' in printify_products:
                printify_products = printify_products.get('data', [])

            self.stdout.write(f'Found {len(printify_products)} products in Printify')

            # Keep track of synced product IDs
            synced_printify_ids = []

            # Create or update each product
            for product_data in printify_products:
                try:
                    self._process_product(client, shop_id, product_data)
                    synced_printify_ids.append(product_data['id'])
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing product {product_data.get("title", "Unknown")}: {str(e)}'))
                    logger.exception(f'Error processing product {product_data.get("id", "Unknown")}')

            # Delete products that are not in Printify if requested
            if delete_others:
                deleted_count = Product.objects.filter(printify_id__isnull=False).exclude(printify_id__in=synced_printify_ids).delete()[0]
                self.stdout.write(f'Deleted {deleted_count} products that are not in Printify')

            self.stdout.write(self.style.SUCCESS('Printify product sync completed successfully'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error syncing Printify products: {str(e)}'))
            logger.exception('Error syncing Printify products')

    @transaction.atomic
    def _process_product(self, client, shop_id, product_data):
        """Process a single product from Printify"""
        printify_id = product_data['id']
        title = product_data['title']
        description = product_data.get('description', '')
        
        # Get detailed product information
        detailed_product = client.get_product(shop_id, printify_id)
        
        self.stdout.write(f'Processing product: {title} (ID: {printify_id})')
        
        # Extract variants
        variants = detailed_product.get('variants', [])
        if not variants:
            self.stdout.write(self.style.WARNING(f'No variants found for product {title}'))
            return
            
        # Extract images
        images = detailed_product.get('images', [])
        
        # Create or update the product
        product, created = Product.objects.update_or_create(
            printify_id=printify_id,
            defaults={
                'name': title,
                'slug': self._generate_unique_slug(title, printify_id),
                'description': description,
                'price': self._get_default_price(variants),
                'stock': 100,  # Default stock value
                'is_active': True,
                'variants_json': variants  # Store the raw variant data
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created new product: {title}'))
            
            # Add to default category if it exists
            try:
                default_category = Category.objects.get(slug='custom-t-shirt')
                product.categories.add(default_category)
            except Category.DoesNotExist:
                pass
        else:
            self.stdout.write(f'Updated existing product: {title}')
            
        # Process variants
        self._process_variants(product, variants)
        
        # Process images
        self._process_images(product, images)
        
        return product
        
    def _process_variants(self, product, variants):
        """Process variants for a product"""
        # Keep track of processed variant IDs
        processed_variant_ids = []
        
        for variant in variants:
            variant_id = variant.get('id')
            if not variant_id:
                continue
                
            title = variant.get('title', '')
            price = variant.get('price', 0)
            is_enabled = variant.get('is_enabled', True)
            
            # Extract color and size from title
            color, size = self._extract_color_size(title)
            
            # Determine gender targeting based on product title or description
            gender = self._determine_gender(product.name, product.description)
            
            # Create or update the variant
            variant_obj, created = ProductVariant.objects.update_or_create(
                product=product,
                variant_id=variant_id,
                defaults={
                    'title': title,
                    'color': color,
                    'size': size,
                    'gender': gender,
                    'price': decimal.Decimal(price),
                    'is_available': is_enabled
                }
            )
            
            processed_variant_ids.append(variant_id)
            
            if created:
                self.stdout.write(f'  Created variant: {title}')
            else:
                self.stdout.write(f'  Updated variant: {title}')
                
        # Delete variants that no longer exist
        deleted_count = ProductVariant.objects.filter(
            product=product
        ).exclude(
            variant_id__in=processed_variant_ids
        ).delete()[0]
        
        if deleted_count > 0:
            self.stdout.write(f'  Deleted {deleted_count} variants that no longer exist')
            
    def _process_images(self, product, images):
        """Process images for a product"""
        # Keep track of processed image positions
        processed_positions = []
        
        for i, image in enumerate(images):
            image_url = image.get('src')
            if not image_url:
                continue
                
            is_primary = i == 0  # First image is primary
            position = i
            
            # Create or update the image
            image_obj, created = ProductImage.objects.update_or_create(
                product=product,
                image_url=image_url,
                defaults={
                    'is_primary': is_primary,
                    'alt_text': f"{product.name} - Image {i+1}"
                }
            )
            
            processed_positions.append(position)
            
            if created:
                self.stdout.write(f'  Created image at position {position}')
            else:
                self.stdout.write(f'  Updated image at position {position}')
                
        # Make sure we have at least one primary image
        if not ProductImage.objects.filter(product=product, is_primary=True).exists() and ProductImage.objects.filter(product=product).exists():
            first_image = ProductImage.objects.filter(product=product).first()
            first_image.is_primary = True
            first_image.save()
            self.stdout.write(f'  Set first image as primary')
            
    def _extract_color_size(self, title):
        """Extract color and size from variant title"""
        # Default values
        color = None
        size = None
        
        # Common pattern: "Color / Size"
        if ' / ' in title:
            parts = title.split(' / ')
            if len(parts) >= 2:
                color = parts[0].strip()
                size = parts[1].strip()
        # Size only pattern (common for some products)
        elif title.strip() in ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL']:
            size = title.strip()
            
        return color, size
        
    def _determine_gender(self, title, description):
        """Determine gender targeting based on product title or description"""
        title_desc = (title + ' ' + description).lower()
        
        if re.search(r'\bwomen\b|\bwoman\b|\bladies\b|\bfemale\b', title_desc):
            return 'Women'
        elif re.search(r'\bmen\b|\bman\b|\bmale\b', title_desc):
            return 'Men'
        elif re.search(r'\bkids\b|\bchildren\b|\bchild\b|\bboy\b|\bgirl\b', title_desc):
            return 'Kids'
        else:
            return 'Unisex'
            
    def _get_default_price(self, variants):
        """Get default price from variants"""
        if not variants:
            return decimal.Decimal('0.00')
            
        # Use the price of the first enabled variant
        for variant in variants:
            if variant.get('is_enabled', True):
                return decimal.Decimal(str(variant.get('price', 0)))
                
        # Fallback to the first variant
        return decimal.Decimal(str(variants[0].get('price', 0)))
        
    def _generate_unique_slug(self, title, printify_id):
        """Generate a unique slug for a product"""
        base_slug = slugify(title)
        
        # Check if the slug already exists
        if not Product.objects.filter(slug=base_slug).exists():
            return base_slug
            
        # If it exists, append part of the Printify ID
        short_id = printify_id[-6:]
        return f"{base_slug}-{short_id}"
