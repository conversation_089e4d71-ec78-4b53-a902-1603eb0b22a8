import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { formatINR } from '../utils/currencyFormatter';

interface TrackingInfo {
  id: string;
  order_id: string;
  printify_order_id: string;
  status: string;
  status_display: string;
  tracking_number: string;
  carrier: string;
  carrier_link: string;
  estimated_delivery: string;
  days_since_shipped: number | null;
  days_until_delivery: number | null;
  is_delivered: boolean;
  is_shipped: boolean;
  last_update: string;
  created_at: string;
}

interface Order {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  total: number;
  status: string;
  payment_status: string;
  payment_method: string;
  created_at: string;
}

const OrderTracking: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [order, setOrder] = useState<Order | null>(null);
  const [trackingInfo, setTrackingInfo] = useState<TrackingInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const response = await axios.get(`${baseUrl}/api/orders/${id}/`, {
        headers: {
          'Authorization': `JWT ${localStorage.getItem('access_token')}`
        }
      });

      setOrder(response.data);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching order:', err);
      setError(err.response?.data?.detail || 'Failed to load order details');
      setLoading(false);
    }
  };

  const fetchTrackingInfo = async () => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const response = await axios.get(`${baseUrl}/api/order_tracking/tracking/by_order/`, {
        headers: {
          'Authorization': `JWT ${localStorage.getItem('access_token')}`
        },
        params: {
          order_id: id
        }
      });

      setTrackingInfo(response.data);
    } catch (err: any) {
      console.error('Error fetching tracking info:', err);
      // Don't set error here, as we might still have order details to show
    }
  };

  const refreshTrackingInfo = async () => {
    try {
      setRefreshing(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const response = await axios.post(
        `${baseUrl}/api/order_tracking/tracking/refresh/`,
        { order_id: id },
        {
          headers: {
            'Authorization': `JWT ${localStorage.getItem('access_token')}`
          }
        }
      );

      setTrackingInfo(response.data);
      setRefreshing(false);
    } catch (err: any) {
      console.error('Error refreshing tracking info:', err);
      setError(err.response?.data?.detail || 'Failed to refresh tracking information');
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchOrderDetails();
      fetchTrackingInfo();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
        <Link to="/orders" className="text-blue-600 hover:underline">
          &larr; Back to Orders
        </Link>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          Order not found
        </div>
        <Link to="/orders" className="text-blue-600 hover:underline">
          &larr; Back to Orders
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Order Tracking</h1>
        <p className="text-gray-600">Order #{order.id}</p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="mb-2"><span className="font-medium">Order Date:</span> {new Date(order.created_at).toLocaleDateString()}</p>
            <p className="mb-2"><span className="font-medium">Status:</span> {order.status}</p>
            <p className="mb-2"><span className="font-medium">Payment Status:</span> {order.payment_status}</p>
            <p className="mb-2"><span className="font-medium">Total:</span> {formatINR(order.total)}</p>
          </div>
          <div>
            <p className="mb-2"><span className="font-medium">Shipping Address:</span></p>
            <p className="mb-1">{order.full_name}</p>
            <p className="mb-1">{order.address}</p>
            <p className="mb-1">{order.city}, {order.state} {order.zipcode}</p>
            <p className="mb-1">{order.country}</p>
          </div>
        </div>
      </div>

      {trackingInfo.length > 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Tracking Information</h2>
            <button
              onClick={refreshTrackingInfo}
              disabled={refreshing}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
            >
              {refreshing ? 'Refreshing...' : 'Refresh Tracking'}
            </button>
          </div>
          
          {trackingInfo.map((tracking) => (
            <div key={tracking.id} className="border-b pb-4 mb-4 last:border-0 last:mb-0 last:pb-0">
              {tracking.printify_order_id && (
                <p className="text-sm text-gray-500 mb-2">Printify Order ID: {tracking.printify_order_id}</p>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="mb-2">
                    <span className="font-medium">Status:</span>
                    <span className={`ml-2 font-medium
                      ${tracking.status === 'pending' ? 'text-yellow-600' : ''}
                      ${tracking.status === 'processing' ? 'text-blue-600' : ''}
                      ${tracking.status === 'shipped' ? 'text-green-600' : ''}
                      ${tracking.status === 'delivered' ? 'text-green-800' : ''}
                      ${tracking.status === 'cancelled' ? 'text-red-600' : ''}
                    `}>
                      {tracking.status_display}
                    </span>
                  </p>
                  
                  {tracking.tracking_number && (
                    <p className="mb-2">
                      <span className="font-medium">Tracking Number:</span>
                      {tracking.carrier_link ? (
                        <a href={tracking.carrier_link} target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-600 hover:underline">
                          {tracking.tracking_number}
                        </a>
                      ) : (
                        <span className="ml-2">{tracking.tracking_number}</span>
                      )}
                    </p>
                  )}
                  
                  {tracking.carrier && (
                    <p className="mb-2"><span className="font-medium">Carrier:</span> <span className="ml-2">{tracking.carrier}</span></p>
                  )}
                </div>
                
                <div>
                  {tracking.estimated_delivery && (
                    <p className="mb-2">
                      <span className="font-medium">Estimated Delivery:</span>
                      <span className="ml-2">{new Date(tracking.estimated_delivery).toLocaleDateString()}</span>
                      
                      {tracking.days_until_delivery !== null && (
                        <span className="ml-2 text-sm text-gray-500">({tracking.days_until_delivery} days remaining)</span>
                      )}
                    </p>
                  )}
                  
                  {tracking.days_since_shipped !== null && tracking.status === 'shipped' && (
                    <p className="mb-2">
                      <span className="font-medium">Shipped:</span>
                      <span className="ml-2 text-sm text-gray-500">{tracking.days_since_shipped} days ago</span>
                    </p>
                  )}
                  
                  <p className="mb-2">
                    <span className="font-medium">Last Updated:</span>
                    <span className="ml-2">{new Date(tracking.last_update).toLocaleString()}</span>
                  </p>
                </div>
              </div>
              
              {/* Shipping Progress Bar */}
              <div className="relative pt-1">
                <div className="flex mb-2 items-center justify-between">
                  <div className={`text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full 
                    ${tracking.status === 'pending' ? 'bg-yellow-200 text-yellow-800' : ''}
                    ${tracking.status === 'processing' ? 'bg-blue-200 text-blue-800' : ''}
                    ${tracking.status === 'shipped' ? 'bg-green-200 text-green-800' : ''}
                    ${tracking.status === 'delivered' ? 'bg-green-200 text-green-800' : ''}
                    ${tracking.status === 'cancelled' ? 'bg-red-200 text-red-800' : ''}
                  `}>
                    {tracking.status_display}
                  </div>
                </div>
                <div className="flex">
                  <div className="w-1/4 text-center">
                    <div className={`rounded-full h-2 mb-1 ${tracking.status !== 'pending' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-xs font-semibold inline-block text-gray-600">Order Placed</span>
                  </div>
                  <div className="w-1/4 text-center">
                    <div className={`rounded-full h-2 mb-1 ${tracking.status !== 'pending' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-xs font-semibold inline-block text-gray-600">Processing</span>
                  </div>
                  <div className="w-1/4 text-center">
                    <div className={`rounded-full h-2 mb-1 ${tracking.status === 'shipped' || tracking.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-xs font-semibold inline-block text-gray-600">Shipped</span>
                  </div>
                  <div className="w-1/4 text-center">
                    <div className={`rounded-full h-2 mb-1 ${tracking.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-xs font-semibold inline-block text-gray-600">Delivered</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="text-center py-8">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-gray-900">No tracking information available</h3>
            <p className="mt-1 text-sm text-gray-500">
              Tracking information will be available once your order has been processed and shipped.
            </p>
            <div className="mt-6">
              <button
                onClick={refreshTrackingInfo}
                disabled={refreshing}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
              >
                {refreshing ? 'Checking...' : 'Check for Updates'}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mt-6">
        <Link to={`/orders/${order.id}`} className="text-blue-600 hover:underline">
          &larr; Back to Order Details
        </Link>
      </div>
    </div>
  );
};

export default OrderTracking;
