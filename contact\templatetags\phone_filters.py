"""
Template filters for phone number handling and privacy protection
"""
from django import template

register = template.Library()

@register.filter
def mask_phone(phone_number, mask_char='X'):
    """
    Masks the last 5 digits of a phone number for privacy
    
    Usage in templates:
    {{ phone_number|mask_phone }}
    {{ phone_number|mask_phone:"*" }}
    """
    if not phone_number:
        return ''
    
    phone_str = str(phone_number)
    
    # Remove all non-digit characters except + at the beginning
    cleaned = phone_str.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
    if cleaned.startswith('+'):
        prefix = '+'
        digits = ''.join(filter(str.isdigit, cleaned[1:]))
    else:
        prefix = ''
        digits = ''.join(filter(str.isdigit, cleaned))
    
    # If the number is too short, return as is
    if len(digits) <= 5:
        return phone_str
    
    # Get the part to keep (all except last 5 digits)
    keep_digits = digits[:-5]
    
    # Create masked part (5 X's)
    masked_part = mask_char * 5
    
    # Reconstruct with formatting
    if prefix:
        if len(keep_digits) >= 2:
            # Format as +XX XXXXXXX
            country_code = keep_digits[:2]
            remaining = keep_digits[2:]
            return f"{prefix}{country_code} {remaining}{masked_part}"
        else:
            return f"{prefix}{keep_digits}{masked_part}"
    else:
        return f"{keep_digits}{masked_part}"

@register.simple_tag
def company_phone(display_type='display'):
    """
    Returns company phone number based on context
    
    Usage in templates:
    {% company_phone 'display' %}  -> Returns masked number for display
    {% company_phone 'link' %}     -> Returns full number for tel: links
    """
    COMPANY_NUMBERS = {
        'full': '+************',
        'display': '+91 935XXXXX'
    }
    
    if display_type == 'link':
        return COMPANY_NUMBERS['full']
    else:
        return COMPANY_NUMBERS['display']

@register.simple_tag
def company_contact_info():
    """
    Returns complete company contact information
    
    Usage in templates:
    {% company_contact_info as contact %}
    {{ contact.phone_display }}
    {{ contact.phone_link }}
    {{ contact.email }}
    """
    return {
        'phone_display': '+91 935XXXXX',
        'phone_link': '+************',
        'email': '<EMAIL>',
        'info_email': '<EMAIL>',
        'business_hours': 'Monday–Friday, 10 AM to 6 PM (IST)',
        'address': {
            'line1': '#42, 2nd Floor',
            'line2': 'MG Road, Bangalore',
            'line3': 'Karnataka, India 560001'
        }
    }

@register.filter
def is_company_phone(phone_number):
    """
    Checks if the phone number is a company number that should be masked
    
    Usage in templates:
    {% if phone_number|is_company_phone %}
        {{ phone_number|mask_phone }}
    {% else %}
        {{ phone_number }}
    {% endif %}
    """
    if not phone_number:
        return False
    
    phone_str = str(phone_number)
    company_numbers = ['9353014895', '935XXXXX']
    
    return any(num in phone_str for num in company_numbers)
