import React from 'react';
import { Link } from 'react-router-dom';

const DeploymentTest: React.FC = () => {
  const buildInfo = {
    timestamp: new Date().toISOString(),
    environment: process.env.REACT_APP_ENVIRONMENT || 'unknown',
    apiUrl: process.env.REACT_APP_API_URL || 'unknown',
    razorpayKey: process.env.REACT_APP_RAZORPAY_KEY_ID || 'unknown',
    paymentTestMode: process.env.REACT_APP_PAYMENT_TEST_MODE || 'unknown'
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">🚀 Deployment Test</h1>
          
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">📊 Build Information</h2>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="font-medium text-gray-700">Environment:</span>
                  <div className="text-gray-600">{buildInfo.environment}</div>
                </div>
                <div>
                  <span className="font-medium text-gray-700">API URL:</span>
                  <div className="text-gray-600 text-sm">{buildInfo.apiUrl}</div>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Razorpay Key:</span>
                  <div className="text-gray-600">{buildInfo.razorpayKey.substring(0, 15)}...</div>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Payment Test Mode:</span>
                  <div className="text-gray-600">{buildInfo.paymentTestMode}</div>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">🧪 Feature Tests</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              
              <Link
                to="/buy-tokens"
                className="block p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
              >
                <div className="flex items-center">
                  <span className="text-2xl mr-3">💰</span>
                  <div>
                    <h3 className="font-semibold text-green-800">Token Purchase Page</h3>
                    <p className="text-green-600 text-sm">Test the main token purchase feature</p>
                  </div>
                </div>
              </Link>

              <Link
                to="/wallet"
                className="block p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <div className="flex items-center">
                  <span className="text-2xl mr-3">🪙</span>
                  <div>
                    <h3 className="font-semibold text-blue-800">Smart Wallet</h3>
                    <p className="text-blue-600 text-sm">Check if Buy Tokens button appears</p>
                  </div>
                </div>
              </Link>

              <Link
                to="/quick-login"
                className="block p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
              >
                <div className="flex items-center">
                  <span className="text-2xl mr-3">🔐</span>
                  <div>
                    <h3 className="font-semibold text-purple-800">Quick Login</h3>
                    <p className="text-purple-600 text-sm">Login with test account</p>
                  </div>
                </div>
              </Link>

              <Link
                to="/modal-debug"
                className="block p-4 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors"
              >
                <div className="flex items-center">
                  <span className="text-2xl mr-3">🔍</span>
                  <div>
                    <h3 className="font-semibold text-yellow-800">Modal Debug</h3>
                    <p className="text-yellow-600 text-sm">Test modal functionality</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">📋 Deployment Checklist</h2>
            <div className="space-y-2">
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✅</span>
                <span>TokenPurchasePage component exists</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✅</span>
                <span>Route /buy-tokens configured</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✅</span>
                <span>WalletPage updated with Buy Tokens button</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✅</span>
                <span>Environment variables configured</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✅</span>
                <span>Latest commit pushed to GitHub</span>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 mb-2">🔧 Troubleshooting Steps</h3>
            <ol className="list-decimal list-inside text-blue-700 space-y-1">
              <li>Clear browser cache (Ctrl+Shift+R or Cmd+Shift+R)</li>
              <li>Check browser console for JavaScript errors (F12)</li>
              <li>Verify you're logged in before accessing wallet</li>
              <li>Check Vercel deployment logs for build errors</li>
              <li>Verify environment variables in Vercel dashboard</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeploymentTest;
