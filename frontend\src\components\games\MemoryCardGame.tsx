import React, { useState, useEffect, useCallback } from 'react';
import { gameSessionService } from '../../services/gameSessionService';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';

interface Card {
  id: number;
  emoji: string;
  isFlipped: boolean;
  isMatched: boolean;
}

interface GameState {
  cards: Card[];
  playerScore: number;
  botScore: number;
  currentPlayer: 'player' | 'bot';
  selectedCards: number[];
  gameStatus: 'waiting' | 'playing' | 'finished';
  gameId: string | null;
  botMemory: { [key: number]: string };
  turnCount: number;
}

const CARD_EMOJIS = ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'];

const MemoryCardGame: React.FC = () => {
  const { user } = useAuth();
  const { wallet, refreshWallet } = useWallet();
  
  const [gameState, setGameState] = useState<GameState>({
    cards: [],
    playerScore: 0,
    botScore: 0,
    currentPlayer: 'player',
    selectedCards: [],
    gameStatus: 'waiting',
    gameId: null,
    botMemory: {},
    turnCount: 0
  });

  const [showResult, setShowResult] = useState(false);
  const [gameResult, setGameResult] = useState<'win' | 'loss' | 'draw' | null>(null);
  const [tokensEarned, setTokensEarned] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Initialize cards
  const initializeCards = useCallback(() => {
    const cardPairs = [...CARD_EMOJIS, ...CARD_EMOJIS];
    const shuffledCards = cardPairs
      .sort(() => Math.random() - 0.5)
      .map((emoji, index) => ({
        id: index,
        emoji,
        isFlipped: false,
        isMatched: false
      }));
    
    setGameState(prev => ({
      ...prev,
      cards: shuffledCards,
      playerScore: 0,
      botScore: 0,
      currentPlayer: 'player',
      selectedCards: [],
      botMemory: {},
      turnCount: 0
    }));
  }, []);

  // Start new game
  const startGame = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await gameSessionService.startGameSession('memory_card');

      if (result.success) {
        setGameState(prev => ({
          ...prev,
          gameId: result.session_id,
          gameStatus: 'playing'
        }));

        initializeCards();
        setShowResult(false);

        // Refresh wallet to show updated balance
        await refreshWallet();
      } else {
        alert(result.error || 'Failed to start game');
      }
    } catch (error) {
      console.error('Error starting game:', error);
      alert('Failed to start game');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle card click
  const handleCardClick = (cardId: number) => {
    if (
      gameState.gameStatus !== 'playing' || 
      gameState.currentPlayer !== 'player' ||
      gameState.selectedCards.length >= 2 ||
      gameState.cards[cardId].isFlipped ||
      gameState.cards[cardId].isMatched ||
      isProcessing
    ) return;

    setGameState(prev => ({
      ...prev,
      cards: prev.cards.map(card => 
        card.id === cardId ? { ...card, isFlipped: true } : card
      ),
      selectedCards: [...prev.selectedCards, cardId]
    }));
  };

  // Check for matches
  const checkMatch = useCallback(() => {
    if (gameState.selectedCards.length === 2) {
      setIsProcessing(true);
      const [firstId, secondId] = gameState.selectedCards;
      const firstCard = gameState.cards[firstId];
      const secondCard = gameState.cards[secondId];
      
      const isMatch = firstCard.emoji === secondCard.emoji;
      
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          cards: prev.cards.map(card => {
            if (card.id === firstId || card.id === secondId) {
              return {
                ...card,
                isMatched: isMatch,
                isFlipped: isMatch
              };
            }
            return { ...card, isFlipped: card.isMatched };
          }),
          playerScore: prev.playerScore + (isMatch ? 1 : 0),
          selectedCards: [],
          currentPlayer: 'bot',
          turnCount: prev.turnCount + 1
        }));
        
        setIsProcessing(false);
        
        // Check if game is finished
        const allMatched = gameState.cards.every(card => 
          card.isMatched || gameState.cards.filter(c => c.emoji === card.emoji).length === 2
        );
        
        if (!allMatched) {
          // Bot turn after delay
          setTimeout(() => {
            handleBotTurn();
          }, 1500);
        } else {
          finishGame();
        }
      }, 1500);
    }
  }, [gameState.selectedCards, gameState.cards]);

  // Bot AI logic with memory
  const handleBotTurn = () => {
    setIsProcessing(true);
    
    // Update bot memory with recently seen cards
    const newMemory = { ...gameState.botMemory };
    gameState.cards.forEach(card => {
      if (card.isFlipped && !card.isMatched) {
        newMemory[card.id] = card.emoji;
      }
    });

    // Find available (unmatched, unflipped) cards
    const availableCards = gameState.cards.filter(card => !card.isMatched && !card.isFlipped);
    
    if (availableCards.length < 2) {
      finishGame();
      return;
    }

    // Bot strategy: Try to find matches in memory first
    let firstChoice: Card | null = null;
    let secondChoice: Card | null = null;

    // Look for known matches in memory
    for (const [cardId, emoji] of Object.entries(newMemory)) {
      const matchingCards = availableCards.filter(card => card.emoji === emoji);
      if (matchingCards.length >= 2) {
        firstChoice = matchingCards[0];
        secondChoice = matchingCards[1];
        break;
      }
    }

    // If no known matches, pick randomly (with some memory after turn 3)
    if (!firstChoice || !secondChoice) {
      if (gameState.turnCount >= 3 && Object.keys(newMemory).length > 0) {
        // 70% chance to use memory
        if (Math.random() < 0.7) {
          const memoryCards = availableCards.filter(card => 
            Object.values(newMemory).includes(card.emoji)
          );
          if (memoryCards.length > 0) {
            firstChoice = memoryCards[Math.floor(Math.random() * memoryCards.length)];
          }
        }
      }
      
      if (!firstChoice) {
        firstChoice = availableCards[Math.floor(Math.random() * availableCards.length)];
      }
      
      const remainingCards = availableCards.filter(card => card.id !== firstChoice!.id);
      secondChoice = remainingCards[Math.floor(Math.random() * remainingCards.length)];
    }

    // Flip bot's choices
    setTimeout(() => {
      setGameState(prev => ({
        ...prev,
        cards: prev.cards.map(card => 
          card.id === firstChoice!.id ? { ...card, isFlipped: true } : card
        ),
        botMemory: newMemory
      }));

      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          cards: prev.cards.map(card => 
            card.id === secondChoice!.id ? { ...card, isFlipped: true } : card
          )
        }));

        // Check bot's match
        setTimeout(() => {
          const isMatch = firstChoice!.emoji === secondChoice!.emoji;
          
          setGameState(prev => ({
            ...prev,
            cards: prev.cards.map(card => {
              if (card.id === firstChoice!.id || card.id === secondChoice!.id) {
                return {
                  ...card,
                  isMatched: isMatch,
                  isFlipped: isMatch
                };
              }
              return { ...card, isFlipped: card.isMatched };
            }),
            botScore: prev.botScore + (isMatch ? 1 : 0),
            currentPlayer: 'player',
            turnCount: prev.turnCount + 1
          }));

          setIsProcessing(false);

          // Check if game is finished
          const allMatched = gameState.cards.filter(card => !card.isMatched).length <= 2;
          if (allMatched) {
            setTimeout(() => finishGame(), 1000);
          }
        }, 1500);
      }, 1000);
    }, 1000);
  };

  // Finish game and calculate result
  const finishGame = async () => {
    if (!gameState.gameId) return;

    let result: 'win' | 'loss' | 'draw';
    if (gameState.playerScore > gameState.botScore) {
      result = 'win';
    } else if (gameState.playerScore < gameState.botScore) {
      result = 'loss';
    } else {
      result = 'draw';
    }

    setGameResult(result);
    setGameState(prev => ({ ...prev, gameStatus: 'finished' }));

    // Submit result to backend using new GameSession API
    try {
      const gameData = {
        matches: {
          player: gameState.playerScore,
          bot: gameState.botScore
        },
        turns: gameState.turnCount,
        total_pairs: 8 // 16 cards = 8 pairs
      };

      const submitResult = await gameSessionService.completeGameSession(
        gameState.gameId,
        result,
        gameData
      );

      if (submitResult.success) {
        setTokensEarned(submitResult.tokens_earned);
        await refreshWallet();
      }
    } catch (error) {
      console.error('Error submitting game result:', error);
    }

    setShowResult(true);
  };

  // Effect to check matches
  useEffect(() => {
    if (gameState.selectedCards.length === 2) {
      checkMatch();
    }
  }, [gameState.selectedCards, checkMatch]);

  if (showResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h2 className="text-3xl font-bold mb-6">🧠 Game Complete!</h2>
          
          <div className="space-y-4 mb-6">
            <div className="flex justify-between text-lg">
              <span>Your Matches:</span>
              <span className="font-bold">{gameState.playerScore}</span>
            </div>
            <div className="flex justify-between text-lg">
              <span>Bot Matches:</span>
              <span className="font-bold">{gameState.botScore}</span>
            </div>
          </div>

          <div className="mb-6">
            {gameResult === 'win' && (
              <div className="text-green-400">
                <div className="text-2xl font-bold">🎉 You Won!</div>
                <div className="text-lg">+{tokensEarned} tokens</div>
              </div>
            )}
            {gameResult === 'loss' && (
              <div className="text-red-400">
                <div className="text-2xl font-bold">😔 You Lost</div>
                <div className="text-lg">{tokensEarned} tokens</div>
              </div>
            )}
            {gameResult === 'draw' && (
              <div className="text-yellow-400">
                <div className="text-2xl font-bold">🤝 Draw!</div>
                <div className="text-lg">+{tokensEarned} tokens</div>
              </div>
            )}
          </div>

          <div className="mb-6">
            <div className="text-sm opacity-75">Current Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-indigo-500 to-purple-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Play Again'}
          </button>
        </div>
      </div>
    );
  }

  if (gameState.gameStatus === 'waiting') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h1 className="text-4xl font-bold mb-4">🧠 Memory Match</h1>
          <p className="text-lg mb-4 opacity-90">
            Find matching pairs! You and the bot take turns flipping cards. Most matches wins!
          </p>

          <div className="mb-4">
            <div className="px-3 py-2 bg-yellow-50/20 border border-yellow-200/30 rounded-lg inline-block">
              <span className="text-sm font-medium text-yellow-200">🟡 Medium Difficulty</span>
            </div>
          </div>
          
          <div className="mb-6">
            <div className="text-sm opacity-75">Your Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <div className="mb-6 text-sm opacity-75">
            <div>Win: +5 tokens</div>
            <div>Draw: +2 tokens</div>
            <div>Lose: -1 token</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-indigo-500 to-purple-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Start Game'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Game Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 text-white">
          <div className="flex justify-between items-center">
            <div className="text-center">
              <div className="text-sm opacity-75">Your Matches</div>
              <div className="text-2xl font-bold">{gameState.playerScore}</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold">
                {gameState.currentPlayer === 'player' ? "Your Turn" : "Bot's Turn"}
              </div>
              <div className="text-sm opacity-75">Turn {gameState.turnCount + 1}</div>
            </div>
            <div className="text-center">
              <div className="text-sm opacity-75">Bot Matches</div>
              <div className="text-2xl font-bold">{gameState.botScore}</div>
            </div>
          </div>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-4 gap-3 max-w-lg mx-auto">
          {gameState.cards.map((card) => (
            <button
              key={card.id}
              onClick={() => handleCardClick(card.id)}
              disabled={
                gameState.currentPlayer !== 'player' || 
                card.isMatched || 
                gameState.selectedCards.length >= 2 ||
                isProcessing
              }
              className={`
                aspect-square rounded-xl text-4xl font-bold
                transition-all duration-300 transform hover:scale-105
                ${card.isFlipped || card.isMatched
                  ? 'bg-white text-gray-800' 
                  : 'bg-white/20 text-transparent hover:bg-white/30'
                }
                ${card.isMatched ? 'ring-4 ring-green-400' : ''}
                disabled:cursor-not-allowed
              `}
            >
              {(card.isFlipped || card.isMatched) ? card.emoji : '?'}
            </button>
          ))}
        </div>

        {isProcessing && (
          <div className="text-center mt-6 text-white">
            <div className="text-lg">Processing turn...</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MemoryCardGame;
