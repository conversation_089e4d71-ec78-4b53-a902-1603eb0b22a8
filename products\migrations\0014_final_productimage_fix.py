# Generated manually to definitively fix ProductImage model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0013_simplify_productimage'),
    ]

    operations = [
        # Ensure the model fields are correctly defined
        migrations.AlterField(
            model_name='productimage',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='products/'),
        ),
        
        migrations.AlterField(
            model_name='productimage',
            name='image_url',
            field=models.URLField(blank=True, null=True, help_text='URL for the image (used for display only)'),
        ),
    ]
