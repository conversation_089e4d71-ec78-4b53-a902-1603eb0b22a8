from django.db import models
from django.core.exceptions import ValidationError
from django.core.files.images import get_image_dimensions
import os

def validate_image(image):
    """
    Validate that the uploaded image meets our requirements.

    Args:
        image: The uploaded image file

    Raises:
        ValidationError: If the image is too large or dimensions are too big
    """
    if image:
        # Check file size (max 2MB)
        if image.size > 2 * 1024 * 1024:
            raise ValidationError("Image file too large (max 2MB)")

        # Check dimensions
        width, height = get_image_dimensions(image)
        if width > 1200 or height > 1200:
            raise ValidationError("Image dimensions too large (max 1200x1200)")

def team_photo_path(instance, filename):
    """
    Generate the upload path for team member photos.

    Args:
        instance: The TeamMember instance
        filename: The original filename

    Returns:
        str: The path where the file will be stored
    """
    # Get the file extension
    ext = filename.split('.')[-1]
    # Generate a new filename using the member's name (slugified)
    slug = instance.name.lower().replace(' ', '-')
    filename = f"{slug}.{ext}"
    return os.path.join('team', 'photos', filename)

class TeamMember(models.Model):
    """
    Model representing a team member.

    This model stores information about team members including their name,
    role, photo, and LinkedIn profile URL.
    """
    name = models.CharField(
        max_length=100,
        help_text="The team member's full name"
    )
    role = models.CharField(
        max_length=100,
        help_text="The team member's role or position"
    )
    photo = models.ImageField(
        upload_to=team_photo_path,
        validators=[validate_image],
        blank=True,
        null=True,
        help_text="Profile photo (max 2MB, 1200x1200)"
    )
    linkedin_url = models.URLField(
        blank=True,
        null=True,
        help_text="LinkedIn profile URL (optional)"
    )
    order = models.PositiveIntegerField(
        default=0,
        help_text="Display order (lower numbers displayed first)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Team Member"
        verbose_name_plural = "Team Members"
        ordering = ['order', 'name']

    def __str__(self):
        return f"{self.name} - {self.role}"

    def clean(self):
        """
        Validate the model fields.

        Ensures that LinkedIn URLs are properly formatted.
        """
        if self.linkedin_url and not self.linkedin_url.startswith(('http://', 'https://')):
            self.linkedin_url = f"https://{self.linkedin_url}"
