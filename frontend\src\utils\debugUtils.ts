/**
 * Frontend debugging utilities for troubleshooting image and API issues
 */

interface DebugInfo {
  timestamp: string;
  userAgent: string;
  url: string;
  apiUrl: string;
  environment: string;
}

interface APITestResult {
  endpoint: string;
  success: boolean;
  status: number;
  data?: any;
  error?: string;
  responseTime: number;
}

interface ImageTestResult {
  url: string;
  accessible: boolean;
  loadTime: number;
  error?: string;
  dimensions?: { width: number; height: number };
}

/**
 * Get comprehensive debug information
 */
export const getDebugInfo = (): DebugInfo => {
  return {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    apiUrl: process.env.REACT_APP_API_URL || 'Not configured',
    environment: process.env.NODE_ENV || 'unknown'
  };
};

/**
 * Test API endpoint connectivity
 */
export const testAPIEndpoint = async (endpoint: string): Promise<APITestResult> => {
  const startTime = Date.now();
  const fullUrl = `${process.env.REACT_APP_API_URL}${endpoint}`;
  
  try {
    console.log(`🔍 Testing API endpoint: ${fullUrl}`);
    
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    const responseTime = Date.now() - startTime;
    const data = await response.json();
    
    const result: APITestResult = {
      endpoint: fullUrl,
      success: response.ok,
      status: response.status,
      data: data,
      responseTime
    };
    
    if (response.ok) {
      console.log(`✅ API test successful: ${endpoint} (${responseTime}ms)`);
    } else {
      console.error(`❌ API test failed: ${endpoint} - Status ${response.status}`);
      result.error = `HTTP ${response.status}`;
    }
    
    return result;
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const result: APITestResult = {
      endpoint: fullUrl,
      success: false,
      status: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime
    };
    
    console.error(`❌ API test error: ${endpoint}`, error);
    return result;
  }
};

/**
 * Test image URL accessibility
 */
export const testImageURL = async (imageUrl: string): Promise<ImageTestResult> => {
  return new Promise((resolve) => {
    const startTime = Date.now();
    const img = new Image();
    
    const cleanup = () => {
      img.onload = null;
      img.onerror = null;
    };
    
    img.onload = () => {
      const loadTime = Date.now() - startTime;
      console.log(`✅ Image loaded successfully: ${imageUrl.substring(0, 60)}... (${loadTime}ms)`);
      
      cleanup();
      resolve({
        url: imageUrl,
        accessible: true,
        loadTime,
        dimensions: {
          width: img.naturalWidth,
          height: img.naturalHeight
        }
      });
    };
    
    img.onerror = (error) => {
      const loadTime = Date.now() - startTime;
      console.error(`❌ Image failed to load: ${imageUrl.substring(0, 60)}...`, error);
      
      cleanup();
      resolve({
        url: imageUrl,
        accessible: false,
        loadTime,
        error: 'Failed to load image'
      });
    };
    
    // Set timeout
    setTimeout(() => {
      cleanup();
      resolve({
        url: imageUrl,
        accessible: false,
        loadTime: Date.now() - startTime,
        error: 'Timeout'
      });
    }, 15000);
    
    img.crossOrigin = 'anonymous';
    img.src = imageUrl;
  });
};

/**
 * Comprehensive frontend diagnostic
 */
export const runFrontendDiagnostic = async () => {
  console.group('🔍 FRONTEND DIAGNOSTIC STARTING');
  
  const debugInfo = getDebugInfo();
  console.log('Debug Info:', debugInfo);
  
  // Test API endpoints
  console.group('🌐 API Connectivity Tests');
  
  const apiTests = [
    '/api/',
    '/api/products/items/',
    '/api/products/categories/',
    '/api/health/printify/'
  ];
  
  const apiResults: APITestResult[] = [];
  
  for (const endpoint of apiTests) {
    const result = await testAPIEndpoint(endpoint);
    apiResults.push(result);
  }
  
  console.groupEnd();
  
  // Test sample images
  console.group('🖼️ Image Accessibility Tests');
  
  const imageResults: ImageTestResult[] = [];
  
  // Get sample product to test images
  const productsResult = apiResults.find(r => r.endpoint.includes('/api/products/items/'));
  if (productsResult && productsResult.success && productsResult.data) {
    const products = productsResult.data.results || productsResult.data;
    const sampleProducts = products.slice(0, 3); // Test first 3 products
    
    for (const product of sampleProducts) {
      if (product.main_image?.image) {
        console.log(`Testing image for product: ${product.name}`);
        const imageResult = await testImageURL(product.main_image.image);
        imageResults.push(imageResult);
      }
    }
  }
  
  console.groupEnd();
  
  // Generate summary
  console.group('📊 DIAGNOSTIC SUMMARY');
  
  const apiSuccessRate = (apiResults.filter(r => r.success).length / apiResults.length) * 100;
  const imageSuccessRate = imageResults.length > 0 
    ? (imageResults.filter(r => r.accessible).length / imageResults.length) * 100 
    : 0;
  
  console.log(`API Success Rate: ${apiSuccessRate.toFixed(1)}% (${apiResults.filter(r => r.success).length}/${apiResults.length})`);
  console.log(`Image Success Rate: ${imageSuccessRate.toFixed(1)}% (${imageResults.filter(r => r.accessible).length}/${imageResults.length})`);
  
  // Check for common issues
  const issues: string[] = [];
  
  if (apiSuccessRate < 100) {
    issues.push('API connectivity issues detected');
  }
  
  if (imageSuccessRate < 100) {
    issues.push('Image loading issues detected');
  }
  
  if (!process.env.REACT_APP_API_URL) {
    issues.push('REACT_APP_API_URL not configured');
  }
  
  if (issues.length > 0) {
    console.warn('🚨 Issues detected:', issues);
  } else {
    console.log('✅ No issues detected');
  }
  
  console.groupEnd();
  console.groupEnd();
  
  return {
    debugInfo,
    apiResults,
    imageResults,
    summary: {
      apiSuccessRate,
      imageSuccessRate,
      issues
    }
  };
};

/**
 * Clear all browser cache and storage
 */
export const clearBrowserCache = () => {
  console.log('🧹 Clearing browser cache and storage...');
  
  // Clear localStorage
  localStorage.clear();
  
  // Clear sessionStorage
  sessionStorage.clear();
  
  // Clear cache if available
  if ('caches' in window) {
    caches.keys().then(names => {
      names.forEach(name => {
        caches.delete(name);
      });
    });
  }
  
  console.log('✅ Browser cache cleared');
  console.log('💡 Recommendation: Hard refresh the page (Ctrl+F5)');
};

/**
 * Monitor image loading in real-time
 */
export const monitorImageLoading = () => {
  console.log('👀 Starting image loading monitor...');
  
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;
          const images = element.tagName === 'IMG' 
            ? [element as HTMLImageElement]
            : Array.from(element.querySelectorAll('img'));
          
          images.forEach((img) => {
            if (img.src && img.src.includes('printify')) {
              console.log(`🖼️ Printify image detected: ${img.src.substring(0, 60)}...`);
              
              img.addEventListener('load', () => {
                console.log(`✅ Printify image loaded: ${img.src.substring(0, 60)}...`);
              });
              
              img.addEventListener('error', () => {
                console.error(`❌ Printify image failed: ${img.src.substring(0, 60)}...`);
              });
            }
          });
        }
      });
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  console.log('✅ Image loading monitor started');
  
  return () => {
    observer.disconnect();
    console.log('🛑 Image loading monitor stopped');
  };
};

// Global debug functions for browser console
declare global {
  interface Window {
    debugFrontend: () => Promise<any>;
    clearCache: () => void;
    monitorImages: () => () => void;
    testAPI: (endpoint: string) => Promise<APITestResult>;
    testImage: (url: string) => Promise<ImageTestResult>;
  }
}

// Expose debug functions globally
window.debugFrontend = runFrontendDiagnostic;
window.clearCache = clearBrowserCache;
window.monitorImages = monitorImageLoading;
window.testAPI = testAPIEndpoint;
window.testImage = testImageURL;
