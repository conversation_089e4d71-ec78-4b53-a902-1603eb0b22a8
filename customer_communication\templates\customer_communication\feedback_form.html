{% extends "base.html" %}
{% load static %}

{% block title %}{{ title }} | PickMeTrend{% endblock %}

{% block extra_css %}
<style>
    .form-group {
        margin-bottom: 1.5rem;
    }
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
    }
    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
    .btn-primary {
        background-color: #4f46e5;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
        border: none;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    .btn-primary:hover {
        background-color: #4338ca;
    }
    .alert {
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1.5rem;
    }
    .alert-success {
        background-color: #ecfdf5;
        color: #065f46;
        border: 1px solid #10b981;
    }
    .alert-danger {
        background-color: #fef2f2;
        color: #b91c1c;
        border: 1px solid #ef4444;
    }
    .hidden {
        display: none;
    }
    .rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }
    .rating input {
        display: none;
    }
    .rating label {
        cursor: pointer;
        width: 40px;
        height: 40px;
        margin: 0 5px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23d1d5db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2'%3E%3C/polygon%3E%3C/svg%3E");
        transition: transform 0.2s;
    }
    .rating input:checked ~ label,
    .rating label:hover,
    .rating input:checked + label:hover,
    .rating input:checked ~ label:hover {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23fbbf24' stroke='%23fbbf24' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2'%3E%3C/polygon%3E%3C/svg%3E");
        transform: scale(1.2);
    }
    .rating label:hover ~ label {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23fbbf24' stroke='%23fbbf24' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2'%3E%3C/polygon%3E%3C/svg%3E");
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-12">
    <div class="max-w-3xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">{{ title }}</h1>
        <p class="text-lg text-gray-600 mb-8">{{ description }}</p>
        
        <div class="bg-white rounded-lg shadow-md p-8">
            <div id="success-message" class="alert alert-success hidden">
                Thank you for your feedback! Your input helps us improve our products and services.
            </div>
            
            <div id="error-message" class="alert alert-danger hidden">
                There was an error submitting your feedback. Please try again or contact us directly.
            </div>
            
            <form id="feedback-form" class="space-y-6">
                <input type="hidden" id="order_id" name="order_id" value="{{ order_id }}">
                
                <div class="form-group">
                    <label class="form-label">How would you rate your overall experience?</label>
                    <div class="rating">
                        <input type="radio" id="star5" name="rating" value="5" required>
                        <label for="star5" title="5 stars"></label>
                        <input type="radio" id="star4" name="rating" value="4">
                        <label for="star4" title="4 stars"></label>
                        <input type="radio" id="star3" name="rating" value="3">
                        <label for="star3" title="3 stars"></label>
                        <input type="radio" id="star2" name="rating" value="2">
                        <label for="star2" title="2 stars"></label>
                        <input type="radio" id="star1" name="rating" value="1">
                        <label for="star1" title="1 star"></label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="comment" class="form-label">Additional Comments (Optional)</label>
                    <textarea id="comment" name="comment" rows="6" class="form-control"></textarea>
                </div>
                
                <div class="text-right">
                    <button type="submit" class="btn-primary">Submit Feedback</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('feedback-form');
        const successMessage = document.getElementById('success-message');
        const errorMessage = document.getElementById('error-message');
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Hide any previous messages
            successMessage.classList.add('hidden');
            errorMessage.classList.add('hidden');
            
            // Get selected rating
            const rating = document.querySelector('input[name="rating"]:checked');
            if (!rating) {
                errorMessage.textContent = 'Please select a rating.';
                errorMessage.classList.remove('hidden');
                return;
            }
            
            // Get form data
            const formData = {
                order: document.getElementById('order_id').value,
                rating: parseInt(rating.value),
                comment: document.getElementById('comment').value
            };
            
            // Send the data to the API
            fetch('/customer/api/feedback/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Show success message
                successMessage.classList.remove('hidden');
                // Reset form
                form.reset();
            })
            .catch(error => {
                // Show error message
                errorMessage.textContent = 'There was an error submitting your feedback. Please try again or contact us directly.';
                errorMessage.classList.remove('hidden');
                console.error('Error:', error);
            });
        });
        
        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
