from .models import TeamMember

def team_members(request):
    """
    Context processor that adds team members to all templates.
    
    This makes the team members available in all templates as the
    'team_members' variable.
    
    Args:
        request: The HTTP request object
        
    Returns:
        dict: A dictionary containing the team_members queryset
    """
    try:
        # Get all team members ordered by their display order
        members = TeamMember.objects.all()
        return {
            'team_members': members
        }
    except Exception:
        # Return an empty list if there's an error
        return {
            'team_members': []
        }
