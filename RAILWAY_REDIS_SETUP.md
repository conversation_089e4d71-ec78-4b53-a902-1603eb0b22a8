# Railway Redis Service Setup Guide

## Step 1: Add Redis Service in Railway

### Via Railway Dashboard:
1. Go to your Railway project: https://railway.app/dashboard
2. Click "New Service" 
3. Select "Redis" from the service templates
4. Railway will automatically provision a Redis instance

### Via Railway CLI:
```bash
railway service create redis
```

## Step 2: Railway Redis Environment Variables

Railway automatically provides these environment variables for Redis:

```bash
# Railway Redis Environment Variables
REDIS_URL=redis://default:<EMAIL>:6379
REDIS_HOST=redis.railway.internal
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_USERNAME=default
```

## Step 3: Connect Redis to Your Django App

### Option A: Automatic Connection (Recommended)
Railway automatically connects services in the same project. Your Django app will automatically receive the Redis environment variables.

### Option B: Manual Connection
If you need to manually connect:

1. **In Railway Dashboard:**
   - Go to your Django app service
   - Click "Variables" tab
   - Add the Redis environment variables from the Redis service

2. **Or use Railway CLI:**
```bash
railway variables set REDIS_URL=redis://default:<EMAIL>:6379
```

## Step 4: Verify Connection

### Check Environment Variables:
```bash
# In Railway dashboard or via CLI
railway variables list
```

### Test Redis Connection:
```bash
# Run the test script in Railway
railway run python test_redis_connection.py
```

## Step 5: Update Your Application

Your Django app is already configured to use Railway Redis! The settings.py file will automatically:

1. **Detect Railway environment** (when `DEBUG=False`)
2. **Use Railway Redis URL** as default
3. **Configure Django Channels** for WebSocket gaming
4. **Set up Celery** for background tasks

## Railway Redis Features

### ✅ **Automatic Benefits:**
- **Internal Network**: `redis.railway.internal` (secure, no external access needed)
- **Auto-scaling**: Railway handles Redis scaling
- **Backup**: Automatic Redis backups
- **Monitoring**: Built-in Redis monitoring
- **SSL/TLS**: Secure connections

### 🔧 **Configuration:**
```python
# Your settings.py already handles this:
if DEBUG:
    REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
else:
    # Production: Uses Railway Redis automatically
    REDIS_URL = os.environ.get('REDIS_URL', 'redis://default:<EMAIL>:6379')
```

## Step 6: Deploy and Test

### Deploy to Railway:
```bash
railway up
```

### Check Logs:
```bash
railway logs
```

### Monitor Redis:
- Go to your Redis service in Railway dashboard
- Check "Metrics" tab for Redis performance
- Monitor memory usage and connections

## Troubleshooting

### Common Issues:

1. **Connection Refused:**
   - Ensure Redis service is running
   - Check if services are in same Railway project
   - Verify environment variables are set

2. **Authentication Failed:**
   - Check Redis password in environment variables
   - Ensure username is 'default'

3. **Network Issues:**
   - Use `redis.railway.internal` (internal network)
   - Don't use external Redis URLs in Railway

### Debug Commands:
```bash
# Check Railway services
railway service list

# Check environment variables
railway variables list

# Test Redis connection
railway run python test_redis_connection.py

# View logs
railway logs
```

## Best Practices

1. **Use Internal URLs**: Always use `redis.railway.internal` for Railway Redis
2. **Environment Variables**: Let Railway manage Redis credentials
3. **Monitoring**: Regularly check Redis metrics in Railway dashboard
4. **Backup**: Railway handles Redis backups automatically
5. **Scaling**: Monitor Redis usage and scale as needed

## Cost Optimization

- **Free Tier**: Railway provides free Redis for small projects
- **Scaling**: Pay only for what you use
- **Monitoring**: Use Railway metrics to optimize Redis usage 