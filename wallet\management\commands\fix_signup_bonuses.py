from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from wallet.models import Wallet, WalletTransaction


class Command(BaseCommand):
    help = 'Fix signup bonuses for existing users who don\'t have them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        self.stdout.write('🔍 Checking users for missing signup bonuses...')
        
        # Get all users
        all_users = User.objects.all()
        users_fixed = 0
        users_already_have_bonus = 0
        users_without_wallet = 0
        
        for user in all_users:
            # Check if user has a wallet
            try:
                wallet = user.wallet
            except Wallet.DoesNotExist:
                # Create wallet and give signup bonus
                if not dry_run:
                    wallet = Wallet.objects.create(user=user)
                    wallet.add_tokens(
                        amount=100,
                        transaction_type='signup_bonus',
                        description='Welcome bonus for existing user'
                    )
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ Created wallet and gave 100 tokens to {user.username}')
                    )
                else:
                    self.stdout.write(f'Would create wallet and give 100 tokens to {user.username}')
                users_without_wallet += 1
                users_fixed += 1
                continue
            
            # Check if user already has signup bonus
            has_signup_bonus = WalletTransaction.objects.filter(
                wallet=wallet,
                transaction_type='signup_bonus'
            ).exists()
            
            if has_signup_bonus:
                users_already_have_bonus += 1
                continue
            
            # Give signup bonus to existing user
            if not dry_run:
                wallet.add_tokens(
                    amount=100,
                    transaction_type='signup_bonus',
                    description='Welcome bonus for existing user (retroactive)'
                )
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Gave 100 token signup bonus to {user.username}')
                )
            else:
                self.stdout.write(f'Would give 100 token signup bonus to {user.username}')
            
            users_fixed += 1
        
        # Summary
        self.stdout.write('\n📊 Summary:')
        self.stdout.write(f'Total users: {all_users.count()}')
        self.stdout.write(f'Users already had signup bonus: {users_already_have_bonus}')
        self.stdout.write(f'Users without wallet: {users_without_wallet}')
        self.stdout.write(f'Users fixed: {users_fixed}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nThis was a dry run. Run without --dry-run to apply changes.')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'\n🎉 Successfully fixed signup bonuses for {users_fixed} users!')
            )
