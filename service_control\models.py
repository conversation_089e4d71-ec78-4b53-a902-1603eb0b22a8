from django.db import models
from django.core.exceptions import ValidationError


class ServiceControl(models.Model):
    """
    Model to control the status of various services like Celery and Redis.
    Only one instance of this model should exist.
    """
    celery_enabled = models.BooleanField(
        default=True,
        verbose_name="Celery Enabled",
        help_text="Enable or disable Celery task processing"
    )
    redis_enabled = models.BooleanField(
        default=True,
        verbose_name="Redis Enabled",
        help_text="Enable or disable Redis service"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Service Control"
        verbose_name_plural = "Service Controls"

    def __str__(self):
        celery_status = "ON" if self.celery_enabled else "OFF"
        redis_status = "ON" if self.redis_enabled else "OFF"
        return f"Service Control - Celery: {celery_status}, Redis: {redis_status}"

    def clean(self):
        """
        Ensure only one instance of ServiceControl exists.
        """
        if not self.pk and ServiceControl.objects.exists():
            raise ValidationError("Only one Service Control instance can exist.")

    def save(self, *args, **kwargs):
        """
        Override save method to ensure only one instance exists.
        """
        self.clean()
        return super().save(*args, **kwargs)

    @classmethod
    def get_instance(cls):
        """
        Get the ServiceControl instance, creating it if it doesn't exist.

        Returns:
            ServiceControl: The ServiceControl instance
        """
        instance, created = cls.objects.get_or_create(pk=1)
        return instance
