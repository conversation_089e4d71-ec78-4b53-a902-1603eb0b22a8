import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios, { AxiosResponse } from 'axios';

interface AuthResponse {
  access: string;
  refresh: string;
  user?: any;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string, re_password: string, first_name?: string, last_name?: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  error: string | null;
  updateUser: (userData: any) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is already authenticated
    const token = localStorage.getItem('access_token');
    if (token) {
      setIsAuthenticated(true);
      // Fetch user data
      fetchUserData(token);
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUserData = async (token: string) => {
    try {
      if (!token) {
        console.error('No token provided to fetchUserData');
        setIsAuthenticated(false);
        setUser(null);
        setLoading(false);
        return;
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const userUrl = `${baseUrl}/api/auth/djoser/users/me/`;

      console.log('Fetching user data from:', userUrl);
      console.log('Using token:', token.substring(0, 10) + '...');

      // Try both JWT and Bearer token formats
      const authHeaders = [
        { name: 'JWT', header: `JWT ${token}` },
        { name: 'Bearer', header: `Bearer ${token}` }
      ];

      let userData = null;
      let success = false;

      for (const auth of authHeaders) {
        try {
          console.log(`Trying ${auth.name} authentication...`);
          const response = await axios.get(userUrl, {
            headers: {
              'Authorization': auth.header
            }
          });

          console.log(`${auth.name} authentication successful!`);
          console.log('User data response:', response.data);
          userData = response.data;
          success = true;
          break;
        } catch (authErr: any) {
          console.log(`${auth.name} authentication failed:`, authErr.message);
          if (authErr.response) {
            console.log('Response status:', authErr.response.status);
            console.log('Response data:', authErr.response.data);
          }
        }
      }

      if (!success) {
        throw new Error('All authentication methods failed');
      }

      setUser(userData);
      setIsAuthenticated(true);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching user data:', err);

      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);
      }

      setIsAuthenticated(false);
      setUser(null);
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      setLoading(false);
    }
  };

  const login = async (usernameOrEmail: string, password: string) => {
    try {
      setLoading(true);
      setError(null);

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      console.log('API Base URL:', baseUrl);

      // Determine if input is email or username
      const isEmail = usernameOrEmail.includes('@');
      console.log('Is email?', isEmail);

      let response: AxiosResponse<AuthResponse> | null = null;
      let loginSuccess = false;
      let loginError: Error | null = null;

      // Try all possible login methods (prioritize working methods)
      const loginMethods = [
        // JWT endpoints - Djoser requires username field
        {
          name: 'JWT with username',
          url: `${baseUrl}/api/auth/djoser/jwt/create/`,
          data: { username: usernameOrEmail, password }
        },
        {
          name: 'JWT with both username and email',
          url: `${baseUrl}/api/auth/djoser/jwt/create/`,
          data: { username: usernameOrEmail, email: usernameOrEmail, password }
        },
        // Custom login endpoints - try these as fallback
        {
          name: 'Custom login with username',
          url: `${baseUrl}/api/auth/login/`,
          data: { username: usernameOrEmail, password }
        },
        {
          name: 'Custom login with email',
          url: `${baseUrl}/api/auth/login/`,
          data: { email: usernameOrEmail, password }
        }
      ];

      // Try each login method until one succeeds
      for (const method of loginMethods) {
        try {
          console.log(`Trying login method: ${method.name}`);
          console.log('Login URL:', method.url);
          console.log('Login data:', { ...method.data, password: '******' });

          const axiosResponse = await axios.post(method.url, method.data);
          console.log(`${method.name} succeeded:`, axiosResponse.data);
          response = axiosResponse;
          loginSuccess = true;
          break;
        } catch (err: any) {
          console.log(`${method.name} failed:`, err.message);
          if (err.response) {
            console.log('Response status:', err.response.status);
            console.log('Response data:', err.response.data);
          }
          loginError = err;
        }
      }

      if (!loginSuccess) {
        throw loginError || new Error('All login methods failed');
      }

      console.log('Login successful! Response:', response?.data);

      if (!response) {
        throw new Error('Login succeeded but response is null');
      }

      const { access, refresh } = response.data;

      localStorage.setItem('access_token', access);
      localStorage.setItem('refresh_token', refresh);

      setIsAuthenticated(true);

      // Fetch user data
      console.log('Fetching user data with token:', access.substring(0, 10) + '...');
      await fetchUserData(access);

      setLoading(false);
    } catch (err: any) {
      console.error('Login error:', err);
      let errorMessage = 'Login failed. Please check your credentials.';

      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);

        if (err.response.data) {
          if (err.response.data.detail) {
            errorMessage = err.response.data.detail;
          } else if (err.response.data.non_field_errors) {
            errorMessage = err.response.data.non_field_errors.join(', ');
          } else {
            // Try to extract error messages from the response data
            const errorMessages = [];
            for (const key in err.response.data) {
              if (Array.isArray(err.response.data[key])) {
                errorMessages.push(`${key}: ${err.response.data[key].join(', ')}`);
              } else {
                errorMessages.push(`${key}: ${err.response.data[key]}`);
              }
            }
            if (errorMessages.length > 0) {
              errorMessage = errorMessages.join('; ');
            }
          }
        }
      }

      setError(errorMessage);
      setIsAuthenticated(false);
      setUser(null);
      setLoading(false);
      throw err;
    }
  };

  const register = async (username: string, email: string, password: string, re_password: string, first_name?: string, last_name?: string) => {
    try {
      setLoading(true);
      setError(null);

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      console.log('API Base URL:', baseUrl);

      // Prepare registration data
      const registrationData = {
        username,
        email,
        password,
        re_password,
        first_name: first_name || '',
        last_name: last_name || ''
      };

      console.log('Registration data:', { ...registrationData, password: '******', re_password: '******' });

      // Use the custom registration endpoint
      const response = await axios.post(`${baseUrl}/api/auth/register/`, registrationData);
      console.log('Registration successful! Response:', response.data);
      setLoading(false);

    } catch (err: any) {
      console.error('Registration error:', err);
      let errorMessage = 'Registration failed. Please try again.';

      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);

        if (err.response.data) {
          if (err.response.data.detail) {
            errorMessage = err.response.data.detail;
          } else if (err.response.data.non_field_errors) {
            errorMessage = err.response.data.non_field_errors.join(', ');
          } else {
            // Try to extract error messages from the response data
            const errorMessages = [];
            for (const key in err.response.data) {
              if (Array.isArray(err.response.data[key])) {
                errorMessages.push(`${key}: ${err.response.data[key].join(', ')}`);
              } else if (typeof err.response.data[key] === 'string') {
                errorMessages.push(`${key}: ${err.response.data[key]}`);
              }
            }
            if (errorMessages.length > 0) {
              errorMessage = errorMessages.join('; ');
            }
          }
        }
      }

      setError(errorMessage);
      setLoading(false);
      throw new Error(errorMessage);
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    setIsAuthenticated(false);
    setUser(null);
  };

  const updateUser = (userData: any) => {
    setUser(userData);
  };

  const value = {
    isAuthenticated,
    user,
    login,
    register,
    logout,
    loading,
    error,
    updateUser
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
