import React, { useState } from 'react';
import axios from 'axios';

const LoginTest: React.FC = () => {
  const [username, setUsername] = useState('testuser');
  const [password, setPassword] = useState('testpassword123');
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [userData, setUserData] = useState<any>(null);

  const handleCustomLogin = async () => {
    setLoading(true);
    setResult('');
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const apiUrl = `${baseUrl}/api`;
      const loginUrl = `${apiUrl}/accounts/login/`;

      console.log('Attempting login with custom endpoint:', loginUrl);

      // Check if input is email or username
      const isEmail = username.includes('@');

      const payload = isEmail
        ? { email: username, password }
        : { username, password };

      console.log('Login payload:', { ...payload, password: '******' });

      const response = await axios.post(loginUrl, payload);

      console.log('Login response:', response.data);
      setResult(JSON.stringify(response.data, null, 2));

      if (response.data.access) {
        setToken(response.data.access);
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setResult(`Error: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDjoserLogin = async () => {
    setLoading(true);
    setResult('');
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const apiUrl = `${baseUrl}/api`;
      const loginUrl = `${apiUrl}/auth/jwt/create/`;

      console.log('Attempting login with Djoser endpoint:', loginUrl);

      // Check if input is email or username
      const isEmail = username.includes('@');

      const payload = isEmail
        ? { email: username, password }
        : { username, password };

      console.log('Login payload:', { ...payload, password: '******' });

      const response = await axios.post(loginUrl, payload);

      console.log('Login response:', response.data);
      setResult(JSON.stringify(response.data, null, 2));

      if (response.data.access) {
        setToken(response.data.access);
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setResult(`Error: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserData = async () => {
    if (!token) {
      setResult('No token available. Please login first.');
      return;
    }

    setLoading(true);
    setResult('');
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const apiUrl = `${baseUrl}/api`;
      const userUrl = `${apiUrl}/auth/users/me/`;

      console.log('Fetching user data from:', userUrl);

      const response = await axios.get(userUrl, {
        headers: {
          'Authorization': `JWT ${token}`
        }
      });

      console.log('User data:', response.data);
      setResult(JSON.stringify(response.data, null, 2));
      setUserData(response.data);
    } catch (error: any) {
      console.error('Error fetching user data:', error);
      setResult(`Error: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Login Test</h1>

      <div className="mb-4">
        <label className="block mb-2">Username:</label>
        <input
          type="text"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          className="border p-2 w-full max-w-md"
        />
      </div>

      <div className="mb-4">
        <label className="block mb-2">Password:</label>
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="border p-2 w-full max-w-md"
        />
      </div>

      <div className="flex space-x-4 mb-4">
        <button
          onClick={handleCustomLogin}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded disabled:bg-blue-300"
        >
          {loading ? 'Loading...' : 'Custom Login'}
        </button>

        <button
          onClick={handleDjoserLogin}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded disabled:bg-green-300"
        >
          {loading ? 'Loading...' : 'Djoser Login'}
        </button>

        <button
          onClick={fetchUserData}
          disabled={loading || !token}
          className="bg-purple-500 text-white px-4 py-2 rounded disabled:bg-purple-300"
        >
          {loading ? 'Loading...' : 'Fetch User Data'}
        </button>
      </div>

      {token && (
        <div className="mb-4 p-4 bg-green-100 border border-green-300 rounded">
          <h2 className="font-bold mb-2">Token:</h2>
          <div className="break-all">{token}</div>
        </div>
      )}

      {userData && (
        <div className="mb-4 p-4 bg-blue-100 border border-blue-300 rounded">
          <h2 className="font-bold mb-2">User Data:</h2>
          <div>
            <p><strong>Username:</strong> {userData.username}</p>
            <p><strong>Email:</strong> {userData.email}</p>
            <p><strong>ID:</strong> {userData.id}</p>
            <p><strong>Is Staff:</strong> {userData.is_staff ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}

      {result && (
        <div className="mt-4">
          <h2 className="font-bold mb-2">Result:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">{result}</pre>
        </div>
      )}

      <div className="mt-8 p-4 bg-yellow-100 border border-yellow-300 rounded">
        <h2 className="font-bold mb-2">Debug Information:</h2>
        <p><strong>API URL:</strong> {process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app'}</p>
        <p><strong>Custom Login URL:</strong> {`${process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app'}/api/accounts/login/`}</p>
        <p><strong>Djoser Login URL:</strong> {`${process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app'}/api/auth/jwt/create/`}</p>
        <p><strong>User URL:</strong> {`${process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app'}/api/auth/users/me/`}</p>
      </div>
    </div>
  );
};

export default LoginTest;
