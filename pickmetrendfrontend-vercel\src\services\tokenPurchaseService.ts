import { api } from './api';

export interface TokenPack {
  id: string;
  name: string;
  tokens: number;
  price_inr: number;
  tokens_per_rupee: number;
  is_popular?: boolean;
  savings_text?: string;
}

export interface TokenPurchase {
  id: string;
  token_pack_name: string;
  tokens_purchased: number;
  amount_paid: number;
  payment_status: string;
  created_at: string;
  completed_at?: string;
}

export interface CreateOrderResponse {
  success: boolean;
  purchase_id?: string;
  razorpay_order?: {
    order_id: string;
    amount: number;
    currency: string;
    key_id: string;
  };
  token_pack?: {
    name: string;
    tokens: number;
    price_inr: number;
  };
  error?: string;
}

export interface VerifyPaymentResponse {
  success: boolean;
  message?: string;
  tokens_purchased?: number;
  amount_paid?: number;
  new_balance?: number;
  purchase_id?: string;
  error?: string;
}

export const tokenPurchaseService = {
  /**
   * Get available token packs
   */
  async getTokenPacks(): Promise<{ success: boolean; token_packs?: TokenPack[]; error?: string }> {
    try {
      const response = await api.get('/api/wallet/token-packs/');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching token packs:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to fetch token packs'
      };
    }
  },

  /**
   * Create a token purchase order
   */
  async createOrder(tokenPackId: string): Promise<CreateOrderResponse> {
    try {
      const response = await api.post('/api/wallet/create-token-order/', {
        token_pack_id: tokenPackId
      });
      return response.data;
    } catch (error: any) {
      console.error('Error creating token purchase order:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to create order'
      };
    }
  },

  /**
   * Verify payment and complete purchase
   */
  async verifyPayment(
    purchaseId: string,
    razorpayPaymentId: string,
    razorpayOrderId: string,
    razorpaySignature: string
  ): Promise<VerifyPaymentResponse> {
    try {
      const response = await api.post('/api/wallet/verify-token-payment/', {
        purchase_id: purchaseId,
        razorpay_payment_id: razorpayPaymentId,
        razorpay_order_id: razorpayOrderId,
        razorpay_signature: razorpaySignature
      });
      return response.data;
    } catch (error: any) {
      console.error('Error verifying payment:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Payment verification failed'
      };
    }
  },

  /**
   * Get user's token purchase history
   */
  async getPurchaseHistory(): Promise<{ success: boolean; purchases?: TokenPurchase[]; error?: string }> {
    try {
      const response = await api.get('/api/wallet/purchase-history/');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching purchase history:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to fetch purchase history'
      };
    }
  }
};
