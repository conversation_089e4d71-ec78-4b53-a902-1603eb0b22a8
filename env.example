# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Settings (for local development)
USE_SQLITE=True
# For PostgreSQL:
# DB_NAME=pickmetrend
# DB_USER=postgres
# DB_PASSWORD=your-password
# DB_HOST=localhost
# DB_PORT=5432

# Cloudinary Settings (for media storage)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
USE_CLOUDINARY=True

# Razorpay Settings (payment gateway)
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret

# SendGrid Settings (for emails)
SENDGRID_API_KEY=your-sendgrid-api-key

# Redis Settings (for gaming features)
# For local development:
REDIS_URL=redis://localhost:6379/0
# For Railway production:
# REDIS_URL=redis://default:<EMAIL>:6379

# Site URL
SITE_URL=http://localhost:8000 