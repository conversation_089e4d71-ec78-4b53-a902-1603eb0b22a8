#!/usr/bin/env python
"""
Production Readiness Verification
=================================

This script verifies that the token purchase feature is ready for production deployment.
"""

import os
import json
from pathlib import Path

def check_frontend_config():
    """Check frontend production configuration"""
    print("🔍 Checking Frontend Configuration")
    print("=" * 50)
    
    frontend_path = Path("pickmetrendfrontend-vercel")
    issues = []
    
    # Check .env.production
    env_prod_path = frontend_path / ".env.production"
    if env_prod_path.exists():
        with open(env_prod_path, 'r') as f:
            env_content = f.read()
        
        required_vars = [
            'REACT_APP_API_URL=https://pickmetrendofficial-render.onrender.com',
            'REACT_APP_RAZORPAY_KEY_ID=rzp_live_1IrGH0WEYdtFdh',
            'REACT_APP_ENVIRONMENT=production',
            'REACT_APP_PAYMENT_TEST_MODE=false'
        ]
        
        print("✅ .env.production exists")
        for var in required_vars:
            if var in env_content:
                print(f"   ✅ {var.split('=')[0]} configured")
            else:
                print(f"   ❌ {var.split('=')[0]} missing")
                issues.append(f"Missing {var.split('=')[0]} in .env.production")
    else:
        print("❌ .env.production not found")
        issues.append(".env.production file missing")
    
    # Check render.yaml
    render_yaml_path = frontend_path / "render.yaml"
    if render_yaml_path.exists():
        with open(render_yaml_path, 'r') as f:
            render_content = f.read()
        
        required_env_vars = [
            'REACT_APP_API_URL',
            'REACT_APP_RAZORPAY_KEY_ID',
            'REACT_APP_ENVIRONMENT',
            'REACT_APP_PAYMENT_TEST_MODE',
            'REACT_APP_ENABLE_CORS'
        ]
        
        print("✅ render.yaml exists")
        for var in required_env_vars:
            if var in render_content:
                print(f"   ✅ {var} in render.yaml")
            else:
                print(f"   ❌ {var} missing from render.yaml")
                issues.append(f"Missing {var} in render.yaml")
    else:
        print("❌ render.yaml not found")
        issues.append("render.yaml file missing")
    
    # Check if TokenPurchasePage exists
    token_page_path = frontend_path / "src" / "pages" / "TokenPurchasePage.tsx"
    if token_page_path.exists():
        print("✅ TokenPurchasePage.tsx exists")
    else:
        print("❌ TokenPurchasePage.tsx not found")
        issues.append("TokenPurchasePage.tsx missing")
    
    # Check if route is added to App.tsx
    app_tsx_path = frontend_path / "src" / "App.tsx"
    if app_tsx_path.exists():
        with open(app_tsx_path, 'r') as f:
            app_content = f.read()
        
        if '/buy-tokens' in app_content and 'TokenPurchasePage' in app_content:
            print("✅ Token purchase route added to App.tsx")
        else:
            print("❌ Token purchase route not found in App.tsx")
            issues.append("Token purchase route missing in App.tsx")
    
    return issues

def check_backend_config():
    """Check backend production configuration"""
    print("\n🔍 Checking Backend Configuration")
    print("=" * 50)
    
    issues = []
    
    # Check if token purchase views exist
    token_views_path = Path("wallet") / "token_purchase_views.py"
    if token_views_path.exists():
        print("✅ token_purchase_views.py exists")
    else:
        print("❌ token_purchase_views.py not found")
        issues.append("token_purchase_views.py missing")
    
    # Check if models are updated
    models_path = Path("wallet") / "models.py"
    if models_path.exists():
        with open(models_path, 'r') as f:
            models_content = f.read()
        
        if 'TokenPack' in models_content and 'TokenPurchase' in models_content:
            print("✅ TokenPack and TokenPurchase models exist")
        else:
            print("❌ Token purchase models not found")
            issues.append("Token purchase models missing")
    
    # Check if URLs are configured
    wallet_urls_path = Path("wallet") / "urls.py"
    if wallet_urls_path.exists():
        with open(wallet_urls_path, 'r') as f:
            urls_content = f.read()
        
        required_urls = [
            'token-packs/',
            'create-token-order/',
            'verify-token-payment/'
        ]
        
        print("✅ wallet/urls.py exists")
        for url in required_urls:
            if url in urls_content:
                print(f"   ✅ {url} endpoint configured")
            else:
                print(f"   ❌ {url} endpoint missing")
                issues.append(f"Missing {url} endpoint")
    
    # Check settings.py for Razorpay config
    settings_path = Path("dropshipping_backend") / "settings.py"
    if settings_path.exists():
        with open(settings_path, 'r') as f:
            settings_content = f.read()
        
        if 'RAZORPAY_KEY_ID' in settings_content and 'rzp_live_1IrGH0WEYdtFdh' in settings_content:
            print("✅ Production Razorpay keys configured")
        else:
            print("❌ Production Razorpay keys not found")
            issues.append("Production Razorpay keys missing")
    
    return issues

def check_documentation():
    """Check if documentation is complete"""
    print("\n📚 Checking Documentation")
    print("=" * 50)
    
    issues = []
    
    docs = [
        "PRODUCTION_DEPLOYMENT_CHECKLIST.md",
        "PRODUCTION_DEPLOYMENT_SUMMARY.md",
        "TOKEN_PURCHASE_SYSTEM.md"
    ]
    
    for doc in docs:
        if Path(doc).exists():
            print(f"✅ {doc} exists")
        else:
            print(f"❌ {doc} missing")
            issues.append(f"Documentation {doc} missing")
    
    return issues

def main():
    """Main verification function"""
    print("🚀 Production Readiness Verification")
    print("=" * 60)
    
    all_issues = []
    
    # Check frontend
    frontend_issues = check_frontend_config()
    all_issues.extend(frontend_issues)
    
    # Check backend
    backend_issues = check_backend_config()
    all_issues.extend(backend_issues)
    
    # Check documentation
    doc_issues = check_documentation()
    all_issues.extend(doc_issues)
    
    # Summary
    print("\n🎯 Verification Summary")
    print("=" * 50)
    
    if not all_issues:
        print("🎉 ALL CHECKS PASSED!")
        print("✅ Frontend configuration ready")
        print("✅ Backend configuration ready")
        print("✅ Documentation complete")
        print("\n🚀 READY FOR PRODUCTION DEPLOYMENT!")
        
        print("\n📋 Next Steps:")
        print("1. Commit and push backend changes to main branch")
        print("2. Commit and push frontend changes to master branch")
        print("3. Monitor deployment in Render dashboard")
        print("4. Test production deployment with checklist")
        
    else:
        print("❌ ISSUES FOUND:")
        for i, issue in enumerate(all_issues, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n🔧 Please fix {len(all_issues)} issue(s) before deployment")
    
    return len(all_issues) == 0

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
