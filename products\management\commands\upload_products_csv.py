import csv
import os
from django.core.management.base import BaseCommand, CommandError
from django.utils.text import slugify
from products.models import Product, ProductImage


class Command(BaseCommand):
    help = 'Import products from a CSV file'

    def add_arguments(self, parser):
        parser.add_argument('csv_file', type=str, help='Path to the CSV file to import')

    def handle(self, *args, **options):
        csv_file = options['csv_file']
        
        if not os.path.exists(csv_file):
            raise CommandError(f'File "{csv_file}" does not exist')
            
        if not csv_file.endswith('.csv'):
            raise CommandError(f'File "{csv_file}" is not a CSV file')
            
        success_count = 0
        error_count = 0
        
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            # Skip header row
            next(reader)
            
            for row_num, row in enumerate(reader, start=2):
                try:
                    if len(row) < 5:
                        self.stdout.write(self.style.ERROR(
                            f"Row {row_num}: Not enough fields, expected 5 fields."
                        ))
                        error_count += 1
                        continue
                        
                    name, description, image_url, price, stock = row[:5]
                    
                    # Validate data
                    if not name:
                        self.stdout.write(self.style.ERROR(
                            f"Row {row_num}: Name is required."
                        ))
                        error_count += 1
                        continue
                        
                    try:
                        price = float(price)
                    except ValueError:
                        self.stdout.write(self.style.ERROR(
                            f"Row {row_num}: Price must be a valid number."
                        ))
                        error_count += 1
                        continue
                        
                    try:
                        stock = int(stock)
                    except ValueError:
                        self.stdout.write(self.style.ERROR(
                            f"Row {row_num}: Stock must be a valid integer."
                        ))
                        error_count += 1
                        continue
                    
                    # Generate a slug from the name
                    slug = slugify(name)
                    
                    # Check if product with this slug already exists
                    product, created = Product.objects.update_or_create(
                        slug=slug,
                        defaults={
                            'name': name,
                            'description': description,
                            'price': price,
                            'stock': stock,
                        }
                    )
                    
                    action_str = 'Created' if created else 'Updated'
                    self.stdout.write(self.style.SUCCESS(
                        f"{action_str} product: {name}"
                    ))
                    
                    # If image URL is provided, create a product image
                    if image_url:
                        # For simplicity, we're not downloading the image here,
                        # just storing the URL as is. In a real app, you would
                        # download and save the image file.
                        ProductImage.objects.create(
                            product=product,
                            image=image_url,
                            is_primary=True
                        )
                        self.stdout.write(self.style.SUCCESS(
                            f"Added image for product: {name}"
                        ))
                    
                    success_count += 1
                    
                except Exception as e:
                    self.stdout.write(self.style.ERROR(
                        f"Row {row_num}: {str(e)}"
                    ))
                    error_count += 1
        
        self.stdout.write("=" * 30)
        self.stdout.write(self.style.SUCCESS(
            f"Import completed! Processed {success_count + error_count} rows with "
            f"{success_count} successes and {error_count} errors."
        )) 