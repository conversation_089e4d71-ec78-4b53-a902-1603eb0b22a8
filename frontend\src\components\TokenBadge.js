import React from 'react';
import './TokenBadge.css';

const TokenBadge = ({ 
    product, 
    size = 'medium', 
    showPercentage = true, 
    showIcon = true 
}) => {
    if (!product?.token_discount_available) {
        return null;
    }

    const { token_discount_info } = product;
    const percentage = token_discount_info?.percentage || 0;

    const sizeClasses = {
        small: 'token-badge--small',
        medium: 'token-badge--medium',
        large: 'token-badge--large'
    };

    return (
        <div className={`token-badge ${sizeClasses[size]}`}>
            {showIcon && (
                <span className="token-badge__icon">🪙</span>
            )}
            <span className="token-badge__text">
                Token Discount
                {showPercentage && percentage > 0 && (
                    <span className="token-badge__percentage">
                        {percentage}%
                    </span>
                )}
            </span>
        </div>
    );
};

export default TokenBadge;
