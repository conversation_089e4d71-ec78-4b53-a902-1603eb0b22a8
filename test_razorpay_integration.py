#!/usr/bin/env python
"""
Test Razorpay Integration
========================

This script tests the complete token purchase flow including:
1. Token packs endpoint
2. Order creation endpoint  
3. Payment verification endpoint
"""

import requests
import json
import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'local_test_settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def get_auth_token():
    """Get authentication token for test user"""
    try:
        user = User.objects.get(username='testuser')
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    except User.DoesNotExist:
        print("❌ Test user not found. Please run create_test_superuser.py first")
        return None

def test_razorpay_integration():
    """Test the complete Razorpay integration flow"""
    
    print("🧪 Testing Razorpay Integration")
    print("=" * 50)
    
    backend_url = "http://127.0.0.1:8000"
    
    # Get authentication token
    print("\n1️⃣ Getting authentication token...")
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        'Authorization': f'JWT {token}',
        'Content-Type': 'application/json'
    }
    print("✅ Authentication token obtained")
    
    try:
        # Test 1: Get token packs
        print("\n2️⃣ Testing token packs endpoint...")
        packs_url = f"{backend_url}/api/wallet/token-packs/"
        response = requests.get(packs_url, headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            packs_data = response.json()
            print("✅ Token packs retrieved successfully")
            print(f"   Found {len(packs_data.get('token_packs', []))} token packs")
            
            if packs_data.get('token_packs'):
                first_pack = packs_data['token_packs'][0]
                print(f"   First pack: {first_pack['name']} - {first_pack['tokens']} tokens for ₹{first_pack['price_inr']}")
                pack_id = first_pack['id']
            else:
                print("❌ No token packs found")
                return False
        else:
            print(f"❌ Failed to get token packs: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
        
        # Test 2: Create order
        print("\n3️⃣ Testing order creation...")
        order_url = f"{backend_url}/api/wallet/create-token-order/"
        order_data = {'token_pack_id': pack_id}
        
        response = requests.post(order_url, headers=headers, json=order_data, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            order_response = response.json()
            print("✅ Order created successfully")
            print(f"   Purchase ID: {order_response.get('purchase_id')}")
            print(f"   Razorpay Order ID: {order_response.get('razorpay_order', {}).get('order_id')}")
            print(f"   Amount: ₹{order_response.get('razorpay_order', {}).get('amount', 0) / 100}")
            
            purchase_id = order_response.get('purchase_id')
            razorpay_order_id = order_response.get('razorpay_order', {}).get('order_id')
            
        else:
            print(f"❌ Failed to create order: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
        
        # Test 3: Test payment verification endpoint (with mock data)
        print("\n4️⃣ Testing payment verification endpoint...")
        verify_url = f"{backend_url}/api/wallet/verify-token-payment/"
        verify_data = {
            'purchase_id': purchase_id,
            'razorpay_payment_id': 'pay_test_123456789',
            'razorpay_order_id': razorpay_order_id,
            'razorpay_signature': 'test_signature_123'
        }
        
        response = requests.post(verify_url, headers=headers, json=verify_data, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            verify_response = response.json()
            if verify_response.get('success'):
                print("✅ Payment verification endpoint working")
                print(f"   Tokens would be added: {verify_response.get('tokens_purchased', 0)}")
            else:
                print("⚠️  Payment verification failed (expected with test data)")
                print(f"   Error: {verify_response.get('error')}")
        else:
            print(f"❌ Payment verification endpoint error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
        
        # Test 4: Check Razorpay configuration
        print("\n5️⃣ Checking Razorpay configuration...")
        from django.conf import settings
        
        razorpay_key = getattr(settings, 'RAZORPAY_KEY_ID', None)
        razorpay_secret = getattr(settings, 'RAZORPAY_KEY_SECRET', None)
        
        if razorpay_key and razorpay_secret:
            print("✅ Razorpay credentials configured")
            print(f"   Key ID: {razorpay_key[:10]}...")
            print(f"   Secret: {'*' * len(razorpay_secret)}")
        else:
            print("❌ Razorpay credentials not configured")
            print("   Please check RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET in settings")
        
        print("\n🎯 Integration Test Summary:")
        print("✅ Authentication: Working")
        print("✅ Token Packs API: Working") 
        print("✅ Order Creation API: Working")
        print("✅ Payment Verification API: Available")
        print("✅ Razorpay Configuration: Set up")
        
        print("\n💡 Next Steps:")
        print("1. Frontend should now be able to create orders")
        print("2. Razorpay payment dialog should open")
        print("3. Test with card: 4111 1111 1111 1111")
        print("4. Payment verification should complete the purchase")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Backend server is not running")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

if __name__ == '__main__':
    test_razorpay_integration()
