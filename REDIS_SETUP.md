# Redis Configuration for PickMeTrend

This document explains the Redis configuration for the PickMeTrend project, which is used for gaming features, WebSocket connections, and task queuing.

## Current Configuration

The project is configured to use Redis for:
- **Django Channels** (WebSocket gaming)
- **Celery** (Task queue)
- **Gaming features** (Real-time multiplayer games)

## Redis URLs

### Development
- **Local Redis**: `redis://localhost:6379/0`
- **Docker Redis**: `redis://redis:6379/0`

### Production
- **Railway Redis**: `redis://default:<EMAIL>:6379`
- **Upstash Redis**: `rediss://...` (with TLS)

## Configuration Details

### Environment Variables
The Redis URL is configured via the `REDIS_URL` environment variable:

```bash
# Development
REDIS_URL=redis://localhost:6379/0

# Production (Railway)
REDIS_URL=redis://default:<EMAIL>:6379
```

### Automatic Configuration
The system automatically:
1. Uses localhost Redis for development (`DEBUG=True`)
2. Uses Railway Redis for production (`DEBUG=False`)
3. Allows environment variable override
4. Handles different Redis providers (Railway, Upstash, local)

## Testing Redis Connection

Run the test script to verify Redis connectivity:

```bash
python test_redis_connection.py
```

## Features Using Redis

### 1. Django Channels (WebSocket Gaming)
- Real-time multiplayer games
- Live chat functionality
- Game state synchronization

### 2. Celery (Task Queue)
- Background email sending
- Order processing
- Data synchronization tasks

### 3. Gaming System
- Game session management
- Player matchmaking
- Score tracking

## Troubleshooting

### Connection Issues
1. Check if Redis service is running
2. Verify the Redis URL format
3. Ensure network connectivity (for Railway/Upstash)
4. Check authentication credentials

### Performance Issues
1. Monitor Redis memory usage
2. Check connection pool settings
3. Review query patterns

## Railway Deployment

For Railway deployment, the Redis URL is automatically configured:
- Uses Railway's internal Redis service
- No TLS required (internal network)
- Automatic failover and scaling

## Security Notes

- Railway Redis uses internal network (secure)
- Upstash Redis uses TLS encryption
- Local Redis should only be used in development
- Never commit Redis passwords to version control 