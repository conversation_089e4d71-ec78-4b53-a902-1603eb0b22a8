from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    ReturnRequestViewSet,
    UserReturnRequestsView,
    OrderReturnEligibilityView
)

router = DefaultRouter()
router.register(r'requests', ReturnRequestViewSet, basename='returnrequest')

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
    
    # Additional endpoints
    path('api/my-returns/', UserReturnRequestsView.as_view(), name='user-return-requests'),
    path('api/orders/<uuid:order_id>/return-eligibility/', OrderReturnEligibilityView.as_view(), name='order-return-eligibility'),
]
