from django.shortcuts import render
from rest_framework import viewsets, permissions, status, generics
from rest_framework.response import Response
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from .models import ReturnRequest
from .serializers import (
    ReturnRequestCreateSerializer,
    ReturnRequestSerializer,
    ReturnRequestUpdateSerializer,
    ReturnRequestListSerializer
)
from orders.models import Order


class IsAdminOrOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of a return request or admins to view/edit it.
    """
    def has_object_permission(self, request, view, obj):
        # Admin users can access everything
        if request.user.is_staff:
            return True
        # Users can only access their own return requests
        return obj.user == request.user


class ReturnRequestViewSet(viewsets.ModelViewSet):
    """
    ViewSet for return request operations
    """
    permission_classes = [permissions.IsAuthenticated, IsAdminO<PERSON>Owner]

    def get_queryset(self):
        """Return return requests for the current user or all for admins"""
        user = self.request.user
        if user.is_staff:
            return ReturnRequest.objects.all()
        return ReturnRequest.objects.filter(user=user)

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return ReturnRequestCreateSerializer
        elif self.action == 'list':
            return ReturnRequestListSerializer
        elif self.action in ['update', 'partial_update'] and self.request.user.is_staff:
            return ReturnRequestUpdateSerializer
        return ReturnRequestSerializer

    def create(self, request, *args, **kwargs):
        """Create a new return request"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return_request = serializer.save()

        # Send email notification to admin
        self.send_return_request_notification(return_request)

        # Return the created return request with full details
        response_serializer = ReturnRequestSerializer(return_request, context={'request': request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        """Update return request (admin only)"""
        if not request.user.is_staff:
            return Response(
                {'detail': 'Only admin users can update return requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        old_status = instance.status

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        return_request = serializer.save()

        # Send email notification if status changed
        if old_status != return_request.status:
            self.send_status_update_notification(return_request)

        response_serializer = ReturnRequestSerializer(return_request, context={'request': request})
        return Response(response_serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def approve(self, request, pk=None):
        """Approve a return request"""
        return_request = self.get_object()
        admin_response = request.data.get('admin_response', '')
        refund_amount = request.data.get('refund_amount')

        return_request.approve(admin_response=admin_response, refund_amount=refund_amount)
        self.send_status_update_notification(return_request)

        serializer = ReturnRequestSerializer(return_request, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def reject(self, request, pk=None):
        """Reject a return request"""
        return_request = self.get_object()
        admin_response = request.data.get('admin_response', '')

        return_request.reject(admin_response=admin_response)
        self.send_status_update_notification(return_request)

        serializer = ReturnRequestSerializer(return_request, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def complete(self, request, pk=None):
        """Mark return request as completed"""
        return_request = self.get_object()
        tracking_number = request.data.get('tracking_number', '')

        return_request.complete(tracking_number=tracking_number)
        self.send_status_update_notification(return_request)

        serializer = ReturnRequestSerializer(return_request, context={'request': request})
        return Response(serializer.data)

    def send_return_request_notification(self, return_request):
        """Send email notification to admin when new return request is created"""
        try:
            subject = f'New Return Request - Order #{return_request.order.id}'

            # Create email content
            context = {
                'return_request': return_request,
                'order': return_request.order,
                'user': return_request.user,
                'site_url': getattr(settings, 'SITE_URL', 'https://www.pickmetrend.com')
            }

            message = f"""
New Return Request Submitted

Order ID: {return_request.order.id}
Customer: {return_request.user.get_full_name() or return_request.user.username}
Email: {return_request.user.email}
Reason: {return_request.get_reason_display()}
Details: {return_request.reason_detail}

Please review and respond to this return request in the admin panel.
            """

            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[settings.ADMIN_EMAIL],
                fail_silently=True
            )
        except Exception as e:
            print(f"Failed to send return request notification: {e}")

    def send_status_update_notification(self, return_request):
        """Send email notification to customer when return status is updated"""
        try:
            subject = f'Return Request Update - Order #{return_request.order.id}'

            # Create email content based on status
            if return_request.status == 'approved':
                message = f"""
Your return request has been approved!

Order ID: {return_request.order.id}
Status: {return_request.get_status_display()}
"""
                if return_request.refund_amount:
                    message += f"Refund Amount: ₹{return_request.refund_amount}\n"

                if return_request.admin_response:
                    message += f"\nMessage from our team:\n{return_request.admin_response}\n"

                message += "\nWe will process your return shortly. Thank you for your patience."

            elif return_request.status == 'rejected':
                message = f"""
Your return request has been reviewed.

Order ID: {return_request.order.id}
Status: {return_request.get_status_display()}
"""
                if return_request.admin_response:
                    message += f"\nMessage from our team:\n{return_request.admin_response}\n"

                message += "\nIf you have any questions, please contact our support team."

            elif return_request.status == 'completed':
                message = f"""
Your return has been completed!

Order ID: {return_request.order.id}
Status: {return_request.get_status_display()}
"""
                if return_request.tracking_number:
                    message += f"Tracking Number: {return_request.tracking_number}\n"

                if return_request.refund_amount:
                    message += f"Refund Amount: ₹{return_request.refund_amount}\n"

                message += "\nThank you for shopping with PickMeTrend!"

            else:
                message = f"""
Your return request status has been updated.

Order ID: {return_request.order.id}
Status: {return_request.get_status_display()}
"""
                if return_request.admin_response:
                    message += f"\nMessage from our team:\n{return_request.admin_response}\n"

            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[return_request.user.email],
                fail_silently=True
            )
        except Exception as e:
            print(f"Failed to send status update notification: {e}")


class UserReturnRequestsView(generics.ListAPIView):
    """
    View to get return requests for the authenticated user
    """
    serializer_class = ReturnRequestListSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ReturnRequest.objects.filter(user=self.request.user)


class OrderReturnEligibilityView(generics.RetrieveAPIView):
    """
    Check if an order is eligible for return
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, order_id):
        try:
            order = Order.objects.get(id=order_id, user=request.user)
        except Order.DoesNotExist:
            return Response(
                {'detail': 'Order not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check eligibility
        can_return = order.status == 'delivered'
        has_existing_request = ReturnRequest.objects.filter(order=order, user=request.user).exists()

        return Response({
            'order_id': order.id,
            'can_return': can_return,
            'has_existing_request': has_existing_request,
            'order_status': order.status,
            'reason': self._get_eligibility_reason(order, has_existing_request)
        })

    def _get_eligibility_reason(self, order, has_existing_request):
        """Get reason why order can or cannot be returned"""
        if has_existing_request:
            return "A return request already exists for this order"
        elif order.status != 'delivered':
            return f"Order must be delivered to request return (current status: {order.get_status_display()})"
        else:
            return "Order is eligible for return"
