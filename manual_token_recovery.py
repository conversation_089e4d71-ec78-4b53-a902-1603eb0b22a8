#!/usr/bin/env python
"""
Manual Token Recovery Script
===========================

This script helps recover tokens for failed payment verifications where
the payment was successful but tokens weren't credited due to verification issues.
"""

import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from wallet.models import TokenPurchase, Wallet
from django.db import transaction

User = get_user_model()

def find_failed_payments():
    """Find payments that were successful but marked as failed"""
    
    print("🔍 Finding Failed Payment Verifications")
    print("=" * 60)
    
    # Find purchases with payment_status = 'failed' but have razorpay_payment_id
    failed_purchases = TokenPurchase.objects.filter(
        payment_status='failed'
    ).exclude(
        razorpay_payment_id__isnull=True
    ).exclude(
        razorpay_payment_id=''
    )
    
    print(f"Found {failed_purchases.count()} potentially recoverable payments:")
    
    for purchase in failed_purchases:
        print(f"\n📋 Purchase ID: {purchase.id}")
        print(f"   User: {purchase.user.username}")
        print(f"   Token Pack: {purchase.token_pack.name}")
        print(f"   Tokens: {purchase.tokens_purchased}")
        print(f"   Amount: ₹{purchase.amount_paid}")
        print(f"   Payment ID: {purchase.razorpay_payment_id}")
        print(f"   Created: {purchase.created_at}")
        print(f"   Status: {purchase.payment_status}")
        print(f"   Notes: {purchase.notes}")
    
    return failed_purchases

def recover_tokens_for_purchase(purchase_id, confirm=False):
    """Manually credit tokens for a specific purchase"""
    
    try:
        purchase = TokenPurchase.objects.get(id=purchase_id)
        
        print(f"\n🔧 Recovering Tokens for Purchase: {purchase_id}")
        print("=" * 60)
        print(f"User: {purchase.user.username}")
        print(f"Token Pack: {purchase.token_pack.name}")
        print(f"Tokens to Credit: {purchase.tokens_purchased}")
        print(f"Amount Paid: ₹{purchase.amount_paid}")
        print(f"Payment ID: {purchase.razorpay_payment_id}")
        
        if not confirm:
            print("\n⚠️  This is a DRY RUN. Set confirm=True to actually credit tokens.")
            return False
        
        # Perform the recovery
        with transaction.atomic():
            # Get or create wallet
            wallet, created = Wallet.objects.get_or_create(user=purchase.user)
            
            # Credit the tokens
            old_balance = wallet.balance
            wallet.balance += purchase.tokens_purchased
            wallet.save()
            
            # Update purchase status
            purchase.payment_status = 'completed'
            purchase.notes = f'Manually recovered - Payment verification fixed. Original notes: {purchase.notes}'
            purchase.completed_at = django.utils.timezone.now()
            purchase.save()
            
            print(f"\n✅ TOKENS RECOVERED SUCCESSFULLY!")
            print(f"   Old Balance: {old_balance} tokens")
            print(f"   New Balance: {wallet.balance} tokens")
            print(f"   Tokens Added: {purchase.tokens_purchased}")
            print(f"   Purchase Status: {purchase.payment_status}")
            
            return True
            
    except TokenPurchase.DoesNotExist:
        print(f"❌ Purchase {purchase_id} not found")
        return False
    except Exception as e:
        print(f"❌ Error recovering tokens: {str(e)}")
        return False

def recover_all_failed_payments(confirm=False):
    """Recover tokens for all failed payments that have payment IDs"""
    
    failed_purchases = find_failed_payments()
    
    if not failed_purchases:
        print("\n✅ No failed payments found that need recovery")
        return
    
    print(f"\n🔧 Recovering {failed_purchases.count()} Failed Payments")
    print("=" * 60)
    
    if not confirm:
        print("⚠️  DRY RUN MODE - Set confirm=True to actually recover tokens")
    
    recovered_count = 0
    total_tokens = 0
    total_amount = 0
    
    for purchase in failed_purchases:
        success = recover_tokens_for_purchase(purchase.id, confirm=confirm)
        if success:
            recovered_count += 1
            total_tokens += purchase.tokens_purchased
            total_amount += float(purchase.amount_paid)
    
    print(f"\n🎯 Recovery Summary:")
    print(f"   Purchases Processed: {failed_purchases.count()}")
    print(f"   Successfully Recovered: {recovered_count}")
    print(f"   Total Tokens Credited: {total_tokens}")
    print(f"   Total Amount Recovered: ₹{total_amount}")

def check_user_payments(username):
    """Check all payments for a specific user"""
    
    try:
        user = User.objects.get(username=username)
        purchases = TokenPurchase.objects.filter(user=user).order_by('-created_at')
        
        print(f"\n👤 Payment History for User: {username}")
        print("=" * 60)
        
        for purchase in purchases:
            print(f"\n📋 Purchase: {purchase.id}")
            print(f"   Token Pack: {purchase.token_pack.name}")
            print(f"   Tokens: {purchase.tokens_purchased}")
            print(f"   Amount: ₹{purchase.amount_paid}")
            print(f"   Status: {purchase.payment_status}")
            print(f"   Payment ID: {purchase.razorpay_payment_id or 'None'}")
            print(f"   Created: {purchase.created_at}")
            print(f"   Completed: {purchase.completed_at or 'Not completed'}")
            if purchase.notes:
                print(f"   Notes: {purchase.notes}")
        
        # Show current wallet balance
        try:
            wallet = Wallet.objects.get(user=user)
            print(f"\n💰 Current Wallet Balance: {wallet.balance} tokens")
        except Wallet.DoesNotExist:
            print(f"\n💰 No wallet found for user")
            
    except User.DoesNotExist:
        print(f"❌ User '{username}' not found")

if __name__ == '__main__':
    print("🚨 Manual Token Recovery Tool")
    print("=" * 70)
    
    # First, show all failed payments
    failed_purchases = find_failed_payments()
    
    if failed_purchases:
        print(f"\n🔧 To recover tokens for all failed payments, run:")
        print(f"   python manual_token_recovery.py")
        print(f"   # Then in Python shell:")
        print(f"   from manual_token_recovery import recover_all_failed_payments")
        print(f"   recover_all_failed_payments(confirm=True)")
        
        print(f"\n🔧 To recover tokens for a specific purchase:")
        print(f"   from manual_token_recovery import recover_tokens_for_purchase")
        print(f"   recover_tokens_for_purchase('purchase_id_here', confirm=True)")
        
        print(f"\n🔧 To check a specific user's payments:")
        print(f"   from manual_token_recovery import check_user_payments")
        print(f"   check_user_payments('username_here')")
    
    print(f"\n⚠️  IMPORTANT: The payment verification bug has been fixed.")
    print(f"   Future payments should work correctly after deploying the fix.")
