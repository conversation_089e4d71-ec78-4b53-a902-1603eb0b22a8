from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .views_printify import handle_payment_success

# Create a single router for all viewsets
router = DefaultRouter()
router.register('cart', views.CartViewSet, basename='cart')
router.register('', views.OrderViewSet, basename='order')

# Add a direct path for create_from_cart
urlpatterns = [
    # Direct cart operation endpoints - these need to come BEFORE the router URLs
    path('cart/add/', views.add_to_cart, name='add_to_cart'),
    path('cart/remove/<int:item_id>/', views.remove_from_cart, name='remove_from_cart'),
    path('cart/update/<int:item_id>/', views.update_cart_item, name='update_cart_item'),
    path('cart/clear/', views.clear_cart, name='clear_cart'),
    path('cart/<uuid:cart_id>/token-discount-info/', views.cart_token_discount_info, name='cart_token_discount_info'),

    # Include router URLs
    path('', include(router.urls)),

    # Add direct path for create_from_cart to avoid routing issues
    path('create_from_cart/', views.OrderViewSet.as_view({'post': 'create_from_cart'}), name='create_from_cart'),

    # Razorpay test endpoints
    path('razorpay_test/', views.razorpay_test_view, name='razorpay_test'),
    path('create_test_order/', views.create_test_order, name='create_test_order'),
    path('verify_test_payment/', views.verify_test_payment, name='verify_test_payment'),

    # Debug endpoints
    path('debug_create_order/', views.debug_create_order, name='debug_create_order'),
    path('simple_test_order/', views.simple_test_order, name='simple_test_order'),

    # Printify integration
    path('payment_success/', handle_payment_success, name='payment_success'),
]