from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings

class Command(BaseCommand):
    help = 'Send a test email'

    def add_arguments(self, parser):
        parser.add_argument('--to', type=str, help='Email recipient')

    def handle(self, *args, **options):
        recipient = options.get('to', '<EMAIL>')

        self.stdout.write(self.style.SUCCESS(f'Sending test email to {recipient}'))

        try:
            send_mail(
                subject='Test Email from PickMeTrend',
                message='This is a test email from the PickMeTrend customer communication system.',
                from_email=settings.SUPPORT_EMAIL,
                recipient_list=[recipient],
                fail_silently=False,
            )
            self.stdout.write(self.style.SUCCESS('Test email sent successfully'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error sending test email: {str(e)}'))
