{% extends "base.html" %}
{% load static %}

{% block title %}Order Tracking - {{ order.id }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold mb-2">Order Tracking</h1>
        <p class="text-gray-600">Order #{{ order.id }}</p>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Order Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="mb-2"><span class="font-medium">Order Date:</span> {{ order.created_at|date:"F j, Y" }}</p>
                <p class="mb-2"><span class="font-medium">Status:</span> {{ order.get_status_display }}</p>
                <p class="mb-2"><span class="font-medium">Payment Status:</span> {{ order.get_payment_status_display }}</p>
                <p class="mb-2"><span class="font-medium">Total:</span> ₹{{ order.total }}</p>
            </div>
            <div>
                <p class="mb-2"><span class="font-medium">Shipping Address:</span></p>
                <p class="mb-1">{{ order.full_name }}</p>
                <p class="mb-1">{{ order.address }}</p>
                <p class="mb-1">{{ order.city }}, {{ order.state }} {{ order.zipcode }}</p>
                <p class="mb-1">{{ order.country }}</p>
            </div>
        </div>
    </div>

    {% if tracking_info %}
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold">Tracking Information</h2>
                <form method="post" action="{% url 'order-tracking-api:order-tracking-refresh' %}">
                    {% csrf_token %}
                    <input type="hidden" name="order_id" value="{{ order.id }}">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Refresh Tracking
                    </button>
                </form>
            </div>
            
            {% for tracking in tracking_info %}
                <div class="border-b pb-4 mb-4 {% if not forloop.last %}border-gray-200{% endif %}">
                    {% if tracking.printify_order_id %}
                        <p class="text-sm text-gray-500 mb-2">Printify Order ID: {{ tracking.printify_order_id }}</p>
                    {% endif %}
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <p class="mb-2">
                                <span class="font-medium">Status:</span>
                                <span class="
                                    {% if tracking.status == 'pending' %}text-yellow-600
                                    {% elif tracking.status == 'processing' %}text-blue-600
                                    {% elif tracking.status == 'shipped' %}text-green-600
                                    {% elif tracking.status == 'delivered' %}text-green-800
                                    {% elif tracking.status == 'cancelled' %}text-red-600
                                    {% endif %} font-medium
                                ">
                                    {{ tracking.get_status_display }}
                                </span>
                            </p>
                            
                            {% if tracking.tracking_number %}
                                <p class="mb-2">
                                    <span class="font-medium">Tracking Number:</span>
                                    {% if tracking.carrier_link %}
                                        <a href="{{ tracking.carrier_link }}" target="_blank" class="text-blue-600 hover:underline">
                                            {{ tracking.tracking_number }}
                                        </a>
                                    {% else %}
                                        {{ tracking.tracking_number }}
                                    {% endif %}
                                </p>
                            {% endif %}
                            
                            {% if tracking.carrier %}
                                <p class="mb-2"><span class="font-medium">Carrier:</span> {{ tracking.carrier }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            {% if tracking.estimated_delivery %}
                                <p class="mb-2">
                                    <span class="font-medium">Estimated Delivery:</span>
                                    {{ tracking.estimated_delivery|date:"F j, Y" }}
                                    
                                    {% if tracking.days_until_delivery %}
                                        <span class="text-sm text-gray-500">({{ tracking.days_until_delivery }} days remaining)</span>
                                    {% endif %}
                                </p>
                            {% endif %}
                            
                            {% if tracking.days_since_shipped and tracking.status == 'shipped' %}
                                <p class="mb-2">
                                    <span class="font-medium">Shipped:</span>
                                    <span class="text-sm text-gray-500">{{ tracking.days_since_shipped }} days ago</span>
                                </p>
                            {% endif %}
                            
                            <p class="mb-2">
                                <span class="font-medium">Last Updated:</span>
                                {{ tracking.last_update|date:"F j, Y H:i" }}
                            </p>
                        </div>
                    </div>
                    
                    <!-- Shipping Progress Bar -->
                    <div class="relative pt-1">
                        <div class="flex mb-2 items-center justify-between">
                            <div class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full 
                                {% if tracking.status == 'pending' %}bg-yellow-200 text-yellow-800
                                {% elif tracking.status == 'processing' %}bg-blue-200 text-blue-800
                                {% elif tracking.status == 'shipped' %}bg-green-200 text-green-800
                                {% elif tracking.status == 'delivered' %}bg-green-200 text-green-800
                                {% elif tracking.status == 'cancelled' %}bg-red-200 text-red-800
                                {% endif %}">
                                {{ tracking.get_status_display }}
                            </div>
                        </div>
                        <div class="flex">
                            <div class="w-1/4 text-center">
                                <div class="bg-gray-300 rounded-full h-2 mb-1 
                                    {% if tracking.status != 'pending' %}bg-green-500{% endif %}"></div>
                                <span class="text-xs font-semibold inline-block text-gray-600">Order Placed</span>
                            </div>
                            <div class="w-1/4 text-center">
                                <div class="bg-gray-300 rounded-full h-2 mb-1 
                                    {% if tracking.status != 'pending' %}bg-green-500{% endif %}"></div>
                                <span class="text-xs font-semibold inline-block text-gray-600">Processing</span>
                            </div>
                            <div class="w-1/4 text-center">
                                <div class="bg-gray-300 rounded-full h-2 mb-1 
                                    {% if tracking.status == 'shipped' or tracking.status == 'delivered' %}bg-green-500{% endif %}"></div>
                                <span class="text-xs font-semibold inline-block text-gray-600">Shipped</span>
                            </div>
                            <div class="w-1/4 text-center">
                                <div class="bg-gray-300 rounded-full h-2 mb-1 
                                    {% if tracking.status == 'delivered' %}bg-green-500{% endif %}"></div>
                                <span class="text-xs font-semibold inline-block text-gray-600">Delivered</span>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900">No tracking information available</h3>
                <p class="mt-1 text-sm text-gray-500">
                    Tracking information will be available once your order has been processed and shipped.
                </p>
                <div class="mt-6">
                    <form method="post" action="{% url 'order-tracking-api:order-tracking-refresh' %}">
                        {% csrf_token %}
                        <input type="hidden" name="order_id" value="{{ order.id }}">
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            Check for Updates
                        </button>
                    </form>
                </div>
            </div>
        </div>
    {% endif %}

    <div class="mt-6">
        <a href="{% url 'orders:order-detail' order.id %}" class="text-blue-600 hover:underline">
            &larr; Back to Order Details
        </a>
    </div>
</div>
{% endblock %}
