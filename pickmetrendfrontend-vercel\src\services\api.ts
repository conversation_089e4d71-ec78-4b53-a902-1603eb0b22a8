import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `JWT ${token}`;
  }
  return config;
});

// Handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/api/auth/djoser/jwt/refresh/`, {
            refresh: refreshToken,
          });
          
          const { access } = response.data;
          localStorage.setItem('access_token', access);
          
          return api(originalRequest);
        } catch (refreshError) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
        }
      }
    }
    
    return Promise.reject(error);
  }
);

// Return Request Types
export interface ReturnRequest {
  id: string;
  order: any;
  user_email: string;
  user_name: string;
  reason: string;
  reason_display: string;
  reason_detail: string;
  status: string;
  status_display: string;
  admin_response?: string;
  refund_amount?: number;
  tracking_number?: string;
  created_at: string;
  updated_at: string;
  processed_at?: string;
  can_be_returned: boolean;
}

export interface CreateReturnRequest {
  order_id: string;
  reason: string;
  reason_detail: string;
}

export interface ReturnEligibility {
  order_id: string;
  can_return: boolean;
  has_existing_request: boolean;
  order_status: string;
  reason: string;
}

// Return Request API
export const returnRequestAPI = {
  // Get all return requests for the user
  getMyReturns: () => api.get<ReturnRequest[]>('/api/returns/api/my-returns/'),
  
  // Get specific return request
  getReturnRequest: (id: string) => api.get<ReturnRequest>(`/api/returns/api/requests/${id}/`),
  
  // Create new return request
  createReturnRequest: (data: CreateReturnRequest) => api.post<ReturnRequest>('/api/returns/api/requests/', data),
  
  // Check if order is eligible for return
  checkReturnEligibility: (orderId: string) => api.get<ReturnEligibility>(`/api/returns/api/orders/${orderId}/return-eligibility/`),
  
  // Get return reasons (static data)
  getReturnReasons: () => [
    { value: 'defective', label: 'Defective Product' },
    { value: 'wrong_item', label: 'Wrong Item Received' },
    { value: 'not_as_described', label: 'Not as Described' },
    { value: 'damaged_shipping', label: 'Damaged During Shipping' },
    { value: 'size_issue', label: 'Size Issue' },
    { value: 'quality_issue', label: 'Quality Issue' },
    { value: 'changed_mind', label: 'Changed Mind' },
    { value: 'other', label: 'Other' },
  ],
};

export { api };
export default api;
