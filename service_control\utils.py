"""
Utility functions for service control.

These functions provide a convenient way to check if services are enabled
from anywhere in the codebase.
"""
import logging
from .models import ServiceControl

logger = logging.getLogger(__name__)


def is_celery_enabled():
    """
    Check if Celery is enabled.
    
    Returns:
        bool: True if Celery is enabled, False otherwise
    """
    try:
        service_control = ServiceControl.get_instance()
        return service_control.celery_enabled
    except Exception as e:
        logger.error(f"Error checking if Celery is enabled: {str(e)}")
        # Default to enabled if there's an error
        return True


def is_redis_enabled():
    """
    Check if Redis is enabled.
    
    Returns:
        bool: True if Redis is enabled, False otherwise
    """
    try:
        service_control = ServiceControl.get_instance()
        return service_control.redis_enabled
    except Exception as e:
        logger.error(f"Error checking if Redis is enabled: {str(e)}")
        # Default to enabled if there's an error
        return True


def safe_task(task_func):
    """
    Decorator to only execute a Celery task if Celery is enabled.
    
    Args:
        task_func: The Celery task function to decorate
        
    Returns:
        function: The decorated function
    """
    def wrapper(*args, **kwargs):
        if is_celery_enabled():
            return task_func(*args, **kwargs)
        else:
            logger.info(f"Celery task {task_func.__name__} skipped because Celery is disabled")
            # Execute the task synchronously if possible
            try:
                # Get the underlying function (not the task)
                # This works for tasks defined with the @shared_task decorator
                if hasattr(task_func, 'run'):
                    return task_func.run(*args, **kwargs)
                # For tasks defined with the @app.task decorator
                elif hasattr(task_func, '__wrapped__'):
                    return task_func.__wrapped__(*args, **kwargs)
                else:
                    logger.warning(f"Could not execute task {task_func.__name__} synchronously")
                    return None
            except Exception as e:
                logger.error(f"Error executing task {task_func.__name__} synchronously: {str(e)}")
                return None
    
    return wrapper
