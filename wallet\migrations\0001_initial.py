# Generated by Django 5.0.2 on 2025-06-03 07:03

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Wallet',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('balance', models.PositiveIntegerField(default=0, help_text='Token balance')),
                ('total_earned', models.PositiveIntegerField(default=0, help_text='Total tokens earned')),
                ('total_spent', models.PositiveIntegerField(default=0, help_text='Total tokens spent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='wallet', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='WalletTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('transaction_type', models.CharField(choices=[('game_win', 'Game Win'), ('game_draw', 'Game Draw'), ('game_participation', 'Game Participation'), ('referral_bonus', 'Referral Bonus'), ('spin_wheel', 'Spin the Wheel'), ('purchase_redemption', 'Purchase Redemption'), ('admin_adjustment', 'Admin Adjustment'), ('bonus', 'Bonus')], max_length=20)),
                ('amount', models.IntegerField(help_text='Positive for credit, negative for debit')),
                ('description', models.TextField(blank=True, null=True)),
                ('balance_after', models.PositiveIntegerField(help_text='Wallet balance after this transaction')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('game_id', models.UUIDField(blank=True, help_text='Reference to game if applicable', null=True)),
                ('order_id', models.UUIDField(blank=True, help_text='Reference to order if applicable', null=True)),
                ('wallet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='wallet.wallet')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
