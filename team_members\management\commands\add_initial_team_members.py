from django.core.management.base import BaseCommand
from team_members.models import TeamMember

class Command(BaseCommand):
    help = 'Adds initial team members to the database'

    def handle(self, *args, **options):
        # Check if team members already exist
        if TeamMember.objects.exists():
            self.stdout.write(self.style.WARNING('Team members already exist. Skipping...'))
            return

        # Create the initial team members
        team_members = [
            {
                'name': '<PERSON><PERSON>',
                'role': 'Founder & CEO',
                'order': 1,
            },
            {
                'name': '<PERSON><PERSON><PERSON>',
                'role': 'Chief Product Officer',
                'order': 2,
            },
        ]

        # Add team members to the database
        for member_data in team_members:
            TeamMember.objects.create(**member_data)
            self.stdout.write(self.style.SUCCESS(f'Added team member: {member_data["name"]} - {member_data["role"]}'))

        self.stdout.write(self.style.SUCCESS('Successfully added initial team members'))
