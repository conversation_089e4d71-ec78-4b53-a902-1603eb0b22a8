from django.core.management.base import BaseCommand
from service_control.models import ServiceControl


class Command(BaseCommand):
    help = 'Create the initial ServiceControl instance if it does not exist'

    def handle(self, *args, **options):
        if not ServiceControl.objects.exists():
            ServiceControl.objects.create(
                celery_enabled=True,
                redis_enabled=True
            )
            self.stdout.write(self.style.SUCCESS('ServiceControl instance created successfully'))
        else:
            self.stdout.write(self.style.SUCCESS('ServiceControl instance already exists'))
