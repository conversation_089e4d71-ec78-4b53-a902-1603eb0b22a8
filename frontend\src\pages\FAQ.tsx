import React, { useState } from 'react';
import { COMPANY_CONTACT } from '../utils/phoneUtils';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

const FAQ = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const faqItems: FAQItem[] = [
    // Shopping & Orders
    {
      question: 'How do I place an order?',
      answer: 'To place an order, browse our products, add items to your cart, and proceed to checkout. You\'ll need to create an account or log in, provide shipping information, and complete the payment process.',
      category: 'orders'
    },
    {
      question: 'Can I change or cancel my order?',
      answer: 'Orders can only be canceled within 2 hours of placement. After production starts, we can no longer cancel or edit the order. Please email us <NAME_EMAIL> if you need assistance with cancellation.',
      category: 'orders'
    },

    // Gaming & Entertainment
    {
      question: 'What games can I play on PickMeTrend?',
      answer: 'We offer 5 exciting games: <PERSON><PERSON>, Rock Paper Scissors, Number Guessing Battle, Color Match, and Memory Card Match. All games can be played against AI opponents or other players to earn tokens!',
      category: 'gaming'
    },
    {
      question: 'How do I start playing games?',
      answer: 'Visit the Gaming Dashboard from the main menu, choose your game, and click "Play vs AI" for instant gameplay or "Find Opponent" for multiplayer battles. You need tokens to play games.',
      category: 'gaming'
    },
    {
      question: 'What are the game rewards?',
      answer: 'Our standardized reward system: Pay 2 tokens to play, Win +5 tokens, Draw +2 tokens, Loss -1 token. New users get 200 tokens as a signup bonus!',
      category: 'gaming'
    },
    {
      question: 'Can I play games without tokens?',
      answer: 'No, you need tokens to play games. However, new users receive 200 tokens upon signup. You can also request token refills through customer support if your balance is low.',
      category: 'gaming'
    },
    {
      question: 'How does the AI difficulty work?',
      answer: 'Our AI opponents are designed to be challenging but fair. Tic Tac Toe uses hard mode for optimal play, while other games use adaptive AI that provides an engaging experience.',
      category: 'gaming'
    },

    // Wallet & Tokens
    {
      question: 'What is the PickMeTrend wallet?',
      answer: 'Your PickMeTrend wallet stores tokens that you can use for gaming and future shopping. It displays your token balance and equivalent INR value. Tokens can be earned through games or purchased.',
      category: 'wallet'
    },
    {
      question: 'How do I check my wallet balance?',
      answer: 'Your wallet balance is displayed on the Gaming Dashboard and in your account profile. You can see both your token count and the equivalent value in Indian Rupees.',
      category: 'wallet'
    },
    {
      question: 'How can I get more tokens?',
      answer: 'You can earn tokens by winning games, or request a token refill through customer support. New users automatically receive 200 tokens when they create an account.',
      category: 'wallet'
    },
    {
      question: 'Can I use tokens for shopping?',
      answer: 'Currently, tokens are primarily used for gaming. We\'re working on expanding token usage for shopping discounts and special offers in future updates.',
      category: 'wallet'
    },
    {
      question: 'What happens if I run out of tokens?',
      answer: 'If your token balance reaches zero, you can request a refill through customer support. We\'re happy to help active players continue enjoying our games!',
      category: 'wallet'
    },

    // Payments
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept all major payment methods in India: UPI (Google Pay, PhonePe, Paytm), Credit/Debit Cards (Visa, MasterCard, RuPay), Net Banking from all major Indian banks, and Cash on Delivery (COD) through our secure Razorpay payment gateway.',
      category: 'payments'
    },
    {
      question: 'Is UPI payment available?',
      answer: 'Yes! UPI is our most popular payment method. You can pay using Google Pay, PhonePe, Paytm, BHIM, or any UPI app. UPI payments are instant and secure with no additional charges.',
      category: 'payments'
    },
    {
      question: 'Are my personal and payment details secure?',
      answer: 'Absolutely! We use Razorpay\'s PCI DSS compliant payment gateway with 256-bit SSL encryption. Your payment details are never stored on our servers and all transactions are secured by Indian banking standards.',
      category: 'payments'
    },

    // Shipping
    {
      question: 'How long does delivery take in India?',
      answer: 'Orders are processed within 2-5 business days. Delivery time varies by location: Metro cities (3-5 days), Tier 1 cities (4-6 days), Tier 2 cities (5-7 days), Tier 3 & rural areas (6-10 days), and remote areas (8-15 days).',
      category: 'shipping'
    },
    {
      question: 'Do you deliver across India?',
      answer: 'Yes! We deliver to all 28 states and 8 union territories of India, including remote areas like Northeast states, Kashmir, Andaman & Nicobar Islands, and Lakshadweep. We use trusted partners like Blue Dart, DTDC, Delhivery, and India Post.',
      category: 'shipping'
    },
    {
      question: 'What are the shipping charges?',
      answer: 'Free shipping on orders above ₹999 across India. Orders below ₹999 have a shipping charge of ₹99. Cash on Delivery (COD) is available with an additional ₹50 charge. All prices include GST.',
      category: 'shipping'
    },
    {
      question: 'How can I track my order?',
      answer: 'You will receive SMS and email notifications with tracking details once your order is dispatched. You can track your order using the tracking number on our logistics partner\'s website or app.',
      category: 'shipping'
    },
    {
      question: 'Is Cash on Delivery (COD) available?',
      answer: 'Yes, COD is available in most serviceable areas across India. COD charges are ₹50 per order with a maximum limit of ₹5,000 per order. You can pay in cash to the delivery person.',
      category: 'shipping'
    },

    // Returns
    {
      question: 'What is your return policy?',
      answer: 'We offer replacements or refunds if the product is defective, damaged during shipping, or if you received the wrong product. Please contact us within 30 days of receiving your order with clear photos of the issue.',
      category: 'returns'
    },
    {
      question: 'What items are not eligible for returns?',
      answer: 'We do not accept returns for wrong size ordered, change of mind, or incorrect shipping address provided by the customer. Please check our size charts carefully before ordering.',
      category: 'returns'
    },

    // General
    {
      question: 'How do I contact customer service?',
      answer: `You can reach our customer service team via email at ${COMPANY_CONTACT.EMAIL} or by phone/WhatsApp at ${COMPANY_CONTACT.DISPLAY_PHONE}. Our business hours are ${COMPANY_CONTACT.BUSINESS_HOURS}.`,
      category: 'general'
    },
    {
      question: 'Do you offer discounts or promotions?',
      answer: 'Yes, we regularly offer seasonal promotions and discounts. Sign up for our newsletter to stay informed about our latest offers and sales events. Plus, earn tokens through gaming for future benefits!',
      category: 'general'
    },
    {
      question: 'What makes PickMeTrend different?',
      answer: 'PickMeTrend combines fashion shopping with gaming entertainment! Shop for trendy products and enjoy our gaming platform where you can earn tokens, compete with others, and have fun while shopping.',
      category: 'general'
    }
  ];

  const categories = [
    { id: 'all', name: 'All Questions' },
    { id: 'gaming', name: '🎮 Gaming' },
    { id: 'wallet', name: '💰 Wallet & Tokens' },
    { id: 'orders', name: '🛒 Orders' },
    { id: 'shipping', name: '📦 Shipping' },
    { id: 'returns', name: '↩️ Returns & Refunds' },
    { id: 'payments', name: '💳 Payments' },
    { id: 'general', name: '❓ General' }
  ];

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const filteredFAQs = faqItems.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = searchQuery === '' ||
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  return (
    <div className="container mx-auto p-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Everything you need to know about shopping, gaming, and earning tokens at PickMeTrend
          </p>
        </div>

        <div className="mb-8">
          <input
            type="text"
            placeholder="Search FAQs..."
            value={searchQuery}
            onChange={handleSearch}
            className="w-full p-3 border rounded-lg focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="flex flex-wrap gap-2 mb-8">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-4 py-2 rounded-full ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        <div className="space-y-4">
          {filteredFAQs.length > 0 ? (
            filteredFAQs.map((faq, index) => (
              <details
                key={index}
                className="bg-white rounded-lg shadow-md overflow-hidden"
              >
                <summary className="px-6 py-4 cursor-pointer text-lg font-medium flex justify-between items-center">
                  {faq.question}
                  <span className="text-blue-600">+</span>
                </summary>
                <div className="px-6 py-4 border-t">
                  <p className="text-gray-700">{faq.answer}</p>
                </div>
              </details>
            ))
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-lg">No matching FAQs found.</p>
              <p className="text-gray-600 mt-2">Try adjusting your search or category filter.</p>
            </div>
          )}
        </div>

        <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 text-center">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">Still Have Questions?</h2>
          <p className="mb-6 text-gray-600 max-w-2xl mx-auto">
            Our customer service team is here to help you with shopping, gaming, wallet issues, or any other questions you may have.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
            >
              📧 Contact Support
            </a>
            <a
              href="/gaming"
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 font-medium"
            >
              🎮 Try Gaming
            </a>
          </div>
          <div className="mt-6 text-sm text-gray-500">
            <p>💡 Need tokens? Contact us for a refill!</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQ;