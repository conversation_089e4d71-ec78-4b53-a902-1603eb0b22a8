# Generated by Django 5.0.2 on 2025-05-31 07:10

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0005_alter_cartitem_unique_together'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReturnRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('reason', models.CharField(choices=[('defective', 'Defective Product'), ('wrong_item', 'Wrong Item Received'), ('not_as_described', 'Not as Described'), ('damaged_shipping', 'Damaged During Shipping'), ('size_issue', 'Size Issue'), ('quality_issue', 'Quality Issue'), ('changed_mind', 'Changed Mind'), ('other', 'Other')], max_length=50)),
                ('reason_detail', models.TextField(help_text='Detailed explanation of the return reason')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('processing', 'Processing'), ('completed', 'Completed')], default='pending', max_length=20)),
                ('admin_notes', models.TextField(blank=True, help_text='Internal notes for admin use', null=True)),
                ('admin_response', models.TextField(blank=True, help_text='Response message to customer', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('processed_at', models.DateTimeField(blank=True, help_text='When the return was processed', null=True)),
                ('refund_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('tracking_number', models.CharField(blank=True, help_text='Return shipping tracking number', max_length=100, null=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='return_requests', to='orders.order')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='return_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('order', 'user')},
            },
        ),
    ]
