#!/usr/bin/env python
"""
Railway + Vercel Setup Verification
==================================

This script verifies that the token purchase feature is properly configured
for Railway (backend) + Vercel (frontend) deployment.
"""

import os
import json
from pathlib import Path

def check_frontend_vercel_config():
    """Check frontend configuration for Vercel deployment"""
    print("🌐 Checking Frontend Configuration (Vercel)")
    print("=" * 60)
    
    frontend_path = Path("pickmetrendfrontend-vercel")
    issues = []
    
    # Check .env.production
    env_prod_path = frontend_path / ".env.production"
    if env_prod_path.exists():
        with open(env_prod_path, 'r') as f:
            env_content = f.read()
        
        print("✅ .env.production exists")
        
        # Check Railway backend URL
        if "web-production-e8443.up.railway.app" in env_content:
            print("   ✅ Railway backend URL configured")
        else:
            print("   ❌ Railway backend URL not found")
            issues.append("Railway backend URL missing in .env.production")
        
        # Check Razorpay live key
        if "rzp_live_1IrGH0WEYdtFdh" in env_content:
            print("   ✅ Razorpay live key configured")
        else:
            print("   ❌ Razorpay live key not found")
            issues.append("Razorpay live key missing")
        
        # Check payment test mode is disabled
        if "REACT_APP_PAYMENT_TEST_MODE=false" in env_content:
            print("   ✅ Payment test mode disabled (production ready)")
        else:
            print("   ❌ Payment test mode not properly configured")
            issues.append("Payment test mode should be false for production")
            
    else:
        print("❌ .env.production not found")
        issues.append(".env.production file missing")
    
    # Check vercel.json
    vercel_json_path = frontend_path / "vercel.json"
    if vercel_json_path.exists():
        print("✅ vercel.json exists")
        
        with open(vercel_json_path, 'r') as f:
            vercel_config = json.load(f)
        
        # Check SPA routing
        if "rewrites" in vercel_config:
            print("   ✅ SPA routing configured")
        else:
            print("   ❌ SPA routing not configured")
            issues.append("SPA routing missing in vercel.json")
        
        # Check CORS headers
        if "headers" in vercel_config:
            print("   ✅ CORS headers configured")
        else:
            print("   ❌ CORS headers not configured")
            issues.append("CORS headers missing in vercel.json")
            
    else:
        print("❌ vercel.json not found")
        issues.append("vercel.json file missing")
    
    # Check if render.yaml was removed (not needed for Vercel)
    render_yaml_path = frontend_path / "render.yaml"
    if not render_yaml_path.exists():
        print("✅ render.yaml properly removed (not needed for Vercel)")
    else:
        print("⚠️  render.yaml still exists (should be removed for Vercel)")
        issues.append("render.yaml should be removed for Vercel deployment")
    
    return issues

def check_backend_railway_config():
    """Check backend configuration for Railway deployment"""
    print("\n🚂 Checking Backend Configuration (Railway)")
    print("=" * 60)
    
    issues = []
    
    # Check settings.py for Railway configuration
    settings_path = Path("dropshipping_backend") / "settings.py"
    if settings_path.exists():
        try:
            with open(settings_path, 'r', encoding='utf-8') as f:
                settings_content = f.read()
        except UnicodeDecodeError:
            with open(settings_path, 'r', encoding='latin-1') as f:
                settings_content = f.read()
        
        print("✅ settings.py exists")
        
        # Check Razorpay live keys
        if "rzp_live_1IrGH0WEYdtFdh" in settings_content:
            print("   ✅ Razorpay live key configured")
        else:
            print("   ❌ Razorpay live key not found")
            issues.append("Razorpay live key missing in settings.py")
        
        # Check CORS configuration
        if "CORS_ALLOWED_ORIGINS" in settings_content:
            print("   ✅ CORS configuration found")
        else:
            print("   ❌ CORS configuration not found")
            issues.append("CORS configuration missing")
            
    else:
        print("❌ settings.py not found")
        issues.append("settings.py file missing")
    
    # Check token purchase models
    models_path = Path("wallet") / "models.py"
    if models_path.exists():
        try:
            with open(models_path, 'r', encoding='utf-8') as f:
                models_content = f.read()
        except UnicodeDecodeError:
            with open(models_path, 'r', encoding='latin-1') as f:
                models_content = f.read()
        
        if "TokenPack" in models_content and "TokenPurchase" in models_content:
            print("✅ Token purchase models exist")
        else:
            print("❌ Token purchase models not found")
            issues.append("Token purchase models missing")
    
    # Check token purchase views
    views_path = Path("wallet") / "token_purchase_views.py"
    if views_path.exists():
        print("✅ Token purchase views exist")
    else:
        print("❌ Token purchase views not found")
        issues.append("Token purchase views missing")
    
    # Check URLs configuration
    urls_path = Path("wallet") / "urls.py"
    if urls_path.exists():
        try:
            with open(urls_path, 'r', encoding='utf-8') as f:
                urls_content = f.read()
        except UnicodeDecodeError:
            with open(urls_path, 'r', encoding='latin-1') as f:
                urls_content = f.read()
        
        required_endpoints = [
            'token-packs/',
            'create-token-order/',
            'verify-token-payment/'
        ]
        
        missing_endpoints = []
        for endpoint in required_endpoints:
            if endpoint in urls_content:
                print(f"   ✅ {endpoint} endpoint configured")
            else:
                print(f"   ❌ {endpoint} endpoint missing")
                missing_endpoints.append(endpoint)
        
        if missing_endpoints:
            issues.append(f"Missing endpoints: {', '.join(missing_endpoints)}")
    
    return issues

def check_deployment_readiness():
    """Check overall deployment readiness"""
    print("\n🚀 Checking Deployment Readiness")
    print("=" * 60)
    
    issues = []
    
    # Check if TokenPurchasePage exists
    token_page_path = Path("pickmetrendfrontend-vercel") / "src" / "pages" / "TokenPurchasePage.tsx"
    if token_page_path.exists():
        print("✅ TokenPurchasePage.tsx exists")
    else:
        print("❌ TokenPurchasePage.tsx not found")
        issues.append("TokenPurchasePage.tsx missing")
    
    # Check if route is added to App.tsx
    app_tsx_path = Path("pickmetrendfrontend-vercel") / "src" / "App.tsx"
    if app_tsx_path.exists():
        try:
            with open(app_tsx_path, 'r', encoding='utf-8') as f:
                app_content = f.read()
        except UnicodeDecodeError:
            with open(app_tsx_path, 'r', encoding='latin-1') as f:
                app_content = f.read()
        
        if '/buy-tokens' in app_content and 'TokenPurchasePage' in app_content:
            print("✅ Token purchase route added to App.tsx")
        else:
            print("❌ Token purchase route not found in App.tsx")
            issues.append("Token purchase route missing in App.tsx")
    
    # Check documentation
    docs = [
        "RAILWAY_VERCEL_DEPLOYMENT_GUIDE.md",
        "PRODUCTION_DEPLOYMENT_CHECKLIST.md"
    ]
    
    for doc in docs:
        if Path(doc).exists():
            print(f"✅ {doc} exists")
        else:
            print(f"❌ {doc} missing")
            issues.append(f"Documentation {doc} missing")
    
    return issues

def main():
    """Main verification function"""
    print("🔍 Railway + Vercel Deployment Verification")
    print("=" * 70)
    
    all_issues = []
    
    # Check frontend (Vercel)
    frontend_issues = check_frontend_vercel_config()
    all_issues.extend(frontend_issues)
    
    # Check backend (Railway)
    backend_issues = check_backend_railway_config()
    all_issues.extend(backend_issues)
    
    # Check deployment readiness
    deployment_issues = check_deployment_readiness()
    all_issues.extend(deployment_issues)
    
    # Summary
    print("\n🎯 Verification Summary")
    print("=" * 60)
    
    if not all_issues:
        print("🎉 ALL CHECKS PASSED!")
        print("✅ Frontend configured for Vercel")
        print("✅ Backend configured for Railway")
        print("✅ Token purchase system ready")
        print("✅ Documentation complete")
        
        print("\n🚀 READY FOR RAILWAY + VERCEL DEPLOYMENT!")
        
        print("\n📋 Next Steps:")
        print("1. Push backend changes to trigger Railway deployment")
        print("2. Push frontend changes to trigger Vercel deployment")
        print("3. Set environment variables in Railway dashboard")
        print("4. Set environment variables in Vercel project settings")
        print("5. Test production deployment")
        
        print("\n🔗 Deployment URLs:")
        print("   Backend:  https://web-production-e8443.up.railway.app")
        print("   Frontend: https://pickmetrendfrontend-vercel.vercel.app")
        print("   Tokens:   https://pickmetrendfrontend-vercel.vercel.app/buy-tokens")
        
    else:
        print("❌ ISSUES FOUND:")
        for i, issue in enumerate(all_issues, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n🔧 Please fix {len(all_issues)} issue(s) before deployment")
    
    return len(all_issues) == 0

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
