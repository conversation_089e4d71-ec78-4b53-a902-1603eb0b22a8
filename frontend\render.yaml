services:
  - type: web
    name: pickmetrend-frontend
    env: node
    region: oregon
    buildCommand: |
      echo "Current directory: $(pwd)"
      echo "Files in current directory:"
      ls -la
      echo "Installing dependencies..."
      npm install
      echo "Building React app..."
      CI=false npm run build
      echo "Build completed!"
    startCommand: npx serve -s build -p $PORT
    envVars:
      - key: NODE_VERSION
        value: 20.11.0
      - key: CI
        value: false
      - key: REACT_APP_API_URL
        value: https://pickmetrendofficial-render.onrender.com
      - key: REACT_APP_RAZORPAY_KEY_ID
        value: rzp_live_1IrGH0WEYdtFdh
