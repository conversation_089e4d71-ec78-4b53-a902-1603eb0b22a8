from django.contrib import admin
from django.utils.html import format_html
from django.urls import path
from django.shortcuts import redirect
from django.contrib import messages
from django.template.response import TemplateResponse

from .models import ServiceControl


@admin.register(ServiceControl)
class ServiceControlAdmin(admin.ModelAdmin):
    """
    Admin interface for ServiceControl model.

    Provides a clean interface to manage service statuses with:
    - Toggle buttons for enabling/disabling services
    - Visual indicators of current status
    - Custom actions for toggling services
    """
    list_display = ['__str__', 'celery_status_display', 'redis_status_display', 'updated_at']
    readonly_fields = ['created_at', 'updated_at', 'celery_status_display', 'redis_status_display']
    fieldsets = (
        ('Service Status', {
            'fields': ('celery_enabled', 'celery_status_display', 'redis_enabled', 'redis_status_display'),
            'description': 'Toggle services on and off. Changes take effect immediately.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        """Only allow adding if no instance exists."""
        return not ServiceControl.objects.exists()

    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of the settings instance."""
        return False

    def celery_status_display(self, obj):
        """Display Celery status with color indicator."""
        if obj.celery_enabled:
            return format_html(
                '<span style="color: green; font-weight: bold;">ON</span> '
                '<a href="{}" class="button" style="background-color: #d9534f; color: white;">Disable</a>',
                f'/admin/service_control/servicecontrol/toggle-celery/'
            )
        else:
            return format_html(
                '<span style="color: red; font-weight: bold;">OFF</span> '
                '<a href="{}" class="button" style="background-color: #5cb85c; color: white;">Enable</a>',
                f'/admin/service_control/servicecontrol/toggle-celery/'
            )
    celery_status_display.short_description = 'Celery Status'

    def redis_status_display(self, obj):
        """Display Redis status with color indicator."""
        if obj.redis_enabled:
            return format_html(
                '<span style="color: green; font-weight: bold;">ON</span> '
                '<a href="{}" class="button" style="background-color: #d9534f; color: white;">Disable</a>',
                f'/admin/service_control/servicecontrol/toggle-redis/'
            )
        else:
            return format_html(
                '<span style="color: red; font-weight: bold;">OFF</span> '
                '<a href="{}" class="button" style="background-color: #5cb85c; color: white;">Enable</a>',
                f'/admin/service_control/servicecontrol/toggle-redis/'
            )
    redis_status_display.short_description = 'Redis Status'

    def get_urls(self):
        """Add custom URLs for toggling services."""
        urls = super().get_urls()
        custom_urls = [
            path('toggle-celery/', self.admin_site.admin_view(self.toggle_celery), name='toggle-celery'),
            path('toggle-redis/', self.admin_site.admin_view(self.toggle_redis), name='toggle-redis'),
            path('dashboard/', self.admin_site.admin_view(self.service_dashboard), name='service-dashboard'),
        ]
        return custom_urls + urls

    def toggle_celery(self, request):
        """Toggle Celery enabled/disabled."""
        service_control = ServiceControl.get_instance()
        service_control.celery_enabled = not service_control.celery_enabled
        service_control.save()

        status = "enabled" if service_control.celery_enabled else "disabled"
        messages.success(request, f"Celery has been {status} successfully.")

        return redirect('admin:service_control_servicecontrol_changelist')

    def toggle_redis(self, request):
        """Toggle Redis enabled/disabled."""
        service_control = ServiceControl.get_instance()
        service_control.redis_enabled = not service_control.redis_enabled
        service_control.save()

        status = "enabled" if service_control.redis_enabled else "disabled"
        messages.success(request, f"Redis has been {status} successfully.")

        return redirect('admin:service_control_servicecontrol_changelist')

    def service_dashboard(self, request):
        """Custom dashboard view for service control."""
        service_control = ServiceControl.get_instance()

        context = {
            'title': 'Service Control Dashboard',
            'service_control': service_control,
            'opts': self.model._meta,
            'has_change_permission': self.has_change_permission(request),
        }

        return TemplateResponse(request, 'admin/service_control/dashboard.html', context)
