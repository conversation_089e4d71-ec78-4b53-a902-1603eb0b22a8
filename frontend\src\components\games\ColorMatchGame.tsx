import React, { useState, useEffect, useCallback } from 'react';
import { gameSessionService } from '../../services/gameSessionService';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';

interface GameState {
  targetColor: string;
  targetColorDisplay: string;
  playerScore: number;
  botScore: number;
  currentPlayer: 'player' | 'bot';
  round: number;
  maxRounds: number;
  gameStatus: 'waiting' | 'playing' | 'finished';
  timeLeft: number;
  gameId: string | null;
}

const COLORS = [
  { name: 'RED', value: '#ff4757', textColor: '#2ed573' },
  { name: 'BLUE', value: '#3742fa', textColor: '#ff6348' },
  { name: 'GREEN', value: '#2ed573', textColor: '#3742fa' },
  { name: 'YELLOW', value: '#ffa502', textColor: '#ff4757' },
  { name: 'PURPLE', value: '#a55eea', textColor: '#ffa502' },
  { name: 'ORANGE', value: '#ff6348', textColor: '#a55eea' }
];

const ColorMatchGame: React.FC = () => {
  const { user } = useAuth();
  const { wallet, refreshWallet } = useWallet();
  
  const [gameState, setGameState] = useState<GameState>({
    targetColor: '',
    targetColorDisplay: '',
    playerScore: 0,
    botScore: 0,
    currentPlayer: 'player',
    round: 1,
    maxRounds: 5,
    gameStatus: 'waiting',
    timeLeft: 10,
    gameId: null
  });

  const [selectedColor, setSelectedColor] = useState<string>('');
  const [showResult, setShowResult] = useState(false);
  const [gameResult, setGameResult] = useState<'win' | 'loss' | 'draw' | null>(null);
  const [tokensEarned, setTokensEarned] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Generate random target color with Stroop effect
  const generateTargetColor = useCallback(() => {
    const targetColor = COLORS[Math.floor(Math.random() * COLORS.length)];
    const displayColor = COLORS[Math.floor(Math.random() * COLORS.length)];
    
    setGameState(prev => ({
      ...prev,
      targetColor: targetColor.name,
      targetColorDisplay: displayColor.textColor
    }));
  }, []);

  // Start new game
  const startGame = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await gameSessionService.startGameSession('color_match');

      if (result.success) {
        setGameState(prev => ({
          ...prev,
          gameId: result.session_id,
          gameStatus: 'playing',
          playerScore: 0,
          botScore: 0,
          round: 1,
          currentPlayer: 'player',
          timeLeft: 10
        }));

        generateTargetColor();
        setShowResult(false);
        setSelectedColor('');

        // Refresh wallet to show updated balance
        await refreshWallet();
      } else {
        alert(result.error || 'Failed to start game');
      }
    } catch (error) {
      console.error('Error starting game:', error);
      alert('Failed to start game');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle player color selection
  const handleColorSelect = (colorName: string) => {
    if (gameState.gameStatus !== 'playing' || gameState.currentPlayer !== 'player') return;
    
    setSelectedColor(colorName);
    
    // Check if correct
    const isCorrect = colorName === gameState.targetColor;
    
    setGameState(prev => ({
      ...prev,
      playerScore: prev.playerScore + (isCorrect ? 1 : 0),
      currentPlayer: 'bot'
    }));

    // Bot turn after delay
    setTimeout(() => {
      handleBotTurn();
    }, 1500);
  };

  // Bot AI logic (80% accuracy as specified)
  const handleBotTurn = () => {
    const isCorrect = Math.random() < 0.8; // 80% accuracy
    const botChoice = isCorrect 
      ? gameState.targetColor 
      : COLORS[Math.floor(Math.random() * COLORS.length)].name;

    setGameState(prev => ({
      ...prev,
      botScore: prev.botScore + (isCorrect ? 1 : 0),
      currentPlayer: 'player',
      round: prev.round + 1
    }));

    // Check if game is finished
    if (gameState.round >= gameState.maxRounds) {
      finishGame();
    } else {
      // Generate new target color for next round
      setTimeout(() => {
        generateTargetColor();
        setSelectedColor('');
      }, 1000);
    }
  };

  // Finish game and calculate result
  const finishGame = async () => {
    if (!gameState.gameId) return;

    let result: 'win' | 'loss' | 'draw';
    if (gameState.playerScore > gameState.botScore) {
      result = 'win';
    } else if (gameState.playerScore < gameState.botScore) {
      result = 'loss';
    } else {
      result = 'draw';
    }

    setGameResult(result);
    setGameState(prev => ({ ...prev, gameStatus: 'finished' }));

    // Submit result to backend using new GameSession API
    try {
      const gameData = {
        rounds: gameState.maxRounds,
        final_score: {
          player: gameState.playerScore,
          bot: gameState.botScore
        },
        max_rounds: gameState.maxRounds
      };

      const submitResult = await gameSessionService.completeGameSession(
        gameState.gameId,
        result,
        gameData
      );

      if (submitResult.success) {
        setTokensEarned(submitResult.tokens_earned);
        await refreshWallet();
      }
    } catch (error) {
      console.error('Error submitting game result:', error);
    }

    setShowResult(true);
  };

  // Timer effect
  useEffect(() => {
    if (gameState.gameStatus === 'playing' && gameState.currentPlayer === 'player') {
      const timer = setInterval(() => {
        setGameState(prev => {
          if (prev.timeLeft <= 1) {
            // Time's up, bot gets the point
            setTimeout(() => handleBotTurn(), 100);
            return { ...prev, timeLeft: 10, currentPlayer: 'bot' };
          }
          return { ...prev, timeLeft: prev.timeLeft - 1 };
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [gameState.gameStatus, gameState.currentPlayer, gameState.timeLeft]);

  if (showResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h2 className="text-3xl font-bold mb-6">🎨 Game Complete!</h2>
          
          <div className="space-y-4 mb-6">
            <div className="flex justify-between text-lg">
              <span>Your Score:</span>
              <span className="font-bold">{gameState.playerScore}</span>
            </div>
            <div className="flex justify-between text-lg">
              <span>Bot Score:</span>
              <span className="font-bold">{gameState.botScore}</span>
            </div>
          </div>

          <div className="mb-6">
            {gameResult === 'win' && (
              <div className="text-green-400">
                <div className="text-2xl font-bold">🎉 You Won!</div>
                <div className="text-lg">+{tokensEarned} tokens</div>
              </div>
            )}
            {gameResult === 'loss' && (
              <div className="text-red-400">
                <div className="text-2xl font-bold">😔 You Lost</div>
                <div className="text-lg">{tokensEarned} tokens</div>
              </div>
            )}
            {gameResult === 'draw' && (
              <div className="text-yellow-400">
                <div className="text-2xl font-bold">🤝 Draw!</div>
                <div className="text-lg">+{tokensEarned} tokens</div>
              </div>
            )}
          </div>

          <div className="mb-6">
            <div className="text-sm opacity-75">Current Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Play Again'}
          </button>
        </div>
      </div>
    );
  }

  if (gameState.gameStatus === 'waiting') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h1 className="text-4xl font-bold mb-4">🎨 Color Match</h1>
          <p className="text-lg mb-4 opacity-90">
            Tap the correct color name! Watch out for the Stroop effect - the text color might confuse you!
          </p>

          <div className="mb-4">
            <div className="px-3 py-2 bg-yellow-50/20 border border-yellow-200/30 rounded-lg inline-block">
              <span className="text-sm font-medium text-yellow-200">🟡 Medium Difficulty</span>
            </div>
          </div>
          
          <div className="mb-6">
            <div className="text-sm opacity-75">Your Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <div className="mb-6 text-sm opacity-75">
            <div>Win: +5 tokens</div>
            <div>Draw: +2 tokens</div>
            <div>Lose: -1 token</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Start Game'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Game Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 text-white">
          <div className="flex justify-between items-center mb-4">
            <div className="text-center">
              <div className="text-sm opacity-75">Your Score</div>
              <div className="text-2xl font-bold">{gameState.playerScore}</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold">Round {gameState.round}/{gameState.maxRounds}</div>
              <div className="text-sm opacity-75">Time: {gameState.timeLeft}s</div>
            </div>
            <div className="text-center">
              <div className="text-sm opacity-75">Bot Score</div>
              <div className="text-2xl font-bold">{gameState.botScore}</div>
            </div>
          </div>
        </div>

        {/* Target Color Display */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-6 text-center">
          <div className="text-lg mb-4 text-white opacity-75">Tap the color:</div>
          <div 
            className="text-6xl font-bold mb-4"
            style={{ color: gameState.targetColorDisplay }}
          >
            {gameState.targetColor}
          </div>
          <div className="text-sm text-white opacity-60">
            {gameState.currentPlayer === 'player' ? "Your turn!" : "Bot is thinking..."}
          </div>
        </div>

        {/* Color Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {COLORS.map((color) => (
            <button
              key={color.name}
              onClick={() => handleColorSelect(color.name)}
              disabled={gameState.currentPlayer !== 'player'}
              className={`
                aspect-square rounded-2xl font-bold text-white text-xl
                transition-all duration-200 transform hover:scale-105
                disabled:opacity-50 disabled:cursor-not-allowed
                ${selectedColor === color.name ? 'ring-4 ring-white' : ''}
              `}
              style={{ backgroundColor: color.value }}
            >
              {color.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ColorMatchGame;
