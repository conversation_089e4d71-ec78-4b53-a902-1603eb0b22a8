import React, { useState, useEffect } from 'react';
import { runFrontendDiagnostic, clearBrowserCache, testAPIEndpoint, testImageURL } from '../utils/debugUtils';

interface DiagnosticResult {
  debugInfo: any;
  apiResults: any[];
  imageResults: any[];
  summary: {
    apiSuccessRate: number;
    imageSuccessRate: number;
    issues: string[];
  };
}

const FrontendDebug: React.FC = () => {
  const [diagnosticResult, setDiagnosticResult] = useState<DiagnosticResult | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [testUrl, setTestUrl] = useState('');
  const [testImageUrl, setTestImageUrl] = useState('');

  const runDiagnostic = async () => {
    setIsRunning(true);
    try {
      const result = await runFrontendDiagnostic();
      setDiagnosticResult(result);
    } catch (error) {
      console.error('Diagnostic failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const handleClearCache = () => {
    clearBrowserCache();
    alert('Cache cleared! Please hard refresh the page (Ctrl+F5)');
  };

  const testCustomAPI = async () => {
    if (!testUrl) return;
    const result = await testAPIEndpoint(testUrl);
    console.log('Custom API test result:', result);
  };

  const testCustomImage = async () => {
    if (!testImageUrl) return;
    const result = await testImageURL(testImageUrl);
    console.log('Custom image test result:', result);
  };

  useEffect(() => {
    // Auto-run diagnostic on component mount
    runDiagnostic();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Frontend Debug Dashboard</h1>
        
        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={runDiagnostic}
              disabled={isRunning}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isRunning ? 'Running...' : 'Run Full Diagnostic'}
            </button>
            
            <button
              onClick={handleClearCache}
              className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
            >
              Clear Browser Cache
            </button>
            
            <button
              onClick={() => window.location.reload()}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Hard Refresh Page
            </button>
          </div>
        </div>

        {/* Custom Testing */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Custom Testing</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Test API Endpoint:</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={testUrl}
                onChange={(e) => setTestUrl(e.target.value)}
                placeholder="/api/products/items/"
                className="flex-1 border border-gray-300 rounded px-3 py-2"
              />
              <button
                onClick={testCustomAPI}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Test
              </button>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Test Image URL:</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={testImageUrl}
                onChange={(e) => setTestImageUrl(e.target.value)}
                placeholder="https://images-api.printify.com/..."
                className="flex-1 border border-gray-300 rounded px-3 py-2"
              />
              <button
                onClick={testCustomImage}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Test
              </button>
            </div>
          </div>
        </div>

        {/* Environment Info */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Environment Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <strong>API URL:</strong> {process.env.REACT_APP_API_URL || 'Not configured'}
            </div>
            <div>
              <strong>Environment:</strong> {process.env.NODE_ENV}
            </div>
            <div>
              <strong>Current URL:</strong> {window.location.href}
            </div>
            <div>
              <strong>User Agent:</strong> {navigator.userAgent.substring(0, 50)}...
            </div>
          </div>
        </div>

        {/* Diagnostic Results */}
        {diagnosticResult && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Diagnostic Results</h2>
            
            {/* Summary */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className={`p-4 rounded ${diagnosticResult.summary.apiSuccessRate === 100 ? 'bg-green-100' : 'bg-red-100'}`}>
                  <div className="text-sm text-gray-600">API Success Rate</div>
                  <div className="text-2xl font-bold">{diagnosticResult.summary.apiSuccessRate.toFixed(1)}%</div>
                </div>
                <div className={`p-4 rounded ${diagnosticResult.summary.imageSuccessRate === 100 ? 'bg-green-100' : 'bg-red-100'}`}>
                  <div className="text-sm text-gray-600">Image Success Rate</div>
                  <div className="text-2xl font-bold">{diagnosticResult.summary.imageSuccessRate.toFixed(1)}%</div>
                </div>
                <div className={`p-4 rounded ${diagnosticResult.summary.issues.length === 0 ? 'bg-green-100' : 'bg-yellow-100'}`}>
                  <div className="text-sm text-gray-600">Issues Found</div>
                  <div className="text-2xl font-bold">{diagnosticResult.summary.issues.length}</div>
                </div>
              </div>
            </div>

            {/* Issues */}
            {diagnosticResult.summary.issues.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Issues Detected</h3>
                <ul className="list-disc list-inside space-y-1">
                  {diagnosticResult.summary.issues.map((issue, index) => (
                    <li key={index} className="text-red-600">{issue}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* API Results */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">API Test Results</h3>
              <div className="space-y-2">
                {diagnosticResult.apiResults.map((result, index) => (
                  <div key={index} className={`p-3 rounded border ${result.success ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'}`}>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{result.endpoint}</span>
                      <span className={`px-2 py-1 rounded text-sm ${result.success ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800'}`}>
                        {result.success ? `✅ ${result.status}` : `❌ ${result.status || 'Error'}`}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      Response time: {result.responseTime}ms
                      {result.error && ` | Error: ${result.error}`}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Image Results */}
            {diagnosticResult.imageResults.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-2">Image Test Results</h3>
                <div className="space-y-2">
                  {diagnosticResult.imageResults.map((result, index) => (
                    <div key={index} className={`p-3 rounded border ${result.accessible ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'}`}>
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-sm">{result.url.substring(0, 60)}...</span>
                        <span className={`px-2 py-1 rounded text-sm ${result.accessible ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800'}`}>
                          {result.accessible ? '✅ Accessible' : '❌ Failed'}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        Load time: {result.loadTime}ms
                        {result.dimensions && ` | Size: ${result.dimensions.width}x${result.dimensions.height}`}
                        {result.error && ` | Error: ${result.error}`}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Console Instructions */}
        <div className="bg-gray-100 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4">Browser Console Commands</h2>
          <p className="mb-4">Open browser console (F12) and try these commands:</p>
          <div className="space-y-2 font-mono text-sm">
            <div><code>debugFrontend()</code> - Run full diagnostic</div>
            <div><code>clearCache()</code> - Clear browser cache</div>
            <div><code>monitorImages()</code> - Monitor image loading</div>
            <div><code>testAPI('/api/products/items/')</code> - Test specific API</div>
            <div><code>testImage('https://...')</code> - Test specific image</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FrontendDebug;
