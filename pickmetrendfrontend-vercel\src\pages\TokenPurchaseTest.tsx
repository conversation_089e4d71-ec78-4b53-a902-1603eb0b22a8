import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useWallet } from '../hooks/useWallet';
import TokenPurchaseButton from '../components/wallet/TokenPurchaseButton';
import TokenPurchaseModal from '../components/wallet/TokenPurchaseModal';
import LowBalanceAlert from '../components/wallet/LowBalanceAlert';
import WalletBalance from '../components/wallet/WalletBalance';

const TokenPurchaseTest: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { wallet, loading, error, refreshWallet } = useWallet();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [testBalance, setTestBalance] = useState(5); // Low balance for testing

  const handleTokensAdded = (tokensAdded: number) => {
    console.log(`✅ Tokens added: ${tokensAdded}`);
    refreshWallet();
    // Show success message
    alert(`Successfully purchased ${tokensAdded} tokens!`);
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Token Purchase Test</h1>
          <p className="text-gray-600 mb-4">Please log in to test the token purchase system.</p>
          <a href="/login" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Login
          </a>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🧪 Token Purchase System Test</h1>
          <p className="text-gray-600">Testing all token purchase components and functionality</p>
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <p className="text-blue-800">
              <strong>User:</strong> {user?.username} | 
              <strong> Current Balance:</strong> {wallet?.balance || 0} tokens
            </p>
          </div>
        </div>

        {/* Test Components Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          {/* Wallet Balance Component */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">1. Wallet Balance Component</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Basic Display:</h3>
                <WalletBalance />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">With Details:</h3>
                <WalletBalance showDetails={true} />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">With Buy Button:</h3>
                <WalletBalance showDetails={true} showBuyButton={true} />
              </div>
            </div>
          </div>

          {/* Token Purchase Buttons */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">2. Purchase Buttons</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Primary Button:</h3>
                <TokenPurchaseButton
                  currentBalance={wallet?.balance || 0}
                  onTokensAdded={handleTokensAdded}
                  variant="primary"
                  size="md"
                >
                  Buy Tokens
                </TokenPurchaseButton>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Secondary Button:</h3>
                <TokenPurchaseButton
                  currentBalance={wallet?.balance || 0}
                  onTokensAdded={handleTokensAdded}
                  variant="secondary"
                  size="sm"
                >
                  Quick Buy
                </TokenPurchaseButton>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Outline Button:</h3>
                <TokenPurchaseButton
                  currentBalance={wallet?.balance || 0}
                  onTokensAdded={handleTokensAdded}
                  variant="outline"
                  size="lg"
                >
                  Purchase More
                </TokenPurchaseButton>
              </div>
            </div>
          </div>

          {/* Low Balance Alert */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">3. Low Balance Alert</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Test with Low Balance:</h3>
                <div className="mb-2">
                  <label className="text-sm text-gray-600">Test Balance: </label>
                  <input
                    type="number"
                    value={testBalance}
                    onChange={(e) => setTestBalance(parseInt(e.target.value) || 0)}
                    className="ml-2 px-2 py-1 border rounded w-20"
                    min="0"
                    max="100"
                  />
                  <span className="ml-2 text-sm text-gray-500">tokens</span>
                </div>
                <LowBalanceAlert
                  balance={testBalance}
                  onTokensAdded={handleTokensAdded}
                  threshold={10}
                  autoShow={true}
                />
              </div>
            </div>
          </div>

          {/* Manual Modal Test */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">4. Purchase Modal</h2>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">Test the purchase modal directly:</p>
              <button
                onClick={() => setIsModalOpen(true)}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Open Purchase Modal
              </button>
              
              <TokenPurchaseModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onSuccess={handleTokensAdded}
                currentBalance={wallet?.balance || 0}
              />
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">🧪 Testing Instructions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-800 mb-2">✅ What to Test:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Click any "Buy Tokens" button</li>
                <li>• Modal should open with token packs</li>
                <li>• Select a token pack</li>
                <li>• Payment form should appear</li>
                <li>• Test with Razorpay test cards</li>
                <li>• Verify tokens are credited</li>
                <li>• Check low balance alerts</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800 mb-2">🧾 Test Cards (Razorpay):</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Card: 4111 1111 1111 1111</li>
                <li>• Expiry: Any future date</li>
                <li>• CVV: Any 3 digits</li>
                <li>• Name: Any name</li>
                <li>• OTP: 123456 (if required)</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h4 className="font-semibold text-yellow-800 mb-2">⚠️ Note:</h4>
            <p className="text-yellow-700 text-sm">
              This is a test environment. No real money will be charged. 
              The backend APIs may show authentication errors in browser console - this is expected for testing.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TokenPurchaseTest;
