import requests
import logging
from django.conf import settings
from typing import Dict, Any, Optional, List, Union

logger = logging.getLogger(__name__)

def place_printify_order(
    order_id: str,
    customer_details: Dict[str, Any],
    printify_product_id: str,
    variant_id: str,
    shop_id: Optional[str] = None,
    quantity: int = 1,
    shipping_method: int = 1,
    send_shipping_notification: bool = True
) -> Dict[str, Any]:
    """
    Place an order on Printify after a successful payment.

    Args:
        order_id: Your internal order ID
        customer_details: Dict containing customer shipping details (name, address, email, phone)
        printify_product_id: Printify product ID
        variant_id: Printify variant ID
        shop_id: Printify shop ID (optional, will use default from settings if not provided)
        quantity: Product quantity (default: 1)
        shipping_method: Shipping method ID (default: 1)
        send_shipping_notification: Whether to send shipping notification to customer (default: True)

    Returns:
        Dict containing the response from Printify API

    Raises:
        Exception: If the API call fails
    """
    # Get API key from settings
    api_key = getattr(settings, 'PRINTIFY_API_TOKEN', None)
    if not api_key:
        logger.error("Printify API key not found in settings")
        raise ValueError("Printify API key not found")

    # Get shop ID from settings if not provided
    if not shop_id:
        shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)
        if not shop_id:
            logger.error("Printify shop ID not found in settings")
            raise ValueError("Printify shop ID not found")

    # Validate required customer details
    required_fields = ["first_name", "email", "phone", "country", "address1", "city", "zip"]
    missing_fields = [field for field in required_fields if not customer_details.get(field)]

    if missing_fields:
        error_message = f"Missing required customer details: {', '.join(missing_fields)}"
        logger.error(error_message)

        # Provide default values for missing fields to prevent API errors
        if "first_name" in missing_fields:
            customer_details["first_name"] = "Customer"
        if "email" in missing_fields:
            customer_details["email"] = "<EMAIL>"
        if "phone" in missing_fields:
            customer_details["phone"] = "0000000000"
        if "country" in missing_fields:
            customer_details["country"] = "IN"  # Default to India
        if "address1" in missing_fields:
            customer_details["address1"] = "Default Address"
        if "city" in missing_fields:
            customer_details["city"] = "Default City"
        if "zip" in missing_fields:
            customer_details["zip"] = "000000"

        logger.warning(f"Using default values for missing fields: {missing_fields}")

    # Prepare the shipping address with defaults for empty values
    address_to = {
        "first_name": customer_details.get("first_name", "Customer"),
        "last_name": customer_details.get("last_name", ""),
        "email": customer_details.get("email", "<EMAIL>"),
        "phone": customer_details.get("phone", "0000000000"),
        "country": customer_details.get("country", "IN"),
        "region": customer_details.get("region", ""),
        "address1": customer_details.get("address1", "Default Address"),
        "address2": customer_details.get("address2", ""),
        "city": customer_details.get("city", "Default City"),
        "zip": customer_details.get("zip", "000000")
    }

    # Log the shipping address
    logger.info(f"Shipping address for Printify order: {address_to}")

    # Prepare the order data
    order_data = {
        "external_id": str(order_id),
        "label": f"Order #{order_id}",
        "line_items": [
            {
                "product_id": printify_product_id,
                "variant_id": variant_id,
                "quantity": quantity
            }
        ],
        "shipping_method": shipping_method,
        "send_shipping_notification": send_shipping_notification,
        "address_to": address_to
    }

    # Set up the API endpoint and headers
    url = f"https://api.printify.com/v1/shops/{shop_id}/orders.json"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    try:
        # Make the API call
        logger.info(f"Placing order on Printify for order ID: {order_id}")
        response = requests.post(url, json=order_data, headers=headers)
        response.raise_for_status()  # Raise exception for 4XX/5XX responses

        # Process the response
        response_data = response.json()
        logger.info(f"Successfully placed order on Printify. Printify order ID: {response_data.get('id')}")

        return {
            "success": True,
            "printify_order_id": response_data.get("id"),
            "status": response_data.get("status"),
            "data": response_data
        }

    except requests.exceptions.RequestException as e:
        # Handle API errors
        error_message = f"Failed to place order on Printify: {str(e)}"
        logger.error(error_message)

        # Try to extract more details from the response if available
        response_data = {}
        try:
            if hasattr(e, 'response') and e.response is not None:
                response_data = e.response.json()
                logger.error(f"Printify API error details: {response_data}")
        except Exception:
            pass

        return {
            "success": False,
            "error": error_message,
            "status_code": e.response.status_code if hasattr(e, 'response') else None,
            "data": response_data
        }

    except Exception as e:
        # Handle unexpected errors
        error_message = f"Unexpected error placing order on Printify: {str(e)}"
        logger.error(error_message)
        return {
            "success": False,
            "error": error_message,
            "data": {}
        }
