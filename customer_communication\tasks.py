# Try to import from celery, fall back to our mock implementation
try:
    from celery import shared_task
except ImportError:
    from dropshipping_backend.celery_mock import shared_task
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template import Template, Context
from django.utils import timezone
from django.conf import settings
from datetime import timedelta
import logging

from orders.models import Order
from .models import EmailTemplate, SupportTicket

logger = logging.getLogger(__name__)

@shared_task
def send_order_confirmation_email(order_id):
    """
    Send an order confirmation email to the customer.

    Args:
        order_id: The ID of the order
    """
    try:
        # Get the order
        order = Order.objects.get(id=order_id)

        # Get the email template
        template = EmailTemplate.objects.filter(
            template_type='order_confirmation',
            is_active=True
        ).first()

        if not template:
            # Use a default template if none exists
            subject = f"Order Confirmation - PickMeTrend Order #{order.id}"
            text_content = f"""
            Dear {order.full_name},

            Thank you for your order! We're pleased to confirm that we've received your order.

            Order Number: {order.id}
            Order Date: {order.created_at.strftime('%B %d, %Y')}
            Total Amount: ₹{order.total}

            We'll send you another email when your order ships.

            Thank you for shopping with PickMeTrend!
            """
            html_content = None
        else:
            # Prepare context for template rendering
            context = {
                'order': order,
                'customer_name': order.full_name,
                'order_id': order.id,
                'order_date': order.created_at.strftime('%B %d, %Y'),
                'order_total': order.total,
                'site_url': settings.SITE_URL if hasattr(settings, 'SITE_URL') else 'https://pickmetrend.com',
            }

            # Render the templates
            t_subject = Template(template.subject)
            t_text = Template(template.text_content)
            t_html = Template(template.html_content)

            c = Context(context)
            subject = t_subject.render(c)
            text_content = t_text.render(c)
            html_content = t_html.render(c)

        # Send the email
        from_email = settings.SUPPORT_EMAIL
        to_email = order.email

        if html_content:
            # Send HTML email
            msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
            msg.attach_alternative(html_content, "text/html")
            msg.send()
        else:
            # Send plain text email
            send_mail(subject, text_content, from_email, [to_email])

        logger.info(f"Order confirmation email sent for order {order_id}")
        return True
    except Order.DoesNotExist:
        logger.error(f"Order {order_id} not found")
        return False
    except Exception as e:
        logger.error(f"Error sending order confirmation email: {str(e)}")
        return False


@shared_task
def schedule_feedback_emails():
    """
    Schedule feedback emails for orders that were placed 7 days ago.
    This task should be run daily.
    """
    try:
        # Calculate the date 7 days ago
        feedback_date = timezone.now() - timedelta(days=settings.CUSTOMER_FEEDBACK_DAYS)

        # Find orders that were placed around that date and have been delivered
        # We use a date range to account for orders placed at different times of the day
        start_date = feedback_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = feedback_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Get orders that were placed in the date range and have been delivered
        orders = Order.objects.filter(
            created_at__range=(start_date, end_date),
            status='delivered'
        )

        # Schedule a feedback email for each order
        for order in orders:
            send_feedback_request_email.delay(str(order.id))

        logger.info(f"Scheduled {orders.count()} feedback emails")
        return True
    except Exception as e:
        logger.error(f"Error scheduling feedback emails: {str(e)}")
        return False


@shared_task
def send_feedback_request_email(order_id):
    """
    Send a feedback request email to the customer.

    Args:
        order_id: The ID of the order
    """
    try:
        # Get the order
        order = Order.objects.get(id=order_id)

        # Get the email template
        template = EmailTemplate.objects.filter(
            template_type='feedback_request',
            is_active=True
        ).first()

        if not template:
            # Use a default template if none exists
            subject = f"How was your PickMeTrend order? We'd love your feedback!"
            text_content = f"""
            Dear {order.full_name},

            Thank you for your recent purchase from PickMeTrend!

            We hope you're enjoying your new items. We'd love to hear your feedback about your experience.

            Please take a moment to share your thoughts by clicking the link below:

            {settings.SITE_URL if hasattr(settings, 'SITE_URL') else 'https://pickmetrend.com'}/feedback/{order.id}

            Your feedback helps us improve our products and services.

            Thank you for choosing PickMeTrend!
            """
            html_content = None
        else:
            # Prepare context for template rendering
            context = {
                'order': order,
                'customer_name': order.full_name,
                'order_id': order.id,
                'feedback_url': f"{settings.SITE_URL if hasattr(settings, 'SITE_URL') else 'https://pickmetrend.com'}/feedback/{order.id}",
                'site_url': settings.SITE_URL if hasattr(settings, 'SITE_URL') else 'https://pickmetrend.com',
            }

            # Render the templates
            t_subject = Template(template.subject)
            t_text = Template(template.text_content)
            t_html = Template(template.html_content)

            c = Context(context)
            subject = t_subject.render(c)
            text_content = t_text.render(c)
            html_content = t_html.render(c)

        # Send the email
        from_email = settings.SUPPORT_EMAIL
        to_email = order.email

        if html_content:
            # Send HTML email
            msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
            msg.attach_alternative(html_content, "text/html")
            msg.send()
        else:
            # Send plain text email
            send_mail(subject, text_content, from_email, [to_email])

        logger.info(f"Feedback request email sent for order {order_id}")
        return True
    except Order.DoesNotExist:
        logger.error(f"Order {order_id} not found")
        return False
    except Exception as e:
        logger.error(f"Error sending feedback request email: {str(e)}")
        return False


@shared_task
def send_support_ticket_confirmation(ticket_id):
    """
    Send a confirmation email when a support ticket is created.

    Args:
        ticket_id: The ID of the support ticket
    """
    try:
        # Get the ticket
        ticket = SupportTicket.objects.get(id=ticket_id)

        # Get the email template
        template = EmailTemplate.objects.filter(
            template_type='support_ticket_confirmation',
            is_active=True
        ).first()

        if not template:
            # Use a default template if none exists
            subject = f"Support Ticket Received - {ticket.subject}"
            text_content = f"""
            Dear {ticket.name},

            Thank you for contacting PickMeTrend support. We have received your ticket regarding:

            "{ticket.subject}"

            Our team will review your inquiry and get back to you as soon as possible.

            Ticket ID: {ticket.id}

            Thank you for your patience.

            Best regards,
            PickMeTrend Support Team
            """
            html_content = None
        else:
            # Prepare context for template rendering
            context = {
                'ticket': ticket,
                'customer_name': ticket.name,
                'ticket_id': ticket.id,
                'ticket_subject': ticket.subject,
                'ticket_message': ticket.message,
                'site_url': settings.SITE_URL if hasattr(settings, 'SITE_URL') else 'https://pickmetrend.com',
            }

            # Render the templates
            t_subject = Template(template.subject)
            t_text = Template(template.text_content)
            t_html = Template(template.html_content)

            c = Context(context)
            subject = t_subject.render(c)
            text_content = t_text.render(c)
            html_content = t_html.render(c)

        # Send the email
        from_email = settings.SUPPORT_EMAIL
        to_email = ticket.email

        if html_content:
            # Send HTML email
            msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
            msg.attach_alternative(html_content, "text/html")
            msg.send()
        else:
            # Send plain text email
            send_mail(subject, text_content, from_email, [to_email])

        # Also send notification to admin
        admin_subject = f"New Support Ticket: {ticket.subject}"
        admin_message = f"""
        A new support ticket has been submitted:

        Ticket ID: {ticket.id}
        Customer: {ticket.name} ({ticket.email})
        Subject: {ticket.subject}

        Message:
        {ticket.message}

        Please log in to the admin panel to respond:
        {settings.SITE_URL if hasattr(settings, 'SITE_URL') else 'https://pickmetrend.com'}/admin/customer_communication/supportticket/{ticket.id}/change/
        """

        send_mail(
            admin_subject,
            admin_message,
            settings.SUPPORT_EMAIL,
            [settings.ADMIN_EMAIL],
            fail_silently=True
        )

        logger.info(f"Support ticket confirmation email sent for ticket {ticket_id}")
        return True
    except SupportTicket.DoesNotExist:
        logger.error(f"Support ticket {ticket_id} not found")
        return False
    except Exception as e:
        logger.error(f"Error sending support ticket confirmation email: {str(e)}")
        return False
