from django.contrib import admin
from .models import Contact

@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'subject', 'created_at', 'is_read']
    list_filter = ['is_read', 'created_at']
    search_fields = ['name', 'email', 'subject', 'message']
    readonly_fields = ['name', 'email', 'subject', 'message', 'created_at']

    def has_add_permission(self, request):
        return False  # Prevent adding contacts through admin

    def has_change_permission(self, request, obj=None):
        # Only allow changing is_read status
        return True

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return self.readonly_fields
        return []
