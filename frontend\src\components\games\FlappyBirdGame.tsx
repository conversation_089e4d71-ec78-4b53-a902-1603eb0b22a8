import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';
import { api } from '../../services/api';

interface GameStats {
  games_played: number;
  high_score: number;
  total_tokens_won: number;
  total_tokens_lost: number;
  average_score: number;
}

const FlappyBirdGame: React.FC = () => {
  const { user } = useAuth();
  const { wallet, fetchWallet } = useWallet();
  const [gameStats, setGameStats] = useState<GameStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [gameSessionId, setGameSessionId] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    loadGameStats();
    // Initialize wallet data
    fetchWallet().then(() => {
      console.log('Wallet loaded successfully');
    }).catch(error => {
      console.error('Error loading wallet:', error);
    });
    
    // Listen for messages from the game iframe
    const handleMessage = (event: MessageEvent) => {
      // Wrap everything in try-catch to prevent any runtime errors
      try {
        // Validate event object
        if (!event || !event.data) {
          return;
        }

        // Allow messages from Django backend (different origin)
        if (event.origin !== window.location.origin &&
            event.origin !== process.env.REACT_APP_API_URL) {
          return;
        }

        console.log('Received message from iframe:', event.data);

        // Safely handle the message data with multiple layers of protection
        let messageData: any = null;
        let messageType: string = '';

        try {
          messageData = event.data;
          if (messageData && typeof messageData === 'object') {
            messageType = String(messageData.type || '');
          }
        } catch (parseError) {
          console.error('Error parsing message data:', parseError);
          return;
        }

        if (!messageType) {
          console.warn('No message type found in:', messageData);
          return;
        }

        console.log('Processing message type:', messageType);

        // Handle each message type with complete safety
        if (messageType === 'requestAuth') {
          console.log('Handling auth request');
          try {
            const token = localStorage.getItem('access_token');
            if (token && iframeRef.current && iframeRef.current.contentWindow) {
              iframeRef.current.contentWindow.postMessage({
                type: 'authToken',
                token: token
              }, '*');
            } else {
              if (iframeRef.current && iframeRef.current.contentWindow) {
                iframeRef.current.contentWindow.postMessage({
                  type: 'authToken',
                  token: null,
                  error: 'No authentication token available'
                }, '*');
              }
            }
          } catch (authError) {
            console.error('Error handling auth request:', authError);
          }
        } else if (messageType === 'gameStart') {
          console.log('Handling game start');
          try {
            handleGameStart();
          } catch (startError) {
            console.error('Error handling game start:', startError);
          }
        } else if (messageType === 'gameEnd') {
          console.log('Handling game end with data:', messageData);
          try {
            const gameScore = messageData && typeof messageData.score === 'number' ? messageData.score : undefined;
            const gamePassedPipes = messageData && typeof messageData.pipes_passed === 'number' ? messageData.pipes_passed : undefined;
            if (gameScore !== undefined && gamePassedPipes !== undefined) {
              handleGameEnd(gameScore, gamePassedPipes);
            }
          } catch (endError) {
            console.error('Error handling game end:', endError);
          }
        } else if (messageType === 'gameForfeit') {
          console.log('Handling game forfeit');
          try {
            handleGameForfeit();
          } catch (forfeitError) {
            console.error('Error handling game forfeit:', forfeitError);
          }
        } else if (messageType === 'tokenUpdate') {
          console.log('Handling token update with data:', messageData);
          try {
            const tokenBalance = messageData && typeof messageData.balance === 'number' ? messageData.balance : null;
            if (tokenBalance !== null && tokenBalance !== undefined) {
              console.log('Token balance updated from game:', tokenBalance);
              fetchWallet();
            } else {
              console.warn('Token update message missing valid balance:', messageData);
            }
          } catch (tokenError) {
            console.error('Error handling token update:', tokenError);
          }
        } else {
          console.log('Unknown message type:', messageType, 'Full data:', messageData);
        }
      } catch (error) {
        console.error('Critical error in message handler:', error);
        if (error instanceof Error) {
          console.error('Error message:', error.message);
          console.error('Error stack:', error.stack);
        }
        console.error('Event that caused error:', event);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const loadGameStats = async () => {
    try {
      const response = await api.get('/api/gaming/flappy-bird/stats/');
      setGameStats(response.data);
    } catch (err: any) {
      console.error('Failed to load game stats:', err);
      setError('Failed to load game statistics');
    }
  };

  const handleGameStart = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.post('/api/gaming/flappy-bird/start/');
      setGameSessionId(response.data.session_id);
      
      // Refresh wallet balance
      await fetchWallet();
    } catch (err: any) {
      console.error('Failed to start game:', err);
      setError(err.response?.data?.error || 'Failed to start game');
    } finally {
      setLoading(false);
    }
  };

  const handleGameEnd = async (score: number, pipes_passed: number) => {
    if (!gameSessionId) return;
    
    try {
      setLoading(true);
      
      const response = await api.post('/api/gaming/flappy-bird/submit-score/', {
        session_id: gameSessionId,
        score: score,
        pipes_passed: pipes_passed
      });
      
      // Refresh wallet balance and stats
      await Promise.all([fetchWallet(), loadGameStats()]);
      
      setGameSessionId(null);
    } catch (err: any) {
      console.error('Failed to submit score:', err);
      setError(err.response?.data?.error || 'Failed to submit score');
    } finally {
      setLoading(false);
    }
  };

  const handleGameForfeit = async () => {
    if (!gameSessionId) return;
    
    try {
      setLoading(true);
      
      await api.post('/api/gaming/flappy-bird/forfeit/', {
        session_id: gameSessionId
      });
      
      // Refresh wallet balance and stats
      await Promise.all([fetchWallet(), loadGameStats()]);
      
      setGameSessionId(null);
    } catch (err: any) {
      console.error('Failed to forfeit game:', err);
      setError(err.response?.data?.error || 'Failed to forfeit game');
    } finally {
      setLoading(false);
    }
  };

  const sendMessageToGame = (message: any) => {
    if (iframeRef.current?.contentWindow) {
      iframeRef.current.contentWindow.postMessage(message, window.location.origin);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-4xl font-bold text-white mb-2">🐦 Flappy Bird</h1>
          <p className="text-white/80">Navigate through pipes and earn tokens!</p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Game Area */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg p-4">
              <div className="relative">
                <iframe
                  ref={iframeRef}
                  src={`${process.env.REACT_APP_API_URL}/static/games/flappy-bird.html`}
                  width="100%"
                  height="600"
                  frameBorder="0"
                  className="rounded-lg"
                  title="Flappy Bird Game"
                />
                {loading && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
                    <div className="bg-white p-4 rounded-lg">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="mt-2 text-sm text-gray-600">Processing...</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Wallet Balance */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">💰 Your Wallet</h3>
              <div className="text-center">
                {wallet && typeof wallet.balance === 'number' ? (
                  <>
                    <div className="text-3xl font-bold text-green-600">
                      {wallet.balance}
                    </div>
                    <div className="text-sm text-gray-600">Available Tokens</div>
                  </>
                ) : (
                  <div className="text-lg text-gray-500">Loading...</div>
                )}
              </div>
            </div>

            {/* Game Rules */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">🎮 How to Play</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Click or tap to make the bird flap</li>
                <li>• Navigate through pipe gaps</li>
                <li>• Avoid hitting pipes or ground</li>
                <li>• Each pipe passed = 1 point</li>
              </ul>
            </div>

            {/* Token System */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">🪙 Token System</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• <span className="text-red-600">-2 tokens</span> to start playing</li>
                <li>• <span className="text-green-600">+5 tokens</span> if you pass 10+ pipes</li>
                <li>• <span className="text-red-600">-1 token</span> if you lose before 5 pipes</li>
                <li>• <span className="text-red-600">-2 tokens</span> if you quit early</li>
              </ul>
            </div>

            {/* Game Stats */}
            {gameStats && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-800">📊 Your Stats</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Games Played:</span>
                    <span className="font-semibold">{gameStats.games_played || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">High Score:</span>
                    <span className="font-semibold text-yellow-600">{gameStats.high_score || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average Score:</span>
                    <span className="font-semibold">{(gameStats.average_score || 0).toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tokens Won:</span>
                    <span className="font-semibold text-green-600">+{gameStats.total_tokens_won || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tokens Lost:</span>
                    <span className="font-semibold text-red-600">-{gameStats.total_tokens_lost || 0}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlappyBirdGame;
