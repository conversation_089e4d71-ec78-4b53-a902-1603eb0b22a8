# Generated manually to fix <PERSON><PERSON><PERSON><PERSON> length issues
# This migration increases the max_length of variant_id and printify_order_id fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0007_remove_order_final_amount_and_more'),
    ]

    operations = [
        # Increase variant_id field length in CartItem model
        migrations.AlterField(
            model_name='cartitem',
            name='variant_id',
            field=models.Char<PERSON>ield(blank=True, help_text='Printify variant ID', max_length=255, null=True),
        ),
        
        # Increase variant_id field length in OrderItem model
        migrations.AlterField(
            model_name='orderitem',
            name='variant_id',
            field=models.Char<PERSON>ield(blank=True, help_text='Printify variant ID', max_length=255, null=True),
        ),
        
        # Increase printify_order_id field length in OrderItem model
        migrations.AlterField(
            model_name='orderitem',
            name='printify_order_id',
            field=models.CharField(blank=True, help_text='Printify order ID', max_length=255, null=True),
        ),
    ]
