import { api } from './api';

export interface TicTacToeGameStart {
  success: boolean;
  game_id: string;
  difficulty: string;
  can_play: boolean;
  balance: number;
  message: string;
  error?: string;
}

export interface TicTacToeGameComplete {
  success: boolean;
  tokens_earned: number;
  new_balance: number;
  balance_in_inr: number;
  transaction_type: string;
  description: string;
  can_play_more: boolean;
  error?: string;
}

export interface TicTacToeStats {
  success: boolean;
  stats: {
    total_games: number;
    wins: number;
    losses: number;
    draws: number;
    total_tokens_earned: number;
    current_balance: number;
    balance_in_inr: number;
  };
  recent_games: Array<{
    date: string;
    result: 'win' | 'loss' | 'draw';
    tokens_earned: number;
    description: string;
  }>;
  error?: string;
}

export const ticTacToeService = {
  /**
   * Start a new Tic Tac Toe game (always hard mode)
   */
  startGame: async (): Promise<TicTacToeGameStart> => {
    try {
      const response = await api.post('/api/gaming/tic-tac-toe/start/', {
        difficulty: 'hard'
      });
      return response.data;
    } catch (error: any) {
      console.error('Error starting Tic Tac Toe game:', error);
      return {
        success: false,
        game_id: '',
        difficulty: 'hard',
        can_play: false,
        balance: 0,
        message: '',
        error: error.response?.data?.error || 'Failed to start game'
      };
    }
  },

  /**
   * Complete a Tic Tac Toe game and handle token transactions (always hard mode)
   */
  completeGame: async (
    gameId: string,
    result: 'win' | 'loss' | 'draw'
  ): Promise<TicTacToeGameComplete> => {
    try {
      const response = await api.post('/api/gaming/tic-tac-toe/complete/', {
        game_id: gameId,
        result,
        difficulty: 'hard'
      });
      return response.data;
    } catch (error: any) {
      console.error('Error completing Tic Tac Toe game:', error);
      return {
        success: false,
        tokens_earned: 0,
        new_balance: 0,
        balance_in_inr: 0,
        transaction_type: '',
        description: '',
        can_play_more: false,
        error: error.response?.data?.error || 'Failed to complete game'
      };
    }
  },

  /**
   * Get user's Tic Tac Toe game statistics
   */
  getStats: async (): Promise<TicTacToeStats> => {
    try {
      const response = await api.get('/api/gaming/tic-tac-toe/stats/');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching Tic Tac Toe stats:', error);
      return {
        success: false,
        stats: {
          total_games: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          total_tokens_earned: 0,
          current_balance: 0,
          balance_in_inr: 0
        },
        recent_games: [],
        error: error.response?.data?.error || 'Failed to fetch stats'
      };
    }
  }
};
