from django.db import models
from django.utils.text import slugify
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid
import os
from .fields import URLOrFileField

def validate_image_file(value):
    """
    Validate that uploaded file is an image with acceptable format
    """
    # Skip validation for None values
    if value is None:
        return value

    # Skip validation for string values (URLs)
    if isinstance(value, str):
        return value

    # Skip validation if value doesn't have a name attribute (could be a placeholder)
    if not hasattr(value, 'name'):
        return value

    ext = os.path.splitext(value.name)[1]  # Get file extension
    valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']

    if not ext.lower() in valid_extensions:
        raise ValidationError(f'Unsupported file format. Supported formats are: {", ".join(valid_extensions)}')

    # Check file size (max 5 MB)
    if hasattr(value, 'size') and value.size > 5 * 1024 * 1024:
        raise ValidationError('Image file size cannot exceed 5 MB.')

    return value


# This function is no longer needed as we're using URLOrFileField


class Category(models.Model):
    """
    Category model for products
    """
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True, max_length=100)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(upload_to='categories/', blank=True, null=True, validators=[validate_image_file])
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='children')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = 'Categories'
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


class Product(models.Model):
    """
    Product model
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    slug = models.SlugField(unique=True, max_length=200)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    compare_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    stock = models.IntegerField(default=0)
    categories = models.ManyToManyField(Category, related_name='products', blank=True)
    is_featured = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Printify integration fields
    printify_id = models.CharField(max_length=255, blank=True, null=True, db_index=True,
                                  help_text="Printify product ID for synced products")
    variants_json = models.JSONField(blank=True, null=True,
                                    help_text="JSON data for product variants from Printify")

    # Description cleaning field
    cleaned = models.BooleanField(default=False,
                                 help_text="Indicates if the product description has been cleaned")

    # Image sync tracking field
    last_image_sync = models.DateTimeField(blank=True, null=True,
                                          help_text="Timestamp of the last image sync from Printify")

    # Token discount fields
    allow_token_discount = models.BooleanField(
        default=False,
        help_text="Allow customers to use tokens for partial payment on this product"
    )
    token_discount_percentage = models.PositiveIntegerField(
        default=20,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        help_text="Maximum percentage of product price that can be paid with tokens (1-100%)"
    )
    token_discount_max_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum INR amount that can be paid with tokens (optional cap)"
    )

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)

        # Ensure compare_price is None if it's empty or 0
        if self.compare_price is not None and float(self.compare_price) == 0:
            self.compare_price = None

        # Truncate printify_id if it's too long
        if self.printify_id and len(self.printify_id) > 255:
            self.printify_id = self.printify_id[:255]

        super().save(*args, **kwargs)

    @property
    def discount_percentage(self):
        if self.compare_price and self.price < self.compare_price:
            return int(((self.compare_price - self.price) / self.compare_price) * 100)
        return 0

    @property
    def main_image(self):
        if self.images.exists():
            first_image = self.images.first()
            # If image_url exists, return that
            if first_image.image_url:
                return first_image.image_url
            # Otherwise, try to get the local file URL
            elif first_image.image:
                try:
                    return first_image.image.url
                except Exception as e:
                    print(f"Error getting image URL: {str(e)}")
                    # If the image is a string and looks like a URL, return it directly
                    if isinstance(first_image.image, str) and first_image.image.startswith(('http://', 'https://')):
                        return first_image.image
                    return None
        return None

    @property
    def token_discount_available(self):
        """Check if token discount is available for this product"""
        return self.allow_token_discount and self.is_active

    def calculate_max_token_discount(self, quantity=1):
        """Calculate maximum token discount for given quantity"""
        if not self.token_discount_available:
            return {
                'max_tokens': 0,
                'max_inr_discount': 0,
                'discount_percentage': 0
            }

        from django.conf import settings
        from decimal import Decimal

        token_rate = Decimal(str(settings.WALLET_SETTINGS.get('TOKEN_TO_INR_RATE', 0.1)))

        # Calculate percentage-based discount
        total_price = self.price * quantity
        percentage_discount = total_price * (Decimal(str(self.token_discount_percentage)) / Decimal('100'))

        # Apply max amount cap if set
        if self.token_discount_max_amount:
            percentage_discount = min(percentage_discount, self.token_discount_max_amount * quantity)

        # Convert to tokens
        max_tokens = int(percentage_discount / token_rate)

        return {
            'max_tokens': max_tokens,
            'max_inr_discount': percentage_discount,
            'discount_percentage': self.token_discount_percentage
        }


class ProductImage(models.Model):
    """
    Product image model
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/', blank=True, null=True)
    image_url = models.URLField(max_length=500, blank=True, null=True, help_text="URL for the image (used for display only)")
    alt_text = models.CharField(max_length=255, blank=True, null=True)
    is_primary = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-is_primary', 'created_at']

    def __str__(self):
        return f"Image for {self.product.name}"

    @property
    def image_display_url(self):
        """Return the URL to display for this image - safely handles missing files"""
        try:
            # First try image_url field (for external URLs)
            if self.image_url:
                return self.image_url

            # Then try local image file
            elif self.image:
                # Check if file exists before trying to get URL
                if hasattr(self.image, 'path'):
                    import os
                    if os.path.exists(self.image.path):
                        return self.image.url
                    else:
                        # File doesn't exist, return None
                        return None
                else:
                    # No path attribute, try to get URL anyway
                    return self.image.url

            return None
        except Exception:
            # If any error occurs, return None safely
            return None

    def file_exists(self):
        """
        Check if the image file actually exists on disk
        """
        try:
            if self.image and hasattr(self.image, 'path'):
                import os
                return os.path.exists(self.image.path)
            return False
        except Exception:
            return False




class ProductVariant(models.Model):
    """
    Product variant model for storing different sizes, colors, and prices
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')
    variant_id = models.CharField(max_length=255, help_text="Variant ID from Printify (SKU)")
    title = models.CharField(max_length=255, help_text="Variant title (e.g. 'Black / XL')")
    color = models.CharField(max_length=255, blank=True, null=True)
    size = models.CharField(max_length=255, blank=True, null=True)
    gender = models.CharField(max_length=50, blank=True, null=True,
                             help_text="Gender targeting (Unisex, Men, Women, Kids)")
    price = models.DecimalField(max_digits=10, decimal_places=2)
    is_available = models.BooleanField(default=True, help_text="Whether this variant is in stock")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['title']
        unique_together = ['product', 'variant_id']

    def clean(self):
        """
        Validate and clean the model fields to prevent database errors
        """
        from django.core.exceptions import ValidationError

        # Truncate fields that might be too long
        if self.variant_id and len(self.variant_id) > 255:
            self.variant_id = self.variant_id[:255]

        if self.title and len(self.title) > 255:
            self.title = self.title[:255]

        if self.color and len(self.color) > 255:
            self.color = self.color[:255]

        if self.size and len(self.size) > 255:
            self.size = self.size[:255]

        if self.gender and len(self.gender) > 50:
            self.gender = self.gender[:50]

    def save(self, *args, **kwargs):
        """
        Override save to ensure clean is called
        """
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.product.name} - {self.title}"


class Review(models.Model):
    """
    Product review model
    """
    RATING_CHOICES = [(i, i) for i in range(1, 6)]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    rating = models.IntegerField(choices=RATING_CHOICES)
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        unique_together = ['product', 'user']

    def __str__(self):
        return f"Review by {self.user.username} for {self.product.name}"
