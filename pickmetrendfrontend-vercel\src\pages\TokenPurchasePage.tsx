import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useWallet } from '../hooks/useWallet';
import { tokenPurchaseService, TokenPack } from '../services/tokenPurchaseService';

declare global {
  interface Window {
    Razorpay: any;
  }
}

const TokenPurchasePage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { wallet, loading: walletLoading, error: walletError, refreshWallet } = useWallet();
  const navigate = useNavigate();

  const [tokenPacks, setTokenPacks] = useState<TokenPack[]>([]);
  const [selectedPack, setSelectedPack] = useState<TokenPack | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'select' | 'confirm' | 'processing' | 'success'>('select');
  const [purchaseResult, setPurchaseResult] = useState<any>(null);

  // Load token packs on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadTokenPacks();
    }
  }, [isAuthenticated]);

  const loadTokenPacks = async () => {
    try {
      setLoading(true);
      setError(null);

      // For testing, use mock data if API fails
      const mockPacks: TokenPack[] = [
        {
          id: '1',
          name: 'Starter Pack',
          tokens: 100,
          price_inr: 10,
          tokens_per_rupee: 10,
          is_popular: false
        },
        {
          id: '2', 
          name: 'Popular Pack',
          tokens: 500,
          price_inr: 45,
          tokens_per_rupee: 11.1,
          is_popular: true
        },
        {
          id: '3',
          name: 'Best Value Pack', 
          tokens: 1000,
          price_inr: 80,
          tokens_per_rupee: 12.5,
          is_popular: false,
          savings_text: 'Best Value!'
        }
      ];

      try {
        const response = await tokenPurchaseService.getTokenPacks();
        if (response.success && response.token_packs) {
          setTokenPacks(response.token_packs);
        } else {
          console.log('API failed, using mock data');
          setTokenPacks(mockPacks);
        }
      } catch (err) {
        console.log('API error, using mock data');
        setTokenPacks(mockPacks);
      }

    } catch (err) {
      console.error('Error loading token packs:', err);
      setError('Failed to load token packs');
    } finally {
      setLoading(false);
    }
  };

  const handlePackSelect = (pack: TokenPack) => {
    setSelectedPack(pack);
    setStep('confirm');
  };

  const handleConfirmPurchase = async () => {
    if (!selectedPack) return;

    try {
      setStep('processing');
      setError(null);

      // Check if we should use test mode or real Razorpay
      const isTestMode = process.env.REACT_APP_PAYMENT_TEST_MODE === 'true' || false;

      if (isTestMode) {
        console.log('🧪 Test mode enabled - simulating successful purchase');
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Simulate successful purchase
        setPurchaseResult({
          tokens_purchased: selectedPack.tokens,
          amount_paid: selectedPack.price_inr,
          new_balance: (wallet?.balance || 0) + selectedPack.tokens
        });

        // Refresh wallet to update balance (in real app)
        refreshWallet();

        setStep('success');
        return;
      }

      console.log('💳 Real payment mode - connecting to Razorpay');
      console.log('📦 Selected pack:', selectedPack);

      // Real Razorpay integration
      console.log('🔄 Creating order with backend...');
      const orderResponse = await tokenPurchaseService.createOrder(selectedPack.id);
      console.log('📋 Order response:', orderResponse);

      if (!orderResponse.success) {
        console.error('❌ Order creation failed:', orderResponse.error);
        throw new Error(orderResponse.error || 'Failed to create order');
      }

      const { purchase_id, razorpay_order, token_pack } = orderResponse;
      console.log('🆔 Purchase ID:', purchase_id);
      console.log('💳 Razorpay Order:', razorpay_order);
      console.log('🪙 Token Pack:', token_pack);

      if (!purchase_id || !razorpay_order || !token_pack) {
        console.error('❌ Missing required data in order response');
        throw new Error('Invalid order response data');
      }

      // Load Razorpay script if not already loaded
      if (!window.Razorpay) {
        console.log('📜 Loading Razorpay script...');
        await loadRazorpayScript();
        console.log('✅ Razorpay script loaded');
      } else {
        console.log('✅ Razorpay script already loaded');
      }

      // Configure Razorpay options
      const options = {
        key: razorpay_order.key_id,
        amount: razorpay_order.amount,
        currency: razorpay_order.currency,
        name: 'PickMeTrend',
        description: `Purchase ${token_pack.tokens} tokens`,
        order_id: razorpay_order.order_id,
        prefill: {
          name: user?.username || 'User',
          email: user?.email || '<EMAIL>'
        },
        theme: {
          color: '#10b981'
        },
        handler: async (response: any) => {
          console.log('✅ Payment successful, verifying...', response);
          await verifyPayment(purchase_id, response);
        },
        modal: {
          ondismiss: () => {
            console.log('❌ Payment cancelled by user');
            setStep('confirm');
            setError('Payment cancelled');
          }
        }
      };

      console.log('🚀 Opening Razorpay with options:', {
        ...options,
        handler: '[Function]',
        modal: '[Object]'
      });

      // Open Razorpay
      const razorpay = new window.Razorpay(options);
      razorpay.open();
      console.log('💳 Razorpay payment dialog opened');

    } catch (err: any) {
      console.error('Error initiating purchase:', err);
      setError(err.message || 'Failed to initiate purchase');
      setStep('confirm');
    }
  };

  const verifyPayment = async (purchaseId: string, paymentResponse: any) => {
    try {
      const verifyResponse = await tokenPurchaseService.verifyPayment(
        purchaseId,
        paymentResponse.razorpay_payment_id,
        paymentResponse.razorpay_order_id,
        paymentResponse.razorpay_signature
      );

      if (verifyResponse.success && verifyResponse.tokens_purchased) {
        setPurchaseResult(verifyResponse);
        refreshWallet();
        setStep('success');
      } else {
        throw new Error(verifyResponse.error || 'Payment verification failed');
      }
    } catch (err: any) {
      console.error('Error verifying payment:', err);
      setError(err.message || 'Payment verification failed');
      setStep('confirm');
    }
  };

  const loadRazorpayScript = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Razorpay script'));
      document.body.appendChild(script);
    });
  };

  const handleBackToSelect = () => {
    setStep('select');
    setSelectedPack(null);
    setError(null);
  };

  const handleBackToWallet = () => {
    navigate('/wallet');
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <span className="text-6xl mb-4 block">🔐</span>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Login Required</h1>
            <p className="text-gray-600 mb-6">Please log in to purchase tokens</p>
            <Link
              to="/login"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 transition-all duration-200"
            >
              <span className="mr-2">🚀</span>
              Login to Continue
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (walletLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading wallet...</p>
        </div>
      </div>
    );
  }

  if (walletError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <span className="text-6xl mb-4 block">❌</span>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Error</h1>
            <p className="text-gray-600 mb-6">{walletError}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Hero Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-700">
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-64 h-64 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>

        <div className="relative container mx-auto px-4 py-12">
          <div className="text-center text-white">
            <div className="flex items-center justify-center mb-4">
              <span className="text-5xl mr-4">💰</span>
              <div>
                <h1 className="text-4xl font-bold mb-2">Buy Tokens</h1>
                <p className="text-xl text-emerald-100">Power up your gaming experience!</p>
              </div>
            </div>
            
            {wallet && (
              <div className="mt-6 bg-white bg-opacity-20 rounded-xl p-4 inline-block">
                <div className="flex items-center justify-center">
                  <span className="text-2xl mr-2">🪙</span>
                  <span className="text-xl font-semibold">Current Balance: {wallet.balance} tokens</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-8">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/wallet" className="text-emerald-600 hover:text-emerald-700">Wallet</Link>
            <span className="text-gray-400">›</span>
            <span className="text-gray-600">Buy Tokens</span>
          </nav>
        </div>

        {/* Payment Mode Indicator (Development Only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className={`mb-6 p-4 rounded-xl border ${
            process.env.REACT_APP_PAYMENT_TEST_MODE === 'true'
              ? 'bg-yellow-50 border-yellow-200'
              : 'bg-green-50 border-green-200'
          }`}>
            <div className={`flex items-center ${
              process.env.REACT_APP_PAYMENT_TEST_MODE === 'true'
                ? 'text-yellow-700'
                : 'text-green-700'
            }`}>
              <span className="mr-2">
                {process.env.REACT_APP_PAYMENT_TEST_MODE === 'true' ? '🧪' : '💳'}
              </span>
              <span className="font-semibold">
                {process.env.REACT_APP_PAYMENT_TEST_MODE === 'true'
                  ? 'Test Mode: No real payments will be processed'
                  : 'Live Mode: Real Razorpay payments enabled'}
              </span>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
            <div className="flex items-center text-red-700">
              <span className="mr-2">⚠️</span>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Content based on current step */}
        {step === 'select' && (
          <div>
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Token Pack</h2>
              <p className="text-lg text-gray-600">Select the perfect pack for your gaming needs</p>
            </div>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                {tokenPacks.map((pack) => (
                  <div
                    key={pack.id}
                    onClick={() => handlePackSelect(pack)}
                    className={`relative cursor-pointer border-2 rounded-2xl p-8 transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
                      pack.is_popular
                        ? 'border-emerald-500 bg-emerald-50 shadow-xl'
                        : 'border-gray-200 bg-white hover:border-emerald-300 shadow-lg'
                    }`}
                  >
                    {pack.is_popular && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span className="bg-emerald-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                          🔥 POPULAR
                        </span>
                      </div>
                    )}
                    
                    {pack.savings_text && (
                      <div className="absolute -top-4 right-4">
                        <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                          {pack.savings_text}
                        </span>
                      </div>
                    )}

                    <div className="text-center">
                      <div className="text-6xl mb-4">🪙</div>
                      <h3 className="font-bold text-2xl text-gray-900 mb-3">{pack.name}</h3>
                      <div className="text-4xl font-bold text-emerald-600 mb-2">
                        {pack.tokens}
                      </div>
                      <div className="text-lg text-gray-600 mb-4">tokens</div>
                      <div className="text-3xl font-bold text-gray-900 mb-3">
                        ₹{pack.price_inr}
                      </div>
                      <div className="text-sm text-gray-600 mb-6">
                        {pack.tokens_per_rupee.toFixed(1)} tokens per ₹
                      </div>
                      
                      <button className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold py-3 px-6 rounded-xl hover:from-emerald-700 hover:to-teal-700 transition-all duration-200">
                        Select Pack
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {step === 'confirm' && selectedPack && (
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Confirm Your Purchase</h2>
              <p className="text-lg text-gray-600">Review your selection before proceeding</p>
            </div>

            <div className="bg-white rounded-2xl shadow-xl p-8">
              <div className="text-center mb-6">
                <div className="text-5xl mb-4">🪙</div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{selectedPack.name}</h3>
                {selectedPack.is_popular && (
                  <span className="inline-block bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-semibold">
                    Popular Choice
                  </span>
                )}
              </div>

              <div className="space-y-4 mb-8">
                <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                  <span className="text-gray-600">Tokens:</span>
                  <span className="font-bold text-xl text-emerald-600">{selectedPack.tokens} tokens</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                  <span className="text-gray-600">Price:</span>
                  <span className="font-bold text-xl">₹{selectedPack.price_inr}</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                  <span className="text-gray-600">Value:</span>
                  <span className="font-semibold">{selectedPack.tokens_per_rupee.toFixed(1)} tokens per ₹</span>
                </div>
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center p-4 bg-emerald-50 rounded-xl">
                    <span className="text-emerald-700 font-semibold">New Balance:</span>
                    <span className="font-bold text-xl text-emerald-600">
                      {(wallet?.balance || 0) + selectedPack.tokens} tokens
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <button
                  onClick={handleBackToSelect}
                  className="flex-1 px-6 py-3 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200"
                >
                  ← Back to Packs
                </button>
                <button
                  onClick={handleConfirmPurchase}
                  disabled={loading}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 disabled:opacity-50 transition-all duration-200"
                >
                  {loading ? '🔄 Processing...' : '💳 Proceed to Payment'}
                </button>
              </div>
            </div>
          </div>
        )}

        {step === 'processing' && (
          <div className="max-w-md mx-auto text-center">
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-emerald-600 mx-auto mb-6"></div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Processing Payment</h2>
              <p className="text-gray-600 mb-4">Please wait while we process your payment...</p>
              <div className="text-sm text-gray-500">This may take a few moments</div>
            </div>
          </div>
        )}

        {step === 'success' && purchaseResult && (
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <div className="text-6xl mb-6">🎉</div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Purchase Successful!</h2>
              <p className="text-lg text-gray-600 mb-8">Your tokens have been added to your wallet</p>

              <div className="bg-emerald-50 rounded-xl p-6 mb-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-emerald-600">{purchaseResult.tokens_purchased}</div>
                    <div className="text-sm text-emerald-700">Tokens Added</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-emerald-600">₹{purchaseResult.amount_paid}</div>
                    <div className="text-sm text-emerald-700">Amount Paid</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-emerald-600">{purchaseResult.new_balance}</div>
                    <div className="text-sm text-emerald-700">New Balance</div>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <button
                  onClick={handleBackToWallet}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 transition-all duration-200"
                >
                  🪙 Back to Wallet
                </button>
                <Link
                  to="/game-dashboard"
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-200 text-center"
                >
                  🎮 Start Playing
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Benefits Section */}
        <div className="mt-16 bg-white rounded-2xl shadow-xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">Why Buy Tokens?</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl mb-4">🎮</div>
              <h4 className="font-bold text-lg text-gray-900 mb-2">Play More Games</h4>
              <p className="text-gray-600">Access all games and tournaments without interruption</p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-4">🛒</div>
              <h4 className="font-bold text-lg text-gray-900 mb-2">Shop Smart</h4>
              <p className="text-gray-600">Get exclusive discounts on trendy products</p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-4">🏆</div>
              <h4 className="font-bold text-lg text-gray-900 mb-2">Win Rewards</h4>
              <p className="text-gray-600">Participate in premium tournaments and win big</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TokenPurchasePage;
