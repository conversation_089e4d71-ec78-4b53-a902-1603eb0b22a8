import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';

interface SiteSettings {
  logo_url: string | null;
  favicon_url: string | null;
}

interface SiteSettingsContextType {
  settings: SiteSettings;
  loading: boolean;
  error: string | null;
}

const defaultSettings: SiteSettings = {
  logo_url: null,
  favicon_url: null
};

const SiteSettingsContext = createContext<SiteSettingsContextType>({
  settings: defaultSettings,
  loading: true,
  error: null
});

export const useSiteSettings = () => useContext(SiteSettingsContext);

interface SiteSettingsProviderProps {
  children: ReactNode;
}

export const SiteSettingsProvider: React.FC<SiteSettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<SiteSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
        const response = await axios.get(`${baseUrl}/site-settings/api/settings/`);
        setSettings(response.data);
        
        // Update favicon in the document head
        if (response.data.favicon_url) {
          const existingFavicon = document.querySelector('link[rel="icon"]');
          if (existingFavicon) {
            existingFavicon.setAttribute('href', response.data.favicon_url);
          } else {
            const link = document.createElement('link');
            link.rel = 'icon';
            link.href = response.data.favicon_url;
            document.head.appendChild(link);
          }
          
          // Also update apple-touch-icon
          const existingAppleIcon = document.querySelector('link[rel="apple-touch-icon"]');
          if (existingAppleIcon) {
            existingAppleIcon.setAttribute('href', response.data.favicon_url);
          }
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching site settings:', err);
        setError('Failed to load site settings');
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  return (
    <SiteSettingsContext.Provider value={{ settings, loading, error }}>
      {children}
    </SiteSettingsContext.Provider>
  );
};
