import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import Home from './pages/Home';
import Shop from './pages/Shop';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import Checkout from './pages/Checkout';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import GameDashboard from './pages/GameDashboard';
import WalletPage from './pages/WalletPage';
import Orders from './pages/Orders';
import OrderDetail from './pages/OrderDetail';
import OrderConfirmation from './pages/OrderConfirmation';
import OrderTracking from './pages/OrderTracking';
import ProfileEdit from './pages/ProfileEdit';
import About from './pages/About';
import Contact from './pages/Contact';
import FAQ from './pages/FAQ';
import Shipping from './pages/Shipping';
import Returns from './pages/Returns';
import Privacy from './pages/Privacy';
import Terms from './pages/Terms';
import NotFound from './pages/NotFound';
import RazorpayTest from './pages/RazorpayTest';
import LoginTest from './pages/LoginTest';
import PrintifyCatalog from './pages/PrintifyCatalog';
import FrontendDebug from './pages/FrontendDebug';
import { AuthProvider } from './contexts/AuthContext';
import { CartProvider } from './contexts/CartContext';
import { SiteSettingsProvider } from './contexts/SiteSettingsContext';
import PrivateRoute from './components/common/PrivateRoute';
import AdminRoute from './components/common/AdminRoute';
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminProducts from './pages/admin/AdminProducts';
import AdminOrders from './pages/admin/AdminOrders';
import AdminUsers from './pages/admin/AdminUsers';
import ColorMatchGame from './components/games/ColorMatchGame';
import MemoryCardGame from './components/games/MemoryCardGame';
import ModernTicTacToe from './components/games/ModernTicTacToe';
import ModernRockPaperScissors from './components/games/ModernRockPaperScissors';
import ModernNumberGuessing from './components/games/ModernNumberGuessing';
import FlappyBirdGame from './components/games/FlappyBirdGame';
import TicTacToeDebug from './components/games/TicTacToeDebug';

// Import debug utilities (only in development)
if (process.env.NODE_ENV === 'development') {
  import('./utils/debugUtils');
}

function App() {
  // Add development debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 PickMeTrend Frontend Debug Mode');
      console.log('API URL:', process.env.REACT_APP_API_URL);
      console.log('Environment:', process.env.NODE_ENV);
      console.log('Available debug functions: debugFrontend(), clearCache(), monitorImages(), testAPI(), testImage()');
    }
  }, []);

  return (
    <Router>
      <AuthProvider>
        <CartProvider>
          <SiteSettingsProvider>
          <div className="flex flex-col min-h-screen">
            <Header />
            <main className="flex-grow">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/shop" element={<Shop />} />
                <Route path="/product/:slug" element={<ProductDetail />} />
                <Route path="/cart" element={<Cart />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/about" element={<About />} />
                <Route path="/contact" element={<Contact />} />
                <Route path="/faq" element={<FAQ />} />
                <Route path="/shipping" element={<Shipping />} />
                <Route path="/returns" element={<Returns />} />
                <Route path="/privacy" element={<Privacy />} />
                <Route path="/terms" element={<Terms />} />
                <Route path="/razorpay-test" element={<RazorpayTest />} />
                <Route path="/login-test" element={<LoginTest />} />

                {/* Debug route (development only) */}
                {process.env.NODE_ENV === 'development' && (
                  <Route path="/debug" element={<FrontendDebug />} />
                )}

                {/* Protected Routes */}
                <Route path="/checkout" element={
                  <PrivateRoute>
                    <Checkout />
                  </PrivateRoute>
                } />
                <Route path="/dashboard" element={
                  <PrivateRoute>
                    <Dashboard />
                  </PrivateRoute>
                } />
                <Route path="/game-dashboard" element={
                  <PrivateRoute>
                    <GameDashboard />
                  </PrivateRoute>
                } />
                <Route path="/gaming/color-match" element={
                  <PrivateRoute>
                    <ColorMatchGame />
                  </PrivateRoute>
                } />
                <Route path="/gaming/memory-card" element={
                  <PrivateRoute>
                    <MemoryCardGame />
                  </PrivateRoute>
                } />
                <Route path="/gaming/tic-tac-toe" element={
                  <PrivateRoute>
                    <ModernTicTacToe />
                  </PrivateRoute>
                } />
                <Route path="/gaming/rock-paper-scissors" element={
                  <PrivateRoute>
                    <ModernRockPaperScissors />
                  </PrivateRoute>
                } />
                <Route path="/gaming/number-guessing" element={
                  <PrivateRoute>
                    <ModernNumberGuessing />
                  </PrivateRoute>
                } />
                <Route path="/gaming/flappy-bird" element={
                  <PrivateRoute>
                    <FlappyBirdGame />
                  </PrivateRoute>
                } />
                <Route path="/gaming/debug" element={
                  <PrivateRoute>
                    <TicTacToeDebug />
                  </PrivateRoute>
                } />
                <Route path="/wallet" element={
                  <PrivateRoute>
                    <WalletPage />
                  </PrivateRoute>
                } />
                <Route path="/orders" element={
                  <PrivateRoute>
                    <Orders />
                  </PrivateRoute>
                } />
                <Route path="/order/:id" element={
                  <PrivateRoute>
                    <OrderDetail />
                  </PrivateRoute>
                } />
                <Route path="/order/:id/tracking" element={
                  <PrivateRoute>
                    <OrderTracking />
                  </PrivateRoute>
                } />
                <Route path="/order-confirmation/:id" element={
                  <PrivateRoute>
                    <OrderConfirmation />
                  </PrivateRoute>
                } />
                <Route path="/printify-catalog" element={
                  <PrivateRoute>
                    <PrintifyCatalog />
                  </PrivateRoute>
                } />
                <Route path="/profile/edit" element={
                  <PrivateRoute>
                    <ProfileEdit />
                  </PrivateRoute>
                } />

                {/* Admin Routes */}
                <Route path="/admin" element={
                  <AdminRoute>
                    <AdminDashboard />
                  </AdminRoute>
                } />
                <Route path="/admin/products" element={
                  <AdminRoute>
                    <AdminProducts />
                  </AdminRoute>
                } />
                <Route path="/admin/orders" element={
                  <AdminRoute>
                    <AdminOrders />
                  </AdminRoute>
                } />
                <Route path="/admin/users" element={
                  <AdminRoute>
                    <AdminUsers />
                  </AdminRoute>
                } />

                {/* 404 Route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </main>
            <Footer />
          </div>
          </SiteSettingsProvider>
        </CartProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
