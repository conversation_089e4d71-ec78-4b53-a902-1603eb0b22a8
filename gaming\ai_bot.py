"""
AI Bot logic for different game types
"""
import random
from typing import Dict, Any, <PERSON>
from django.conf import settings


class AIBot:
    """
    AI Bot that can play different games
    """
    
    def __init__(self, difficulty: str = 'medium'):
        self.difficulty = difficulty.lower()
        if self.difficulty not in ['easy', 'medium', 'hard']:
            self.difficulty = 'medium'
    
    def make_move(self, game_type: str, game_state: Dict[str, Any]) -> Union[str, int]:
        """
        Make a move based on game type and current state
        """
        if game_type == 'rock_paper_scissors':
            return self._rock_paper_scissors_move(game_state)
        elif game_type == 'number_guessing':
            return self._number_guessing_move(game_state)
        elif game_type == 'tic_tac_toe':
            return self._tic_tac_toe_move(game_state)
        elif game_type == 'color_match':
            return self._color_match_move(game_state)
        elif game_type == 'memory_card':
            return self._memory_card_move(game_state)
        elif game_type == 'ludo':
            return self._ludo_move(game_state)
        else:
            raise ValueError(f"Unknown game type: {game_type}")
    
    def _rock_paper_scissors_move(self, game_state: Dict[str, Any]) -> str:
        """
        AI strategy for Rock Paper Scissors
        """
        choices = ['rock', 'paper', 'scissors']
        
        if self.difficulty == 'easy':
            # Easy: Completely random
            return random.choice(choices)
        
        elif self.difficulty == 'medium':
            # Medium: Slight pattern recognition
            round_results = game_state.get('round_results', [])
            
            if len(round_results) == 0:
                return random.choice(choices)
            
            # Look at player's last move and try to counter
            last_result = round_results[-1]
            player_last_move = last_result.get('player1_move')
            
            if player_last_move:
                # 70% chance to counter, 30% random
                if random.random() < 0.7:
                    counters = {
                        'rock': 'paper',
                        'paper': 'scissors',
                        'scissors': 'rock'
                    }
                    return counters.get(player_last_move, random.choice(choices))
            
            return random.choice(choices)
        
        elif self.difficulty == 'hard':
            # Hard: Advanced pattern recognition
            round_results = game_state.get('round_results', [])
            
            if len(round_results) < 2:
                return random.choice(choices)
            
            # Analyze player's patterns
            player_moves = [r.get('player1_move') for r in round_results]
            
            # Look for patterns in last 3 moves
            if len(player_moves) >= 3:
                last_three = player_moves[-3:]
                
                # Check for repetition
                if last_three[0] == last_three[1] == last_three[2]:
                    # Player is repeating, counter it
                    counters = {
                        'rock': 'paper',
                        'paper': 'scissors',
                        'scissors': 'rock'
                    }
                    return counters.get(last_three[0], random.choice(choices))
                
                # Check for alternating pattern
                if len(set(last_three)) == 2:
                    # Predict next in pattern
                    most_common = max(set(last_three), key=last_three.count)
                    counters = {
                        'rock': 'paper',
                        'paper': 'scissors',
                        'scissors': 'rock'
                    }
                    return counters.get(most_common, random.choice(choices))
            
            # Default to countering last move
            last_move = player_moves[-1]
            counters = {
                'rock': 'paper',
                'paper': 'scissors',
                'scissors': 'rock'
            }
            return counters.get(last_move, random.choice(choices))
    
    def _number_guessing_move(self, game_state: Dict[str, Any]) -> int:
        """
        AI strategy for Number Guessing
        """
        target = game_state.get('target_number', 50)
        round_results = game_state.get('round_results', [])
        
        if self.difficulty == 'easy':
            # Easy: Random guess with some bias toward center
            if random.random() < 0.3:
                return random.randint(40, 60)  # 30% chance for center bias
            return random.randint(1, 100)
        
        elif self.difficulty == 'medium':
            # Medium: Some strategy based on previous rounds
            if len(round_results) == 0:
                return random.randint(25, 75)  # Start with middle range
            
            # Look at player's guessing patterns
            player_guesses = [r.get('player1_guess') for r in round_results if r.get('player1_guess')]
            
            if player_guesses:
                avg_guess = sum(player_guesses) / len(player_guesses)
                
                # Try to guess close to player's average but with some randomness
                base_guess = int(avg_guess)
                variation = random.randint(-15, 15)
                guess = max(1, min(100, base_guess + variation))
                return guess
            
            return random.randint(1, 100)
        
        elif self.difficulty == 'hard':
            # Hard: Advanced strategy
            if len(round_results) == 0:
                return random.randint(35, 65)  # Start with optimal range
            
            # Analyze player patterns and target distributions
            player_guesses = [r.get('player1_guess') for r in round_results if r.get('player1_guess')]
            targets = [r.get('target_number') for r in round_results if r.get('target_number')]
            
            if len(player_guesses) >= 2:
                # Calculate player's tendency
                avg_guess = sum(player_guesses) / len(player_guesses)
                
                # Calculate how close player usually gets
                distances = []
                for i, result in enumerate(round_results):
                    if result.get('player1_guess') and result.get('target_number'):
                        dist = abs(result['target_number'] - result['player1_guess'])
                        distances.append(dist)
                
                if distances:
                    avg_distance = sum(distances) / len(distances)
                    
                    # Try to guess within the average distance of the target
                    # but use some prediction of where target might be
                    if len(targets) >= 2:
                        target_avg = sum(targets) / len(targets)
                        # Guess near the historical target average
                        base_guess = int(target_avg)
                    else:
                        base_guess = 50  # Default to middle
                    
                    # Add some strategic variation
                    variation = random.randint(-int(avg_distance), int(avg_distance))
                    guess = max(1, min(100, base_guess + variation))
                    return guess
            
            return random.randint(30, 70)

    def _tic_tac_toe_move(self, game_state: Dict[str, Any]) -> tuple:
        """
        AI strategy for Tic Tac Toe
        """
        board = game_state.get('board', [['', '', ''], ['', '', ''], ['', '', '']])
        ai_symbol = game_state.get('player2_symbol', 'O')
        player_symbol = game_state.get('player1_symbol', 'X')

        # Find available moves
        available_moves = []
        for i in range(3):
            for j in range(3):
                if board[i][j] == '':
                    available_moves.append((i, j))

        if not available_moves:
            return (0, 0)  # No valid moves

        if self.difficulty == 'easy':
            # Easy: Random move
            return random.choice(available_moves)

        elif self.difficulty == 'medium':
            # Medium: Block player wins, try to win

            # Check if AI can win
            for move in available_moves:
                test_board = [row[:] for row in board]
                test_board[move[0]][move[1]] = ai_symbol
                if self._check_tic_tac_toe_winner(test_board) == ai_symbol:
                    return move

            # Check if need to block player
            for move in available_moves:
                test_board = [row[:] for row in board]
                test_board[move[0]][move[1]] = player_symbol
                if self._check_tic_tac_toe_winner(test_board) == player_symbol:
                    return move

            # Take center if available
            if (1, 1) in available_moves:
                return (1, 1)

            # Take corners
            corners = [(0, 0), (0, 2), (2, 0), (2, 2)]
            corner_moves = [m for m in available_moves if m in corners]
            if corner_moves:
                return random.choice(corner_moves)

            return random.choice(available_moves)

        elif self.difficulty == 'hard':
            # Hard: Minimax algorithm (simplified)
            best_move = self._minimax_tic_tac_toe(board, ai_symbol, player_symbol, True, 0)[1]
            return best_move if best_move else random.choice(available_moves)

        return random.choice(available_moves)

    def _check_tic_tac_toe_winner(self, board):
        """Check if there's a winner in tic tac toe"""
        # Check rows
        for i in range(3):
            if board[i][0] == board[i][1] == board[i][2] != '':
                return board[i][0]

        # Check columns
        for j in range(3):
            if board[0][j] == board[1][j] == board[2][j] != '':
                return board[0][j]

        # Check diagonals
        if board[0][0] == board[1][1] == board[2][2] != '':
            return board[0][0]

        if board[0][2] == board[1][1] == board[2][0] != '':
            return board[0][2]

        return None

    def _minimax_tic_tac_toe(self, board, ai_symbol, player_symbol, is_maximizing, depth):
        """Minimax algorithm for tic tac toe"""
        winner = self._check_tic_tac_toe_winner(board)

        if winner == ai_symbol:
            return 10 - depth, None
        elif winner == player_symbol:
            return depth - 10, None
        elif self._is_tic_tac_toe_full(board):
            return 0, None

        if depth > 6:  # Limit depth for performance
            return 0, None

        if is_maximizing:
            best_score = float('-inf')
            best_move = None

            for i in range(3):
                for j in range(3):
                    if board[i][j] == '':
                        board[i][j] = ai_symbol
                        score, _ = self._minimax_tic_tac_toe(board, ai_symbol, player_symbol, False, depth + 1)
                        board[i][j] = ''

                        if score > best_score:
                            best_score = score
                            best_move = (i, j)

            return best_score, best_move
        else:
            best_score = float('inf')
            best_move = None

            for i in range(3):
                for j in range(3):
                    if board[i][j] == '':
                        board[i][j] = player_symbol
                        score, _ = self._minimax_tic_tac_toe(board, ai_symbol, player_symbol, True, depth + 1)
                        board[i][j] = ''

                        if score < best_score:
                            best_score = score
                            best_move = (i, j)

            return best_score, best_move

    def _is_tic_tac_toe_full(self, board):
        """Check if tic tac toe board is full"""
        for row in board:
            for cell in row:
                if cell == '':
                    return False
        return True

    def _color_match_move(self, game_state: Dict[str, Any]) -> str:
        """
        AI strategy for Color Match
        """
        colors = game_state.get('colors', ['red', 'blue', 'green', 'yellow', 'purple', 'orange'])
        sequence = game_state.get('sequence', [])

        if self.difficulty == 'easy':
            # Easy: Random color choice (often wrong)
            return random.choice(colors)

        elif self.difficulty == 'medium':
            # Medium: Sometimes remembers the sequence correctly
            if sequence and random.random() < 0.7:  # 70% chance to be correct
                ai_sequence = game_state.get('player2_sequence', [])
                if len(ai_sequence) < len(sequence):
                    return sequence[len(ai_sequence)]
            return random.choice(colors)

        elif self.difficulty == 'hard':
            # Hard: Usually remembers the sequence correctly
            if sequence and random.random() < 0.9:  # 90% chance to be correct
                ai_sequence = game_state.get('player2_sequence', [])
                if len(ai_sequence) < len(sequence):
                    return sequence[len(ai_sequence)]
            return random.choice(colors)

        return random.choice(colors)

    def _memory_card_move(self, game_state: Dict[str, Any]) -> int:
        """
        AI strategy for Memory Card Match
        """
        cards = game_state.get('cards', [])
        revealed = game_state.get('revealed', [])
        matched = game_state.get('matched', [])
        selected_cards = game_state.get('selected_cards', [])

        # Find available cards (not revealed and not matched)
        available_cards = []
        for i in range(len(cards)):
            if not revealed[i] and not matched[i]:
                available_cards.append(i)

        if not available_cards:
            return 0  # No valid moves

        if self.difficulty == 'easy':
            # Easy: Random card selection
            return random.choice(available_cards)

        elif self.difficulty == 'medium':
            # Medium: Sometimes remembers previously seen cards
            if len(selected_cards) == 1:
                # Second card selection - try to find a match
                first_card_idx = selected_cards[0]
                first_card = cards[first_card_idx]

                # 60% chance to remember and find correct match
                if random.random() < 0.6:
                    for i in available_cards:
                        if cards[i] == first_card:
                            return i

            return random.choice(available_cards)

        elif self.difficulty == 'hard':
            # Hard: Good memory, strategic play
            if len(selected_cards) == 1:
                # Second card selection - try to find a match
                first_card_idx = selected_cards[0]
                first_card = cards[first_card_idx]

                # 85% chance to remember and find correct match
                if random.random() < 0.85:
                    for i in available_cards:
                        if cards[i] == first_card:
                            return i

            # If no match found or first card, pick strategically
            # Prefer cards that haven't been revealed recently
            return random.choice(available_cards)

        return random.choice(available_cards)

    def _ludo_move(self, game_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        AI strategy for Ludo game
        """
        valid_moves = game_state.get('valid_moves', [])

        if not valid_moves:
            return None

        if self.difficulty == 'easy':
            # Easy: Random move selection
            return random.choice(valid_moves)

        elif self.difficulty == 'medium':
            # Medium: Prefer moves that advance tokens or exit home
            return self._select_ludo_move(valid_moves, game_state, 'medium')

        elif self.difficulty == 'hard':
            # Hard: Strategic move selection with capture priority
            return self._select_ludo_move(valid_moves, game_state, 'hard')

        return random.choice(valid_moves)

    def _select_ludo_move(self, valid_moves, game_state, difficulty):
        """
        Select best Ludo move based on strategy
        """
        if not valid_moves:
            return None

        scored_moves = []

        for move in valid_moves:
            score = 0

            # Priority 1: Exit home (always good)
            if move['type'] == 'exit_home':
                score += 10

            # Priority 2: Advance tokens
            if move['type'] == 'normal':
                distance = move['to'] - move['from']
                score += distance * 2

            # Priority 3: Capture opportunities (hard mode)
            if difficulty == 'hard':
                # Check if move can capture opponent token
                opponent_tokens = game_state.get('player1_tokens', {})
                for token_data in opponent_tokens.values():
                    if (not token_data.get('is_home', True) and
                        not token_data.get('is_finished', False) and
                        token_data.get('position') == move['to']):
                        score += 15  # High priority for captures

            # Priority 4: Avoid being captured (medium/hard)
            if difficulty in ['medium', 'hard']:
                # Lower score if move puts token in danger
                if move['to'] not in [1, 9, 14, 22, 27, 35, 40, 48]:  # Not safe positions
                    score -= 2

            scored_moves.append((move, score))

        # Sort by score and return best move
        scored_moves.sort(key=lambda x: x[1], reverse=True)
        return scored_moves[0][0]


def get_ai_bot(difficulty: str = None) -> AIBot:
    """
    Get an AI bot instance with specified difficulty
    """
    if difficulty is None:
        difficulty = settings.GAMING_SETTINGS.get('AI_BOT_DIFFICULTY', 'medium')
    
    return AIBot(difficulty)
