"""
Django management command to create default token packs
"""

from django.core.management.base import BaseCommand
from wallet.models import TokenPack


class Command(BaseCommand):
    help = 'Create default token packs for purchase'

    def handle(self, *args, **options):
        # Define default token packs
        default_packs = [
            {
                'name': 'Starter Pack',
                'tokens': 100,
                'price_inr': 10.00,
                'sort_order': 1
            },
            {
                'name': 'Popular Pack',
                'tokens': 500,
                'price_inr': 45.00,
                'sort_order': 2
            },
            {
                'name': 'Best Value Pack',
                'tokens': 1000,
                'price_inr': 80.00,
                'sort_order': 3
            }
        ]

        created_count = 0
        updated_count = 0

        for pack_data in default_packs:
            pack, created = TokenPack.objects.get_or_create(
                tokens=pack_data['tokens'],
                defaults=pack_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Created token pack: {pack.name} - {pack.tokens} tokens for ₹{pack.price_inr}'
                    )
                )
            else:
                # Update existing pack if needed
                updated = False
                for field, value in pack_data.items():
                    if getattr(pack, field) != value:
                        setattr(pack, field, value)
                        updated = True
                
                if updated:
                    pack.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(
                            f'Updated token pack: {pack.name} - {pack.tokens} tokens for ₹{pack.price_inr}'
                        )
                    )
                else:
                    self.stdout.write(
                        f'Token pack already exists: {pack.name} - {pack.tokens} tokens for ₹{pack.price_inr}'
                    )

        self.stdout.write(
            self.style.SUCCESS(
                f'\nSummary: {created_count} packs created, {updated_count} packs updated'
            )
        )
