/**
 * Image utility functions for handling product images
 */
import React from 'react';

export interface ImageDebugInfo {
  originalUrl: string;
  processedUrl: string;
  isValid: boolean;
  isExternal: boolean;
  isPrintify: boolean;
  error?: string;
}

/**
 * Process and validate image URLs
 * @param imageUrl - The image URL to process
 * @param fallbackUrl - Fallback URL if the image is invalid
 * @returns Processed image URL
 */
export const getImageUrl = (imageUrl: string, fallbackUrl?: string): string => {
  const defaultFallback = 'https://via.placeholder.com/400x400?text=No+Image';

  if (!imageUrl || imageUrl === 'null' || imageUrl === 'undefined') {
    console.warn('ImageUtils: Empty or invalid image URL provided:', imageUrl);
    return fallbackUrl || defaultFallback;
  }

  // If it's already a complete URL (including Printify URLs), return it
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a relative URL starting with /media, prepend the API base URL
  if (imageUrl.startsWith('/media/')) {
    const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
    // Remove trailing slash from baseUrl if present
    const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    return `${cleanBaseUrl}${imageUrl}`;
  }

  // If it's a relative path without leading slash, add /media/ prefix
  if (imageUrl.startsWith('products/') || imageUrl.startsWith('team/') || imageUrl.startsWith('site/')) {
    const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
    const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    return `${cleanBaseUrl}/media/${imageUrl}`;
  }

  // For other cases, return as is but log a warning
  console.warn('ImageUtils: Unhandled image URL format:', imageUrl);
  return imageUrl;
};

/**
 * Debug image URL and provide detailed information
 * @param imageUrl - The image URL to debug
 * @returns Debug information about the image URL
 */
export const debugImageUrl = (imageUrl: string): ImageDebugInfo => {
  const processedUrl = getImageUrl(imageUrl);

  const debugInfo: ImageDebugInfo = {
    originalUrl: imageUrl,
    processedUrl,
    isValid: Boolean(imageUrl),
    isExternal: imageUrl.startsWith('http'),
    isPrintify: imageUrl.includes('printify.com'),
  };

  if (!imageUrl) {
    debugInfo.error = 'Empty or null image URL';
    debugInfo.isValid = false;
  }

  return debugInfo;
};

/**
 * Test if an image URL is accessible with enhanced error handling
 * @param imageUrl - The image URL to test
 * @param timeout - Timeout in milliseconds (default: 15000)
 * @returns Promise that resolves to true if image is accessible
 */
export const testImageUrl = async (imageUrl: string, timeout: number = 15000): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    let timeoutId: NodeJS.Timeout;

    const cleanup = () => {
      if (timeoutId) clearTimeout(timeoutId);
      img.onload = null;
      img.onerror = null;
    };

    img.onload = () => {
      console.log(`✅ Image accessible: ${imageUrl.substring(0, 60)}...`);
      cleanup();
      resolve(true);
    };

    img.onerror = (error) => {
      console.error(`❌ Image not accessible: ${imageUrl.substring(0, 60)}...`, error);
      cleanup();
      resolve(false);
    };

    // Set a timeout for the test
    timeoutId = setTimeout(() => {
      console.warn(`⏰ Image test timeout (${timeout}ms): ${imageUrl.substring(0, 60)}...`);
      cleanup();
      resolve(false);
    }, timeout);

    // Add crossOrigin attribute for external images
    img.crossOrigin = 'anonymous';
    img.src = getImageUrl(imageUrl);
  });
};

/**
 * Batch test multiple image URLs
 * @param imageUrls - Array of image URLs to test
 * @returns Promise that resolves to an array of test results
 */
export const batchTestImages = async (imageUrls: string[]): Promise<Array<{url: string, accessible: boolean}>> => {
  const results = await Promise.all(
    imageUrls.map(async (url) => ({
      url,
      accessible: await testImageUrl(url)
    }))
  );

  const accessible = results.filter(r => r.accessible).length;
  const total = results.length;

  console.log(`📊 Image accessibility test: ${accessible}/${total} images accessible`);

  return results;
};

/**
 * Create an optimized image URL for different sizes
 * @param imageUrl - The original image URL
 * @param size - The desired size (small, medium, large)
 * @returns Optimized image URL
 */
export const getOptimizedImageUrl = (imageUrl: string, size: 'small' | 'medium' | 'large' = 'medium'): string => {
  const processedUrl = getImageUrl(imageUrl);

  // For Printify images, we can't optimize them as they're external
  // But we can add size hints for other optimization services
  if (processedUrl.includes('printify.com')) {
    return processedUrl;
  }

  // For local images, you could add optimization parameters here
  // This is a placeholder for future optimization logic
  return processedUrl;
};

/**
 * Handle image loading errors with detailed logging
 * @param error - The error event (can be native Event or React SyntheticEvent)
 * @param imageUrl - The original image URL
 * @param context - Additional context (e.g., component name)
 */
export const handleImageError = (
  error: Event | React.SyntheticEvent<HTMLImageElement, Event>,
  imageUrl: string,
  context: string = 'Unknown'
): void => {
  const debugInfo = debugImageUrl(imageUrl);

  console.error(`❌ Image load error in ${context}:`, {
    ...debugInfo,
    error: error.type || 'Image load error',
    timestamp: new Date().toISOString()
  });

  // You could send this to an error tracking service here
  // Example: errorTracker.captureException(error, { extra: debugInfo });
};

/**
 * Preload images for better user experience
 * @param imageUrls - Array of image URLs to preload
 */
export const preloadImages = (imageUrls: string[]): void => {
  imageUrls.forEach((url) => {
    const img = new Image();
    img.src = getImageUrl(url);

    img.onload = () => {
      console.log(`🚀 Preloaded: ${url}`);
    };

    img.onerror = () => {
      console.warn(`⚠️ Failed to preload: ${url}`);
    };
  });
};

/**
 * Get placeholder image URL based on context
 * @param context - The context where the placeholder is used
 * @param size - The size of the placeholder
 * @returns Placeholder image URL
 */
export const getPlaceholderUrl = (
  context: 'product' | 'avatar' | 'general' = 'general',
  size: string = '400x400'
): string => {
  const placeholders = {
    product: `https://via.placeholder.com/${size}?text=Product+Image`,
    avatar: `https://via.placeholder.com/${size}?text=Avatar`,
    general: `https://via.placeholder.com/${size}?text=No+Image`
  };

  return placeholders[context];
};

/**
 * Extract image metadata from URL
 * @param imageUrl - The image URL
 * @returns Image metadata
 */
export const getImageMetadata = (imageUrl: string) => {
  const url = new URL(getImageUrl(imageUrl));

  return {
    domain: url.hostname,
    path: url.pathname,
    filename: url.pathname.split('/').pop() || '',
    extension: url.pathname.split('.').pop() || '',
    isPrintify: url.hostname.includes('printify.com'),
    isLocal: url.hostname === 'localhost' || url.hostname.includes('127.0.0.1'),
    queryParams: Object.fromEntries(url.searchParams.entries())
  };
};
