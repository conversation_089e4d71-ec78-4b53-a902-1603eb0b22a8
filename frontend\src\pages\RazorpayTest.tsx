import React, { useState } from 'react';
import axios from 'axios';

declare global {
  interface Window {
    Razorpay: any;
  }
}

const RazorpayTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<string | null>(null);

  const handleTestPayment = async () => {
    setLoading(true);
    setError(null);
    setResult('Creating test order...');

    try {
      // Create a test order
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/razorpay/test/create_test_order/`,
        {
          amount: 50000, // 500 INR in paise
          currency: 'INR',
          receipt: `test_receipt_${Date.now()}`
        }
      );

      console.log('Order created:', response.data);
      setResult(`Order created with ID: ${response.data.id}`);

      // Configure Razorpay
      const options = {
        key: process.env.REACT_APP_RAZORPAY_KEY_ID || response.data.key_id,
        amount: response.data.amount,
        currency: response.data.currency,
        name: 'PickMeTrend',
        description: 'Test Payment',
        order_id: response.data.id,
        handler: function (response: any) {
          console.log('Payment successful:', response);
          setResult(`Payment successful!\nPayment ID: ${response.razorpay_payment_id}\nOrder ID: ${response.razorpay_order_id}`);
          verifyPayment(response);
        },
        prefill: {
          name: 'Test User',
          email: '<EMAIL>',
          contact: '9876543210'  // Using a generic test number instead of company number
        },
        theme: {
          color: '#3399cc'
        },
        modal: {
          ondismiss: function () {
            console.log('Payment modal dismissed');
            setResult('Payment cancelled by user');
          }
        }
      };

      console.log('Razorpay options:', options);

      // Open Razorpay checkout
      if (window.Razorpay) {
        const razorpay = new window.Razorpay(options);
        razorpay.on('payment.failed', function (response: any) {
          console.error('Payment failed:', response.error);
          setError(`Payment failed: ${response.error.description}`);
        });
        razorpay.open();
      } else {
        setError('Razorpay SDK not loaded');
      }
    } catch (err: any) {
      console.error('Error:', err);
      setError(err.response?.data?.error || err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const verifyPayment = async (paymentData: any) => {
    try {
      setResult('Verifying payment...');
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/razorpay/test/verify_test_payment/`,
        {
          razorpay_payment_id: paymentData.razorpay_payment_id,
          razorpay_order_id: paymentData.razorpay_order_id,
          razorpay_signature: paymentData.razorpay_signature
        }
      );

      console.log('Verification response:', response.data);
      setResult(`Payment verified successfully!\n${JSON.stringify(response.data, null, 2)}`);
    } catch (err: any) {
      console.error('Verification error:', err);
      setError(err.response?.data?.message || err.message || 'Verification failed');
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Razorpay Test</h1>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Razorpay Integration</h2>
        <p className="mb-4">Click the button below to create a test order and open the Razorpay payment form.</p>

        <button
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          onClick={handleTestPayment}
          disabled={loading}
        >
          {loading ? 'Processing...' : 'Test Razorpay Payment'}
        </button>

        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            <h3 className="font-semibold">Error:</h3>
            <p>{error}</p>
          </div>
        )}

        {result && (
          <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            <h3 className="font-semibold">Result:</h3>
            <pre className="whitespace-pre-wrap">{result}</pre>
          </div>
        )}
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
        <p><strong>API URL:</strong> {process.env.REACT_APP_API_URL}</p>
        <p><strong>Razorpay Key ID:</strong> {process.env.REACT_APP_RAZORPAY_KEY_ID}</p>
        <p><strong>Razorpay SDK Loaded:</strong> {window.Razorpay ? 'Yes' : 'No'}</p>
      </div>
    </div>
  );
};

export default RazorpayTest;
